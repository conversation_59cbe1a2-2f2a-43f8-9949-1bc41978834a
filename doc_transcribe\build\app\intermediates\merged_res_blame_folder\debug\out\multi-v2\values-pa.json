{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,268,334,409,475,574,670", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "121,186,263,329,404,470,569,665,750"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3260,3331,3396,3473,3539,3614,3680,3779,3875", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "3326,3391,3468,3534,3609,3675,3774,3870,3955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "667,765,867,970,1071,1173,1271,5066", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "760,862,965,1066,1168,1266,1395,5162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,487,667,756,843,926,1017,1111,1182,1245,1336,1427,1491,1554,1614,1682,1764,1847,1926,1996,2072,2143,2214,2306,2398,2464,2527,2580,2638,2686,2747,2807,2879,2941,3003,3064,3126,3191,3255,3321,3373,3433,3507,3581", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,81,82,78,69,75,70,70,91,91,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "279,482,662,751,838,921,1012,1106,1177,1240,1331,1422,1486,1549,1609,1677,1759,1842,1921,1991,2067,2138,2209,2301,2393,2459,2522,2575,2633,2681,2742,2802,2874,2936,2998,3059,3121,3186,3250,3316,3368,3428,3502,3576,3628"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,487,1400,1489,1576,1659,1750,1844,1915,1978,2069,2160,2224,2287,2347,2415,2497,2580,2659,2729,2805,2876,2947,3039,3131,3197,3960,4013,4071,4119,4180,4240,4312,4374,4436,4497,4559,4624,4688,4754,4806,4866,4940,5014", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,81,82,78,69,75,70,70,91,91,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "279,482,662,1484,1571,1654,1745,1839,1910,1973,2064,2155,2219,2282,2342,2410,2492,2575,2654,2724,2800,2871,2942,3034,3126,3192,3255,4008,4066,4114,4175,4235,4307,4369,4431,4492,4554,4619,4683,4749,4801,4861,4935,5009,5061"}}]}]}
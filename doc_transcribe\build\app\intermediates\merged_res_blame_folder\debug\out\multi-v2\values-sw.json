{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "706,800,902,999,1100,1207,1314,5107", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "795,897,994,1095,1202,1309,1424,5203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,177,239,306,384,465,552,634", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "113,172,234,301,379,460,547,629,699"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3315,3378,3437,3499,3566,3644,3725,3812,3894", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "3373,3432,3494,3561,3639,3720,3807,3889,3959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,289,506,706,785,862,947,1037,1125,1201,1267,1360,1455,1522,1586,1647,1722,1797,1877,1952,2026,2107,2180,2258,2352,2446,2514,2592,2645,2703,2751,2812,2873,2940,3004,3070,3133,3192,3258,3327,3393,3445,3511,3594,3677", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,74,79,74,73,80,72,77,93,93,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "284,501,701,780,857,942,1032,1120,1196,1262,1355,1450,1517,1581,1642,1717,1792,1872,1947,2021,2102,2175,2253,2347,2441,2509,2587,2640,2698,2746,2807,2868,2935,2999,3065,3128,3187,3253,3322,3388,3440,3506,3589,3672,3730"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,289,506,1429,1508,1585,1670,1760,1848,1924,1990,2083,2178,2245,2309,2370,2445,2520,2600,2675,2749,2830,2903,2981,3075,3169,3237,3964,4017,4075,4123,4184,4245,4312,4376,4442,4505,4564,4630,4699,4765,4817,4883,4966,5049", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,74,79,74,73,80,72,77,93,93,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "284,501,701,1503,1580,1665,1755,1843,1919,1985,2078,2173,2240,2304,2365,2440,2515,2595,2670,2744,2825,2898,2976,3070,3164,3232,3310,4012,4070,4118,4179,4240,4307,4371,4437,4500,4559,4625,4694,4760,4812,4878,4961,5044,5102"}}]}]}
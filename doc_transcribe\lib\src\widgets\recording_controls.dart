import 'package:flutter/material.dart';
import '../providers/recording_provider.dart';

class RecordingControls extends StatelessWidget {
  final RecordingState recordingState;
  final VoidCallback onStartRecording;
  final VoidCallback onStopRecording;
  final VoidCallback onPauseRecording;
  final VoidCallback onResumeRecording;

  const RecordingControls({
    super.key,
    required this.recordingState,
    required this.onStartRecording,
    required this.onStopRecording,
    required this.onPauseRecording,
    required this.onResumeRecording,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Pause/Resume button (only show when recording)
        if (recordingState == RecordingState.recording ||
            recordingState == RecordingState.paused) ...[
          _buildControlButton(
            context: context,
            icon: recordingState == RecordingState.recording
                ? Icons.pause
                : Icons.play_arrow,
            onPressed: recordingState == RecordingState.recording
                ? onPauseRecording
                : onResumeRecording,
            backgroundColor: Colors.orange,
            size: 56,
          ),
          const SizedBox(width: 24),
        ],

        // Main record/stop button
        _buildMainButton(context),

        // Stop button (only show when recording or paused)
        if (recordingState == RecordingState.recording ||
            recordingState == RecordingState.paused) ...[
          const SizedBox(width: 24),
          _buildControlButton(
            context: context,
            icon: Icons.stop,
            onPressed: onStopRecording,
            backgroundColor: Colors.blue,
            size: 56,
          ),
        ],
      ],
    );
  }

  Widget _buildMainButton(BuildContext context) {
    IconData icon;
    Color backgroundColor;
    VoidCallback? onPressed;
    double size = 80;

    switch (recordingState) {
      case RecordingState.idle:
      case RecordingState.stopped:
        icon = Icons.mic;
        backgroundColor = Colors.red;
        onPressed = onStartRecording;
        break;
      case RecordingState.recording:
        icon = Icons.mic;
        backgroundColor = Colors.red;
        onPressed = null; // Disable during recording
        break;
      case RecordingState.paused:
        icon = Icons.mic;
        backgroundColor = Colors.red.withOpacity(0.7);
        onPressed = null; // Disable during pause
        break;
      case RecordingState.processing:
        icon = Icons.hourglass_empty;
        backgroundColor = Colors.grey;
        onPressed = null;
        break;
      case RecordingState.error:
        icon = Icons.error;
        backgroundColor = Colors.red.withOpacity(0.5);
        onPressed = onStartRecording; // Allow retry
        break;
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: _buildControlButton(
        context: context,
        icon: icon,
        onPressed: onPressed,
        backgroundColor: backgroundColor,
        size: size,
        isMain: true,
      ),
    );
  }

  Widget _buildControlButton({
    required BuildContext context,
    required IconData icon,
    required VoidCallback? onPressed,
    required Color backgroundColor,
    required double size,
    bool isMain = false,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: backgroundColor,
        shape: const CircleBorder(),
        child: InkWell(
          onTap: onPressed,
          customBorder: const CircleBorder(),
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: isMain && recordingState == RecordingState.recording
                  ? Border.all(
                      color: Colors.white,
                      width: 3,
                    )
                  : null,
            ),
            child: Center(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: Icon(
                  icon,
                  key: ValueKey(icon),
                  color: Colors.white,
                  size: isMain ? 36 : 24,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

enum AudioFormat {
  wav,
  aac,
  opus,
}

class SimplifiedAudioService {
  static final SimplifiedAudioService _instance = SimplifiedAudioService._internal();
  static SimplifiedAudioService get instance => _instance;
  SimplifiedAudioService._internal();

  StreamController<Uint8List>? _audioStreamController;
  Timer? _simulationTimer;
  String? _currentRecordingPath;
  bool _isInitialized = false;
  bool _isRecording = false;
  bool _isPaused = false;

  // Audio configuration
  static const int sampleRate = 16000; // 16kHz for Riva
  static const int numChannels = 1; // Mono
  static const int bitRate = 16000; // 16-bit

  Stream<Uint8List>? get audioStream => _audioStreamController?.stream;
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;
  bool get isPaused => _isPaused;
  String? get currentRecordingPath => _currentRecordingPath;

  // Initialize audio service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request microphone permission
      await _requestPermissions();
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize audio service: $e');
    }
  }

  // Request necessary permissions
  Future<void> _requestPermissions() async {
    try {
      final microphoneStatus = await Permission.microphone.request();
      print('Microphone permission status: $microphoneStatus');

      if (Platform.isAndroid) {
        // Try to request storage permission, but don't fail if denied
        final storageStatus = await Permission.storage.request();
        print('Storage permission status: $storageStatus');

        // For demo purposes, we'll continue even if storage permission is denied
        // In a real app, you might want to show a dialog explaining why the permission is needed
        if (storageStatus == PermissionStatus.denied) {
          print('Storage permission denied, but continuing with demo functionality');
        }
      }

      // Only fail if microphone permission is absolutely denied
      if (microphoneStatus == PermissionStatus.permanentlyDenied) {
        throw Exception('Microphone permission is permanently denied. Please enable it in app settings.');
      }
    } catch (e) {
      print('Permission request error: $e');
      // For demo purposes, we'll continue even if there are permission issues
      print('Continuing with demo functionality despite permission issues');
    }
  }

  // Start recording (simulated for demo)
  Future<void> startRecording({
    String? consultationId,
    AudioFormat format = AudioFormat.wav,
    bool enableStreaming = true,
  }) async {
    if (!_isInitialized) {
      throw Exception('Audio service not initialized');
    }

    if (_isRecording) {
      throw Exception('Already recording');
    }

    try {
      // Generate file path
      _currentRecordingPath = await _generateRecordingPath(consultationId, format);

      // Setup audio streaming if enabled
      if (enableStreaming) {
        _audioStreamController = StreamController<Uint8List>.broadcast();
        _startAudioStreaming();
      }

      _isRecording = true;
      _isPaused = false;
    } catch (e) {
      await _cleanup();
      throw Exception('Failed to start recording: $e');
    }
  }

  // Stop recording
  Future<String?> stopRecording() async {
    if (!_isRecording) {
      throw Exception('Not currently recording');
    }

    try {
      _simulationTimer?.cancel();
      await _audioStreamController?.close();

      _isRecording = false;
      _isPaused = false;
      _simulationTimer = null;
      _audioStreamController = null;

      final recordingPath = _currentRecordingPath;
      _currentRecordingPath = null;

      return recordingPath;
    } catch (e) {
      throw Exception('Failed to stop recording: $e');
    }
  }

  // Pause recording
  Future<void> pauseRecording() async {
    if (!_isRecording || _isPaused) {
      throw Exception('Cannot pause recording in current state');
    }

    try {
      _simulationTimer?.cancel();
      _isPaused = true;
    } catch (e) {
      throw Exception('Failed to pause recording: $e');
    }
  }

  // Resume recording
  Future<void> resumeRecording() async {
    if (!_isRecording || !_isPaused) {
      throw Exception('Cannot resume recording in current state');
    }

    try {
      _startAudioStreaming();
      _isPaused = false;
    } catch (e) {
      throw Exception('Failed to resume recording: $e');
    }
  }

  // Get current recording level (0.0 to 1.0)
  double getCurrentRecordingLevel() {
    if (!_isRecording || _isPaused) return 0.0;
    
    // Simulate audio level
    return (DateTime.now().millisecondsSinceEpoch % 100) / 100.0;
  }

  // Private methods
  Future<String> _generateRecordingPath(String? consultationId, AudioFormat format) async {
    // Simplified path generation for demo
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = _getFileExtension(format);

    // Create audio directory if it doesn't exist
    final audioDir = Directory(path.join(directory.path, 'audio'));
    if (!await audioDir.exists()) {
      await audioDir.create(recursive: true);
    }

    if (consultationId != null) {
      return path.join(audioDir.path, '$consultationId.$extension');
    } else {
      return path.join(audioDir.path, 'recording_$timestamp.$extension');
    }
  }

  String _getFileExtension(AudioFormat format) {
    switch (format) {
      case AudioFormat.wav:
        return 'wav';
      case AudioFormat.aac:
        return 'aac';
      case AudioFormat.opus:
        return 'opus';
    }
  }

  void _startAudioStreaming() {
    // Simulate streaming audio data
    _simulationTimer = Timer.periodic(
      const Duration(milliseconds: 100),
      (timer) {
        if (_audioStreamController != null && !_audioStreamController!.isClosed) {
          // Generate simulated audio data chunks for streaming to Riva
          // In a real implementation, this would be actual audio data from the microphone
          final data = Uint8List.fromList(List.generate(1024, (i) => i % 256));
          _audioStreamController!.add(data);
        }
      },
    );
  }

  Future<void> _cleanup() async {
    _simulationTimer?.cancel();
    await _audioStreamController?.close();
    _simulationTimer = null;
    _audioStreamController = null;
    _currentRecordingPath = null;
    _isRecording = false;
    _isPaused = false;
  }

  // Cleanup and dispose
  Future<void> cleanup() async {
    await _cleanup();
    _isInitialized = false;
  }

  Future<void> dispose() async {
    await cleanup();
  }
}

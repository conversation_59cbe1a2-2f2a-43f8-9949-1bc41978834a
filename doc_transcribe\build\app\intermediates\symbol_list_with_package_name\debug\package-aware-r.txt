com.example.doc_transcribe
anim fragment_fast_out_extra_slow_in
animator fragment_close_enter
animator fragment_close_exit
animator fragment_fade_enter
animator fragment_fade_exit
animator fragment_open_enter
animator fragment_open_exit
array exo_controls_playback_speeds
attr activityAction
attr activityName
attr ad_marker_color
attr ad_marker_width
attr alpha
attr alwaysExpand
attr animationBackgroundColor
attr animation_enabled
attr auto_show
attr backgroundTint
attr bar_gravity
attr bar_height
attr buffered_color
attr clearTop
attr controller_layout_id
attr default_artwork
attr fastScrollEnabled
attr fastScrollHorizontalThumbDrawable
attr fastScrollHorizontalTrackDrawable
attr fastScrollVerticalThumbDrawable
attr fastScrollVerticalTrackDrawable
attr finishPrimaryWithPlaceholder
attr finishPrimaryWithSecondary
attr finishSecondaryWithPrimary
attr font
attr fontProviderAuthority
attr fontProviderCerts
attr fontProviderFetchStrategy
attr fontProviderFetchTimeout
attr fontProviderPackage
attr fontProviderQuery
attr fontProviderSystemFontFamily
attr fontStyle
attr fontVariationSettings
attr fontWeight
attr hide_during_ads
attr hide_on_touch
attr keep_content_on_player_reset
attr lStar
attr layoutManager
attr nestedScrollViewStyle
attr placeholderActivityName
attr played_ad_marker_color
attr played_color
attr player_layout_id
attr primaryActivityName
attr queryPatterns
attr recyclerViewStyle
attr repeat_toggle_modes
attr resize_mode
attr reverseLayout
attr scrubber_color
attr scrubber_disabled_size
attr scrubber_dragged_size
attr scrubber_drawable
attr scrubber_enabled_size
attr secondaryActivityAction
attr secondaryActivityName
attr shortcutMatchRequired
attr show_buffering
attr show_fastforward_button
attr show_next_button
attr show_previous_button
attr show_rewind_button
attr show_shuffle_button
attr show_subtitle_button
attr show_timeout
attr show_vr_button
attr shutter_background_color
attr spanCount
attr splitLayoutDirection
attr splitMaxAspectRatioInLandscape
attr splitMaxAspectRatioInPortrait
attr splitMinHeightDp
attr splitMinSmallestWidthDp
attr splitMinWidthDp
attr splitRatio
attr stackFromEnd
attr stickyPlaceholder
attr surface_type
attr tag
attr time_bar_min_update_interval
attr touch_target_height
attr ttcIndex
attr unplayed_color
attr use_artwork
attr use_controller
color androidx_core_ripple_material_light
color androidx_core_secondary_text_default_material_light
color call_notification_answer_color
color call_notification_decline_color
color exo_black_opacity_60
color exo_black_opacity_70
color exo_bottom_bar_background
color exo_edit_mode_background_color
color exo_error_message_background_color
color exo_styled_error_message_background
color exo_white
color exo_white_opacity_70
color notification_action_color_filter
color notification_icon_bg_color
color notification_material_background_media_default_color
color primary_text_default_material_dark
color secondary_text_default_material_dark
dimen compat_button_inset_horizontal_material
dimen compat_button_inset_vertical_material
dimen compat_button_padding_horizontal_material
dimen compat_button_padding_vertical_material
dimen compat_control_corner_material
dimen compat_notification_large_icon_max_height
dimen compat_notification_large_icon_max_width
dimen exo_error_message_height
dimen exo_error_message_margin_bottom
dimen exo_error_message_text_padding_horizontal
dimen exo_error_message_text_padding_vertical
dimen exo_error_message_text_size
dimen exo_icon_horizontal_margin
dimen exo_icon_padding
dimen exo_icon_padding_bottom
dimen exo_icon_size
dimen exo_icon_text_size
dimen exo_media_button_height
dimen exo_media_button_width
dimen exo_setting_width
dimen exo_settings_height
dimen exo_settings_icon_size
dimen exo_settings_main_text_size
dimen exo_settings_offset
dimen exo_settings_sub_text_size
dimen exo_settings_text_height
dimen exo_small_icon_height
dimen exo_small_icon_horizontal_margin
dimen exo_small_icon_padding_horizontal
dimen exo_small_icon_padding_vertical
dimen exo_small_icon_width
dimen exo_styled_bottom_bar_height
dimen exo_styled_bottom_bar_margin_top
dimen exo_styled_bottom_bar_time_padding
dimen exo_styled_controls_padding
dimen exo_styled_minimal_controls_margin_bottom
dimen exo_styled_progress_bar_height
dimen exo_styled_progress_dragged_thumb_size
dimen exo_styled_progress_enabled_thumb_size
dimen exo_styled_progress_layout_height
dimen exo_styled_progress_margin_bottom
dimen exo_styled_progress_touch_target_height
dimen fastscroll_default_thickness
dimen fastscroll_margin
dimen fastscroll_minimum_range
dimen item_touch_helper_max_drag_scroll_per_frame
dimen item_touch_helper_swipe_escape_max_velocity
dimen item_touch_helper_swipe_escape_velocity
dimen notification_action_icon_size
dimen notification_action_text_size
dimen notification_big_circle_margin
dimen notification_content_margin_start
dimen notification_large_icon_height
dimen notification_large_icon_width
dimen notification_main_column_padding_top
dimen notification_media_narrow_margin
dimen notification_right_icon_size
dimen notification_right_side_padding_top
dimen notification_small_icon_background_padding
dimen notification_small_icon_size_as_large
dimen notification_subtext_size
dimen notification_top_pad
dimen notification_top_pad_large_text
drawable exo_controls_fastforward
drawable exo_controls_fullscreen_enter
drawable exo_controls_fullscreen_exit
drawable exo_controls_next
drawable exo_controls_pause
drawable exo_controls_play
drawable exo_controls_previous
drawable exo_controls_repeat_all
drawable exo_controls_repeat_off
drawable exo_controls_repeat_one
drawable exo_controls_rewind
drawable exo_controls_shuffle_off
drawable exo_controls_shuffle_on
drawable exo_controls_vr
drawable exo_edit_mode_logo
drawable exo_ic_audiotrack
drawable exo_ic_check
drawable exo_ic_chevron_left
drawable exo_ic_chevron_right
drawable exo_ic_default_album_image
drawable exo_ic_forward
drawable exo_ic_fullscreen_enter
drawable exo_ic_fullscreen_exit
drawable exo_ic_pause_circle_filled
drawable exo_ic_play_circle_filled
drawable exo_ic_rewind
drawable exo_ic_settings
drawable exo_ic_skip_next
drawable exo_ic_skip_previous
drawable exo_ic_speed
drawable exo_ic_subtitle_off
drawable exo_ic_subtitle_on
drawable exo_icon_circular_play
drawable exo_icon_fastforward
drawable exo_icon_fullscreen_enter
drawable exo_icon_fullscreen_exit
drawable exo_icon_next
drawable exo_icon_pause
drawable exo_icon_play
drawable exo_icon_previous
drawable exo_icon_repeat_all
drawable exo_icon_repeat_off
drawable exo_icon_repeat_one
drawable exo_icon_rewind
drawable exo_icon_shuffle_off
drawable exo_icon_shuffle_on
drawable exo_icon_stop
drawable exo_icon_vr
drawable exo_notification_fastforward
drawable exo_notification_next
drawable exo_notification_pause
drawable exo_notification_play
drawable exo_notification_previous
drawable exo_notification_rewind
drawable exo_notification_small_icon
drawable exo_notification_stop
drawable exo_rounded_rectangle
drawable exo_styled_controls_audiotrack
drawable exo_styled_controls_check
drawable exo_styled_controls_fastforward
drawable exo_styled_controls_fullscreen_enter
drawable exo_styled_controls_fullscreen_exit
drawable exo_styled_controls_next
drawable exo_styled_controls_overflow_hide
drawable exo_styled_controls_overflow_show
drawable exo_styled_controls_pause
drawable exo_styled_controls_play
drawable exo_styled_controls_previous
drawable exo_styled_controls_repeat_all
drawable exo_styled_controls_repeat_off
drawable exo_styled_controls_repeat_one
drawable exo_styled_controls_rewind
drawable exo_styled_controls_settings
drawable exo_styled_controls_shuffle_off
drawable exo_styled_controls_shuffle_on
drawable exo_styled_controls_speed
drawable exo_styled_controls_subtitle_off
drawable exo_styled_controls_subtitle_on
drawable exo_styled_controls_vr
drawable ic_call_answer
drawable ic_call_answer_low
drawable ic_call_answer_video
drawable ic_call_answer_video_low
drawable ic_call_decline
drawable ic_call_decline_low
drawable launch_background
drawable notification_action_background
drawable notification_bg
drawable notification_bg_low
drawable notification_bg_low_normal
drawable notification_bg_low_pressed
drawable notification_bg_normal
drawable notification_bg_normal_pressed
drawable notification_icon_background
drawable notification_oversize_large_icon_bg
drawable notification_template_icon_bg
drawable notification_template_icon_low_bg
drawable notification_tile_bg
drawable notify_panel_notification_icon_bg
font roboto_medium_numbers
id accessibility_action_clickable_span
id accessibility_custom_action_0
id accessibility_custom_action_1
id accessibility_custom_action_10
id accessibility_custom_action_11
id accessibility_custom_action_12
id accessibility_custom_action_13
id accessibility_custom_action_14
id accessibility_custom_action_15
id accessibility_custom_action_16
id accessibility_custom_action_17
id accessibility_custom_action_18
id accessibility_custom_action_19
id accessibility_custom_action_2
id accessibility_custom_action_20
id accessibility_custom_action_21
id accessibility_custom_action_22
id accessibility_custom_action_23
id accessibility_custom_action_24
id accessibility_custom_action_25
id accessibility_custom_action_26
id accessibility_custom_action_27
id accessibility_custom_action_28
id accessibility_custom_action_29
id accessibility_custom_action_3
id accessibility_custom_action_30
id accessibility_custom_action_31
id accessibility_custom_action_4
id accessibility_custom_action_5
id accessibility_custom_action_6
id accessibility_custom_action_7
id accessibility_custom_action_8
id accessibility_custom_action_9
id action0
id action_container
id action_divider
id action_image
id action_text
id actions
id adjacent
id all
id always
id alwaysAllow
id alwaysDisallow
id androidx_window_activity_scope
id async
id blocking
id bottom
id bottomToTop
id cancel_action
id center
id chronometer
id dialog_button
id edit_text_id
id end_padder
id exo_ad_overlay
id exo_artwork
id exo_audio_track
id exo_basic_controls
id exo_bottom_bar
id exo_buffering
id exo_center_controls
id exo_check
id exo_content_frame
id exo_controller
id exo_controller_placeholder
id exo_controls_background
id exo_duration
id exo_error_message
id exo_extra_controls
id exo_extra_controls_scroll_view
id exo_ffwd
id exo_ffwd_with_amount
id exo_fullscreen
id exo_icon
id exo_main_text
id exo_minimal_controls
id exo_minimal_fullscreen
id exo_next
id exo_overflow_hide
id exo_overflow_show
id exo_overlay
id exo_pause
id exo_play
id exo_play_pause
id exo_playback_speed
id exo_position
id exo_prev
id exo_progress
id exo_progress_placeholder
id exo_repeat_toggle
id exo_rew
id exo_rew_with_amount
id exo_settings
id exo_settings_listview
id exo_shuffle
id exo_shutter
id exo_sub_text
id exo_subtitle
id exo_subtitles
id exo_text
id exo_time
id exo_track_selection_view
id exo_vr
id fill
id fit
id fixed_height
id fixed_width
id forever
id fragment_container_view_tag
id hide_ime_id
id icon
id icon_group
id info
id italic
id item_touch_helper_previous_elevation
id line1
id line3
id locale
id ltr
id media_actions
id media_controller_compat_view_tag
id never
id none
id normal
id notification_background
id notification_main_column
id notification_main_column_container
id one
id report_drawn
id right_icon
id right_side
id rtl
id special_effects_controller_view_tag
id spherical_gl_surface_view
id status_bar_latest_event_content
id surface_view
id tag_accessibility_actions
id tag_accessibility_clickable_spans
id tag_accessibility_heading
id tag_accessibility_pane_title
id tag_on_apply_window_listener
id tag_on_receive_content_listener
id tag_on_receive_content_mime_types
id tag_screen_reader_focusable
id tag_state_description
id tag_transition_group
id tag_unhandled_key_event_manager
id tag_unhandled_key_listeners
id tag_window_insets_animation_callback
id text
id text2
id texture_view
id time
id title
id topToBottom
id video_decoder_gl_surface_view
id view_tree_lifecycle_owner
id view_tree_on_back_pressed_dispatcher_owner
id view_tree_saved_state_registry_owner
id view_tree_view_model_store_owner
id visible_removing_fragment_view_tag
id when_playing
id zoom
integer cancel_button_image_alpha
integer exo_media_button_opacity_percentage_disabled
integer exo_media_button_opacity_percentage_enabled
integer status_bar_notification_info_maxnum
layout custom_dialog
layout exo_list_divider
layout exo_player_control_view
layout exo_player_view
layout exo_styled_player_control_ffwd_button
layout exo_styled_player_control_rewind_button
layout exo_styled_player_control_view
layout exo_styled_player_view
layout exo_styled_settings_list
layout exo_styled_settings_list_item
layout exo_styled_sub_settings_list_item
layout exo_track_selection_dialog
layout ime_base_split_test_activity
layout ime_secondary_split_test_activity
layout notification_action
layout notification_action_tombstone
layout notification_media_action
layout notification_media_cancel_action
layout notification_template_big_media
layout notification_template_big_media_custom
layout notification_template_big_media_narrow
layout notification_template_big_media_narrow_custom
layout notification_template_custom_big
layout notification_template_icon_group
layout notification_template_lines_media
layout notification_template_media
layout notification_template_media_custom
layout notification_template_part_chronometer
layout notification_template_part_time
mipmap ic_launcher
plurals exo_controls_fastforward_by_amount_description
plurals exo_controls_rewind_by_amount_description
string androidx_startup
string call_notification_answer_action
string call_notification_answer_video_action
string call_notification_decline_action
string call_notification_hang_up_action
string call_notification_incoming_text
string call_notification_ongoing_text
string call_notification_screening_text
string exo_controls_cc_disabled_description
string exo_controls_cc_enabled_description
string exo_controls_custom_playback_speed
string exo_controls_fastforward_description
string exo_controls_fullscreen_enter_description
string exo_controls_fullscreen_exit_description
string exo_controls_hide
string exo_controls_next_description
string exo_controls_overflow_hide_description
string exo_controls_overflow_show_description
string exo_controls_pause_description
string exo_controls_play_description
string exo_controls_playback_speed
string exo_controls_previous_description
string exo_controls_repeat_all_description
string exo_controls_repeat_off_description
string exo_controls_repeat_one_description
string exo_controls_rewind_description
string exo_controls_seek_bar_description
string exo_controls_settings_description
string exo_controls_show
string exo_controls_shuffle_off_description
string exo_controls_shuffle_on_description
string exo_controls_stop_description
string exo_controls_time_placeholder
string exo_controls_vr_description
string exo_download_completed
string exo_download_description
string exo_download_downloading
string exo_download_failed
string exo_download_notification_channel_name
string exo_download_paused
string exo_download_paused_for_network
string exo_download_paused_for_wifi
string exo_download_removing
string exo_item_list
string exo_track_bitrate
string exo_track_mono
string exo_track_resolution
string exo_track_role_alternate
string exo_track_role_closed_captions
string exo_track_role_commentary
string exo_track_role_supplementary
string exo_track_selection_auto
string exo_track_selection_none
string exo_track_selection_title_audio
string exo_track_selection_title_text
string exo_track_selection_title_video
string exo_track_stereo
string exo_track_surround
string exo_track_surround_5_point_1
string exo_track_surround_7_point_1
string exo_track_unknown
string status_bar_notification_info_overflow
style ExoMediaButton
style ExoMediaButton_FastForward
style ExoMediaButton_Next
style ExoMediaButton_Pause
style ExoMediaButton_Play
style ExoMediaButton_Previous
style ExoMediaButton_Rewind
style ExoMediaButton_VR
style ExoStyledControls
style ExoStyledControls_Button
style ExoStyledControls_Button_Bottom
style ExoStyledControls_Button_Bottom_AudioTrack
style ExoStyledControls_Button_Bottom_CC
style ExoStyledControls_Button_Bottom_FullScreen
style ExoStyledControls_Button_Bottom_OverflowHide
style ExoStyledControls_Button_Bottom_OverflowShow
style ExoStyledControls_Button_Bottom_PlaybackSpeed
style ExoStyledControls_Button_Bottom_RepeatToggle
style ExoStyledControls_Button_Bottom_Settings
style ExoStyledControls_Button_Bottom_Shuffle
style ExoStyledControls_Button_Bottom_VR
style ExoStyledControls_Button_Center
style ExoStyledControls_Button_Center_FfwdWithAmount
style ExoStyledControls_Button_Center_Next
style ExoStyledControls_Button_Center_PlayPause
style ExoStyledControls_Button_Center_Previous
style ExoStyledControls_Button_Center_RewWithAmount
style ExoStyledControls_TimeBar
style ExoStyledControls_TimeText
style ExoStyledControls_TimeText_Duration
style ExoStyledControls_TimeText_Position
style ExoStyledControls_TimeText_Separator
style LaunchTheme
style NormalTheme
style TextAppearance_Compat_Notification
style TextAppearance_Compat_Notification_Info
style TextAppearance_Compat_Notification_Info_Media
style TextAppearance_Compat_Notification_Line2
style TextAppearance_Compat_Notification_Line2_Media
style TextAppearance_Compat_Notification_Media
style TextAppearance_Compat_Notification_Time
style TextAppearance_Compat_Notification_Time_Media
style TextAppearance_Compat_Notification_Title
style TextAppearance_Compat_Notification_Title_Media
style Widget_Compat_NotificationActionContainer
style Widget_Compat_NotificationActionText
styleable ActivityFilter activityAction activityName
styleable ActivityRule alwaysExpand tag
styleable AspectRatioFrameLayout resize_mode
styleable Capability queryPatterns shortcutMatchRequired
styleable ColorStateListItem android_color android_alpha android_lStar alpha lStar
styleable DefaultTimeBar ad_marker_color ad_marker_width bar_gravity bar_height buffered_color played_ad_marker_color played_color scrubber_color scrubber_disabled_size scrubber_dragged_size scrubber_drawable scrubber_enabled_size touch_target_height unplayed_color
styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
styleable FontFamilyFont android_font android_fontWeight android_fontStyle android_ttcIndex android_fontVariationSettings font fontStyle fontVariationSettings fontWeight ttcIndex
styleable Fragment android_name android_id android_tag
styleable FragmentContainerView android_name android_tag
styleable GradientColor android_startColor android_endColor android_type android_centerX android_centerY android_gradientRadius android_tileMode android_centerColor android_startX android_startY android_endX android_endY
styleable GradientColorItem android_color android_offset
styleable PlayerControlView ad_marker_color ad_marker_width bar_gravity bar_height buffered_color controller_layout_id played_ad_marker_color played_color repeat_toggle_modes scrubber_color scrubber_disabled_size scrubber_dragged_size scrubber_drawable scrubber_enabled_size show_fastforward_button show_next_button show_previous_button show_rewind_button show_shuffle_button show_timeout time_bar_min_update_interval touch_target_height unplayed_color
styleable PlayerView ad_marker_color ad_marker_width auto_show bar_height buffered_color controller_layout_id default_artwork hide_during_ads hide_on_touch keep_content_on_player_reset played_ad_marker_color played_color player_layout_id repeat_toggle_modes resize_mode scrubber_color scrubber_disabled_size scrubber_dragged_size scrubber_drawable scrubber_enabled_size show_buffering show_shuffle_button show_timeout shutter_background_color surface_type time_bar_min_update_interval touch_target_height unplayed_color use_artwork use_controller
styleable RecyclerView android_orientation android_clipToPadding android_descendantFocusability fastScrollEnabled fastScrollHorizontalThumbDrawable fastScrollHorizontalTrackDrawable fastScrollVerticalThumbDrawable fastScrollVerticalTrackDrawable layoutManager reverseLayout spanCount stackFromEnd
styleable SplitPairFilter primaryActivityName secondaryActivityAction secondaryActivityName
styleable SplitPairRule animationBackgroundColor clearTop finishPrimaryWithSecondary finishSecondaryWithPrimary splitLayoutDirection splitMaxAspectRatioInLandscape splitMaxAspectRatioInPortrait splitMinHeightDp splitMinSmallestWidthDp splitMinWidthDp splitRatio tag
styleable SplitPlaceholderRule animationBackgroundColor finishPrimaryWithPlaceholder placeholderActivityName splitLayoutDirection splitMaxAspectRatioInLandscape splitMaxAspectRatioInPortrait splitMinHeightDp splitMinSmallestWidthDp splitMinWidthDp splitRatio stickyPlaceholder tag
styleable StyledPlayerControlView ad_marker_color ad_marker_width animation_enabled bar_gravity bar_height buffered_color controller_layout_id played_ad_marker_color played_color repeat_toggle_modes scrubber_color scrubber_disabled_size scrubber_dragged_size scrubber_drawable scrubber_enabled_size show_fastforward_button show_next_button show_previous_button show_rewind_button show_shuffle_button show_subtitle_button show_timeout show_vr_button time_bar_min_update_interval touch_target_height unplayed_color
styleable StyledPlayerView ad_marker_color ad_marker_width animation_enabled auto_show bar_gravity bar_height buffered_color controller_layout_id default_artwork hide_during_ads hide_on_touch keep_content_on_player_reset played_ad_marker_color played_color player_layout_id repeat_toggle_modes resize_mode scrubber_color scrubber_disabled_size scrubber_dragged_size scrubber_drawable scrubber_enabled_size show_buffering show_shuffle_button show_subtitle_button show_timeout show_vr_button shutter_background_color surface_type time_bar_min_update_interval touch_target_height unplayed_color use_artwork use_controller
xml file_paths
xml flutter_printing_file_paths
xml flutter_share_file_paths

{"logs": [{"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-zu_values-zu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "726,824,928,1027,1130,1236,1343,5202", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "819,923,1022,1125,1231,1338,1451,5298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,522,726,823,914,998,1092,1187,1259,1330,1429,1529,1596,1660,1726,1806,1883,1964,2044,2119,2211,2285,2358,2441,2523,2586,2655,2708,2766,2818,2879,2939,3001,3066,3134,3204,3263,3331,3398,3466,3520,3588,3675,3762", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,76,80,79,74,91,73,72,82,81,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "288,517,721,818,909,993,1087,1182,1254,1325,1424,1524,1591,1655,1721,1801,1878,1959,2039,2114,2206,2280,2353,2436,2518,2581,2650,2703,2761,2813,2874,2934,2996,3061,3129,3199,3258,3326,3393,3461,3515,3583,3670,3757,3812"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,522,1456,1553,1644,1728,1822,1917,1989,2060,2159,2259,2326,2390,2456,2536,2613,2694,2774,2849,2941,3015,3088,3171,3253,3316,4040,4093,4151,4203,4264,4324,4386,4451,4519,4589,4648,4716,4783,4851,4905,4973,5060,5147", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,76,80,79,74,91,73,72,82,81,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "288,517,721,1548,1639,1723,1817,1912,1984,2055,2154,2254,2321,2385,2451,2531,2608,2689,2769,2844,2936,3010,3083,3166,3248,3311,3380,4088,4146,4198,4259,4319,4381,4446,4514,4584,4643,4711,4778,4846,4900,4968,5055,5142,5197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,183,245,314,391,471,560,641", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "119,178,240,309,386,466,555,636,705"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3385,3454,3513,3575,3644,3721,3801,3890,3971", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "3449,3508,3570,3639,3716,3796,3885,3966,4035"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-pl_values-pl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,622,944,1022,1100,1183,1272,1361,1444,1511,1605,1699,1768,1834,1899,1971,2053,2131,2209,2285,2366,2439,2522,2613,2702,2770,2834,2887,2945,2993,3054,3127,3193,3257,3334,3401,3459,3526,3591,3656,3708,3775,3866,3957", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,81,77,77,75,80,72,82,90,88,67,63,52,57,47,60,72,65,63,76,66,57,66,64,64,51,66,90,90,54", "endOffsets": "282,617,939,1017,1095,1178,1267,1356,1439,1506,1600,1694,1763,1829,1894,1966,2048,2126,2204,2280,2361,2434,2517,2608,2697,2765,2829,2882,2940,2988,3049,3122,3188,3252,3329,3396,3454,3521,3586,3651,3703,3770,3861,3952,4007"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,622,1681,1759,1837,1920,2009,2098,2181,2248,2342,2436,2505,2571,2636,2708,2790,2868,2946,3022,3103,3176,3259,3350,3439,3507,4233,4286,4344,4392,4453,4526,4592,4656,4733,4800,4858,4925,4990,5055,5107,5174,5265,5356", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,81,77,77,75,80,72,82,90,88,67,63,52,57,47,60,72,65,63,76,66,57,66,64,64,51,66,90,90,54", "endOffsets": "282,617,939,1754,1832,1915,2004,2093,2176,2243,2337,2431,2500,2566,2631,2703,2785,2863,2941,3017,3098,3171,3254,3345,3434,3502,3566,4281,4339,4387,4448,4521,4587,4651,4728,4795,4853,4920,4985,5050,5102,5169,5260,5351,5406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,189,251,320,398,468,561,652", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "123,184,246,315,393,463,556,647,712"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3571,3644,3705,3767,3836,3914,3984,4077,4168", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "3639,3700,3762,3831,3909,3979,4072,4163,4228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "944,1041,1143,1241,1340,1454,1559,5411", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "1036,1138,1236,1335,1449,1554,1676,5507"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-lt_values-lt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,298,622,933,1017,1100,1178,1275,1372,1446,1510,1606,1702,1773,1838,1901,1974,2052,2132,2210,2282,2358,2431,2505,2589,2671,2740,2807,2860,2918,2973,3034,3100,3169,3234,3302,3366,3424,3497,3564,3638,3697,3760,3837,3914", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,77,79,77,71,75,72,73,83,81,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "293,617,928,1012,1095,1173,1270,1367,1441,1505,1601,1697,1768,1833,1896,1969,2047,2127,2205,2277,2353,2426,2500,2584,2666,2735,2802,2855,2913,2968,3029,3095,3164,3229,3297,3361,3419,3492,3559,3633,3692,3755,3832,3909,3965"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,298,622,1684,1768,1851,1929,2026,2123,2197,2261,2357,2453,2524,2589,2652,2725,2803,2883,2961,3033,3109,3182,3256,3340,3422,3491,4260,4313,4371,4426,4487,4553,4622,4687,4755,4819,4877,4950,5017,5091,5150,5213,5290,5367", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,77,79,77,71,75,72,73,83,81,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "293,617,928,1763,1846,1924,2021,2118,2192,2256,2352,2448,2519,2584,2647,2720,2798,2878,2956,3028,3104,3177,3251,3335,3417,3486,3553,4308,4366,4421,4482,4548,4617,4682,4750,4814,4872,4945,5012,5086,5145,5208,5285,5362,5418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "933,1031,1141,1240,1343,1454,1564,5423", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "1026,1136,1235,1338,1449,1559,1679,5519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,267,335,416,490,587,682", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "131,195,262,330,411,485,582,677,752"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3558,3639,3703,3770,3838,3919,3993,4090,4185", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "3634,3698,3765,3833,3914,3988,4085,4180,4255"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-am_values-am.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,175,237,297,369,432,521,602", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "112,170,232,292,364,427,516,597,663"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3080,3142,3200,3262,3322,3394,3457,3546,3627", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "3137,3195,3257,3317,3389,3452,3541,3622,3688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,664,744,825,901,986,1067,1133,1195,1279,1362,1429,1492,1553,1619,1692,1766,1838,1907,1983,2051,2117,2192,2266,2328,2393,2446,2503,2549,2610,2668,2743,2802,2864,2923,2980,3044,3107,3171,3221,3277,3347,3417", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,72,73,71,68,75,67,65,74,73,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "278,477,659,739,820,896,981,1062,1128,1190,1274,1357,1424,1487,1548,1614,1687,1761,1833,1902,1978,2046,2112,2187,2261,2323,2388,2441,2498,2544,2605,2663,2738,2797,2859,2918,2975,3039,3102,3166,3216,3272,3342,3412,3464"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,1351,1431,1512,1588,1673,1754,1820,1882,1966,2049,2116,2179,2240,2306,2379,2453,2525,2594,2670,2738,2804,2879,2953,3015,3693,3746,3803,3849,3910,3968,4043,4102,4164,4223,4280,4344,4407,4471,4521,4577,4647,4717", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,72,73,71,68,75,67,65,74,73,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "278,477,659,1426,1507,1583,1668,1749,1815,1877,1961,2044,2111,2174,2235,2301,2374,2448,2520,2589,2665,2733,2799,2874,2948,3010,3075,3741,3798,3844,3905,3963,4038,4097,4159,4218,4275,4339,4402,4466,4516,4572,4642,4712,4764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "664,757,857,954,1053,1149,1251,4769", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "752,852,949,1048,1144,1246,1346,4865"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-vi_values-vi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "647,744,846,945,1045,1148,1261,5090", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "739,841,940,1040,1143,1256,1372,5186"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,473,647,723,798,870,973,1074,1153,1221,1320,1421,1489,1552,1615,1683,1762,1840,1916,1984,2062,2132,2217,2307,2398,2461,2535,2588,2649,2699,2760,2822,2888,2952,3017,3078,3137,3206,3273,3339,3397,3457,3531,3605", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,78,77,75,67,77,69,84,89,90,62,73,52,60,49,60,61,65,63,64,60,58,68,66,65,57,59,73,73,62", "endOffsets": "285,468,642,718,793,865,968,1069,1148,1216,1315,1416,1484,1547,1610,1678,1757,1835,1911,1979,2057,2127,2212,2302,2393,2456,2530,2583,2644,2694,2755,2817,2883,2947,3012,3073,3132,3201,3268,3334,3392,3452,3526,3600,3663"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,473,1377,1453,1528,1600,1703,1804,1883,1951,2050,2151,2219,2282,2345,2413,2492,2570,2646,2714,2792,2862,2947,3037,3128,3191,3957,4010,4071,4121,4182,4244,4310,4374,4439,4500,4559,4628,4695,4761,4819,4879,4953,5027", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,78,77,75,67,77,69,84,89,90,62,73,52,60,49,60,61,65,63,64,60,58,68,66,65,57,59,73,73,62", "endOffsets": "285,468,642,1448,1523,1595,1698,1799,1878,1946,2045,2146,2214,2277,2340,2408,2487,2565,2641,2709,2787,2857,2942,3032,3123,3186,3260,4005,4066,4116,4177,4239,4305,4369,4434,4495,4554,4623,4690,4756,4814,4874,4948,5022,5085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,259,328,419,489,579,667", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "123,186,254,323,414,484,574,662,742"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3265,3338,3401,3469,3538,3629,3699,3789,3877", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "3333,3396,3464,3533,3624,3694,3784,3872,3952"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-km_values-km.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,185,251,320,393,464,562,657", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "121,180,246,315,388,459,557,652,721"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3198,3269,3328,3394,3463,3536,3607,3705,3800", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "3264,3323,3389,3458,3531,3602,3700,3795,3864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "654,749,852,950,1050,1151,1263,5003", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "744,847,945,1045,1146,1258,1370,5099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,470,654,731,807,886,970,1058,1139,1205,1292,1381,1446,1508,1570,1636,1719,1798,1879,1951,2036,2107,2190,2270,2349,2411,2477,2530,2588,2638,2699,2758,2826,2896,2965,3032,3091,3157,3222,3289,3344,3401,3478,3555", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,82,78,80,71,84,70,82,79,78,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "280,465,649,726,802,881,965,1053,1134,1200,1287,1376,1441,1503,1565,1631,1714,1793,1874,1946,2031,2102,2185,2265,2344,2406,2472,2525,2583,2633,2694,2753,2821,2891,2960,3027,3086,3152,3217,3284,3339,3396,3473,3550,3606"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,470,1375,1452,1528,1607,1691,1779,1860,1926,2013,2102,2167,2229,2291,2357,2440,2519,2600,2672,2757,2828,2911,2991,3070,3132,3869,3922,3980,4030,4091,4150,4218,4288,4357,4424,4483,4549,4614,4681,4736,4793,4870,4947", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,82,78,80,71,84,70,82,79,78,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "280,465,649,1447,1523,1602,1686,1774,1855,1921,2008,2097,2162,2224,2286,2352,2435,2514,2595,2667,2752,2823,2906,2986,3065,3127,3193,3917,3975,4025,4086,4145,4213,4283,4352,4419,4478,4544,4609,4676,4731,4788,4865,4942,4998"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-gl_values-gl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,192,257,336,413,489,588,684", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "124,187,252,331,408,484,583,679,748"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3349,3423,3486,3551,3630,3707,3783,3882,3978", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "3418,3481,3546,3625,3702,3778,3877,3973,4042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "682,781,883,983,1081,1188,1294,5200", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "776,878,978,1076,1183,1289,1405,5296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,682,768,856,935,1033,1128,1205,1272,1372,1472,1538,1607,1674,1745,1833,1909,1992,2063,2149,2225,2302,2398,2493,2557,2621,2674,2732,2780,2841,2906,2976,3042,3114,3184,3252,3318,3383,3449,3502,3564,3640,3716", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,87,75,82,70,85,75,76,95,94,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "286,494,677,763,851,930,1028,1123,1200,1267,1367,1467,1533,1602,1669,1740,1828,1904,1987,2058,2144,2220,2297,2393,2488,2552,2616,2669,2727,2775,2836,2901,2971,3037,3109,3179,3247,3313,3378,3444,3497,3559,3635,3711,3769"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,1410,1496,1584,1663,1761,1856,1933,2000,2100,2200,2266,2335,2402,2473,2561,2637,2720,2791,2877,2953,3030,3126,3221,3285,4047,4100,4158,4206,4267,4332,4402,4468,4540,4610,4678,4744,4809,4875,4928,4990,5066,5142", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,87,75,82,70,85,75,76,95,94,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "286,494,677,1491,1579,1658,1756,1851,1928,1995,2095,2195,2261,2330,2397,2468,2556,2632,2715,2786,2872,2948,3025,3121,3216,3280,3344,4095,4153,4201,4262,4327,4397,4463,4535,4605,4673,4739,4804,4870,4923,4985,5061,5137,5195"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-gu_values-gu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "677,771,874,971,1073,1175,1273,5045", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "766,869,966,1068,1170,1268,1390,5141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,489,677,759,839,922,1021,1123,1200,1262,1351,1439,1503,1567,1627,1694,1774,1858,1935,2008,2086,2155,2231,2307,2383,2446,2509,2562,2620,2668,2729,2791,2853,2918,2980,3047,3110,3176,3243,3310,3363,3425,3501,3577", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,79,83,76,72,77,68,75,75,75,62,62,52,57,47,60,61,61,64,61,66,62,65,66,66,52,61,75,75,53", "endOffsets": "281,484,672,754,834,917,1016,1118,1195,1257,1346,1434,1498,1562,1622,1689,1769,1853,1930,2003,2081,2150,2226,2302,2378,2441,2504,2557,2615,2663,2724,2786,2848,2913,2975,3042,3105,3171,3238,3305,3358,3420,3496,3572,3626"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,489,1395,1477,1557,1640,1739,1841,1918,1980,2069,2157,2221,2285,2345,2412,2492,2576,2653,2726,2804,2873,2949,3025,3101,3164,3923,3976,4034,4082,4143,4205,4267,4332,4394,4461,4524,4590,4657,4724,4777,4839,4915,4991", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,79,83,76,72,77,68,75,75,75,62,62,52,57,47,60,61,61,64,61,66,62,65,66,66,52,61,75,75,53", "endOffsets": "281,484,672,1472,1552,1635,1734,1836,1913,1975,2064,2152,2216,2280,2340,2407,2487,2571,2648,2721,2799,2868,2944,3020,3096,3159,3222,3971,4029,4077,4138,4200,4262,4327,4389,4456,4519,4585,4652,4719,4772,4834,4910,4986,5040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,266,334,409,477,576,672", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "120,185,261,329,404,472,571,667,746"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3227,3297,3362,3438,3506,3581,3649,3748,3844", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "3292,3357,3433,3501,3576,3644,3743,3839,3918"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-sq_values-sq.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,254,320,398,477,569,655", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "121,182,249,315,393,472,564,650,720"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3267,3338,3399,3466,3532,3610,3689,3781,3867", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "3333,3394,3461,3527,3605,3684,3776,3862,3932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "671,770,872,970,1067,1175,1286,5068", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "765,867,965,1062,1170,1281,1403,5164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,485,671,752,833,919,1023,1115,1188,1251,1341,1431,1496,1559,1626,1694,1777,1860,1937,2004,2086,2158,2231,2315,2396,2460,2530,2583,2641,2689,2750,2815,2881,2943,3011,3075,3134,3200,3265,3331,3383,3448,3526,3604", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,82,82,76,66,81,71,72,83,80,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "281,480,666,747,828,914,1018,1110,1183,1246,1336,1426,1491,1554,1621,1689,1772,1855,1932,1999,2081,2153,2226,2310,2391,2455,2525,2578,2636,2684,2745,2810,2876,2938,3006,3070,3129,3195,3260,3326,3378,3443,3521,3599,3656"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,485,1408,1489,1570,1656,1760,1852,1925,1988,2078,2168,2233,2296,2363,2431,2514,2597,2674,2741,2823,2895,2968,3052,3133,3197,3937,3990,4048,4096,4157,4222,4288,4350,4418,4482,4541,4607,4672,4738,4790,4855,4933,5011", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,82,82,76,66,81,71,72,83,80,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "281,480,666,1484,1565,1651,1755,1847,1920,1983,2073,2163,2228,2291,2358,2426,2509,2592,2669,2736,2818,2890,2963,3047,3128,3192,3262,3985,4043,4091,4152,4217,4283,4345,4413,4477,4536,4602,4667,4733,4785,4850,4928,5006,5063"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-af_values-af.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,689,776,864,944,1031,1117,1188,1255,1353,1446,1516,1580,1642,1711,1789,1866,1942,2014,2096,2170,2236,2315,2394,2457,2522,2575,2633,2681,2742,2807,2879,2944,3012,3070,3128,3194,3258,3324,3376,3435,3508,3581", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,77,76,75,71,81,73,65,78,78,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "281,495,684,771,859,939,1026,1112,1183,1250,1348,1441,1511,1575,1637,1706,1784,1861,1937,2009,2091,2165,2231,2310,2389,2452,2517,2570,2628,2676,2737,2802,2874,2939,3007,3065,3123,3189,3253,3319,3371,3430,3503,3576,3631"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,1421,1508,1596,1676,1763,1849,1920,1987,2085,2178,2248,2312,2374,2443,2521,2598,2674,2746,2828,2902,2968,3047,3126,3189,3906,3959,4017,4065,4126,4191,4263,4328,4396,4454,4512,4578,4642,4708,4760,4819,4892,4965", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,77,76,75,71,81,73,65,78,78,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "281,495,684,1503,1591,1671,1758,1844,1915,1982,2080,2173,2243,2307,2369,2438,2516,2593,2669,2741,2823,2897,2963,3042,3121,3184,3249,3954,4012,4060,4121,4186,4258,4323,4391,4449,4507,4573,4637,4703,4755,4814,4887,4960,5015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3254,3323,3383,3449,3516,3591,3661,3750,3834", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "3318,3378,3444,3511,3586,3656,3745,3829,3901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "689,787,889,987,1085,1192,1301,5020", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "782,884,982,1080,1187,1296,1416,5116"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "602,694,793,887,981,1074,1167,4466", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "689,788,882,976,1069,1162,1258,4562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2871,2927,2983,3041,3094,3166,3220,3294,3370", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "2922,2978,3036,3089,3161,3215,3289,3365,3424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,602,672,741,811,887,962,1017,1078,1152,1226,1288,1349,1408,1473,1544,1614,1687,1750,1817,1882,1937,2012,2086,2147,2210,2262,2320,2367,2428,2484,2546,2603,2663,2719,2774,2837,2899,2962,3011,3064,3131,3198", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,70,69,72,62,66,64,54,74,73,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "276,439,597,667,736,806,882,957,1012,1073,1147,1221,1283,1344,1403,1468,1539,1609,1682,1745,1812,1877,1932,2007,2081,2142,2205,2257,2315,2362,2423,2479,2541,2598,2658,2714,2769,2832,2894,2957,3006,3059,3126,3193,3242"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,1263,1333,1402,1472,1548,1623,1678,1739,1813,1887,1949,2010,2069,2134,2205,2275,2348,2411,2478,2543,2598,2673,2747,2808,3429,3481,3539,3586,3647,3703,3765,3822,3882,3938,3993,4056,4118,4181,4230,4283,4350,4417", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,70,69,72,62,66,64,54,74,73,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "276,439,597,1328,1397,1467,1543,1618,1673,1734,1808,1882,1944,2005,2064,2129,2200,2270,2343,2406,2473,2538,2593,2668,2742,2803,2866,3476,3534,3581,3642,3698,3760,3817,3877,3933,3988,4051,4113,4176,4225,4278,4345,4412,4461"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "817,915,1017,1114,1218,1322,1427,5229", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "910,1012,1109,1213,1317,1422,1538,5325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,899,982,1064,1153,1244,1314,1381,1475,1570,1638,1702,1765,1837,1912,1997,2074,2150,2238,2312,2383,2481,2577,2644,2709,2762,2820,2868,2929,2995,3059,3122,3187,3251,3312,3378,3443,3509,3561,3623,3699,3775", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,74,84,76,75,87,73,70,97,95,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,894,977,1059,1148,1239,1309,1376,1470,1565,1633,1697,1760,1832,1907,1992,2069,2145,2233,2307,2378,2476,2572,2639,2704,2757,2815,2863,2924,2990,3054,3117,3182,3246,3307,3373,3438,3504,3556,3618,3694,3770,3826"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,1543,1625,1708,1790,1879,1970,2040,2107,2201,2296,2364,2428,2491,2563,2638,2723,2800,2876,2964,3038,3109,3207,3303,3370,4107,4160,4218,4266,4327,4393,4457,4520,4585,4649,4710,4776,4841,4907,4959,5021,5097,5173", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,74,84,76,75,87,73,70,97,95,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,1620,1703,1785,1874,1965,2035,2102,2196,2291,2359,2423,2486,2558,2633,2718,2795,2871,2959,3033,3104,3202,3298,3365,3430,4155,4213,4261,4322,4388,4452,4515,4580,4644,4705,4771,4836,4902,4954,5016,5092,5168,5224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3435,3510,3571,3636,3708,3787,3860,3948,4032", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "3505,3566,3631,3703,3782,3855,3943,4027,4102"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-mk_values-mk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,516,702,791,881,962,1052,1143,1221,1286,1389,1494,1559,1623,1686,1758,1838,1926,2003,2080,2169,2240,2319,2405,2489,2553,2621,2674,2732,2780,2841,2907,2974,3037,3107,3171,3229,3295,3360,3426,3478,3543,3622,3701", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,79,87,76,76,88,70,78,85,83,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "306,511,697,786,876,957,1047,1138,1216,1281,1384,1489,1554,1618,1681,1753,1833,1921,1998,2075,2164,2235,2314,2400,2484,2548,2616,2669,2727,2775,2836,2902,2969,3032,3102,3166,3224,3290,3355,3421,3473,3538,3617,3696,3752"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,516,1421,1510,1600,1681,1771,1862,1940,2005,2108,2213,2278,2342,2405,2477,2557,2645,2722,2799,2888,2959,3038,3124,3208,3272,4005,4058,4116,4164,4225,4291,4358,4421,4491,4555,4613,4679,4744,4810,4862,4927,5006,5085", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,79,87,76,76,88,70,78,85,83,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "306,511,697,1505,1595,1676,1766,1857,1935,2000,2103,2208,2273,2337,2400,2472,2552,2640,2717,2794,2883,2954,3033,3119,3203,3267,3335,4053,4111,4159,4220,4286,4353,4416,4486,4550,4608,4674,4739,4805,4857,4922,5001,5080,5136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,250,318,395,468,557,642", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "120,181,245,313,390,463,552,637,715"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3340,3410,3471,3535,3603,3680,3753,3842,3927", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "3405,3466,3530,3598,3675,3748,3837,3922,4000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "702,800,902,999,1097,1202,1305,5141", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "795,897,994,1092,1197,1300,1416,5237"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-fa_values-fa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,183,250,310,388,463,552,640", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "117,178,245,305,383,458,547,635,700"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3235,3302,3363,3430,3490,3568,3643,3732,3820", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "3297,3358,3425,3485,3563,3638,3727,3815,3880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "696,795,897,996,1096,1197,1303,5009", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "790,892,991,1091,1192,1298,1415,5105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,696,779,864,943,1036,1128,1205,1268,1360,1447,1510,1572,1633,1700,1774,1853,1929,1998,2074,2144,2216,2296,2374,2437,2511,2565,2634,2682,2743,2801,2878,2942,3006,3066,3128,3193,3259,3325,3377,3436,3509,3582", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,73,78,75,68,75,69,71,79,77,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "308,507,691,774,859,938,1031,1123,1200,1263,1355,1442,1505,1567,1628,1695,1769,1848,1924,1993,2069,2139,2211,2291,2369,2432,2506,2560,2629,2677,2738,2796,2873,2937,3001,3061,3123,3188,3254,3320,3372,3431,3504,3577,3630"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,1420,1503,1588,1667,1760,1852,1929,1992,2084,2171,2234,2296,2357,2424,2498,2577,2653,2722,2798,2868,2940,3020,3098,3161,3885,3939,4008,4056,4117,4175,4252,4316,4380,4440,4502,4567,4633,4699,4751,4810,4883,4956", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,73,78,75,68,75,69,71,79,77,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "308,507,691,1498,1583,1662,1755,1847,1924,1987,2079,2166,2229,2291,2352,2419,2493,2572,2648,2717,2793,2863,2935,3015,3093,3156,3230,3934,4003,4051,4112,4170,4247,4311,4375,4435,4497,4562,4628,4694,4746,4805,4878,4951,5004"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ko_values-ko.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,172,232,288,360,419,501,581", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "109,167,227,283,355,414,496,576,639"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2928,2987,3045,3105,3161,3233,3292,3374,3454", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "2982,3040,3100,3156,3228,3287,3369,3449,3512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "612,704,804,898,995,1091,1189,4569", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "699,799,893,990,1086,1184,1284,4665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,612,686,759,830,911,989,1048,1109,1186,1262,1326,1387,1446,1511,1581,1651,1725,1789,1858,1923,1981,2055,2125,2186,2251,2304,2361,2407,2466,2522,2584,2641,2701,2757,2813,2877,2940,3004,3054,3110,3180,3250", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,69,69,73,63,68,64,57,73,69,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "282,449,607,681,754,825,906,984,1043,1104,1181,1257,1321,1382,1441,1506,1576,1646,1720,1784,1853,1918,1976,2050,2120,2181,2246,2299,2356,2402,2461,2517,2579,2636,2696,2752,2808,2872,2935,2999,3049,3105,3175,3245,3298"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,1289,1363,1436,1507,1588,1666,1725,1786,1863,1939,2003,2064,2123,2188,2258,2328,2402,2466,2535,2600,2658,2732,2802,2863,3517,3570,3627,3673,3732,3788,3850,3907,3967,4023,4079,4143,4206,4270,4320,4376,4446,4516", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,69,69,73,63,68,64,57,73,69,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "282,449,607,1358,1431,1502,1583,1661,1720,1781,1858,1934,1998,2059,2118,2183,2253,2323,2397,2461,2530,2595,2653,2727,2797,2858,2923,3565,3622,3668,3727,3783,3845,3902,3962,4018,4074,4138,4201,4265,4315,4371,4441,4511,4564"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-hu_values-hu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "692,789,891,993,1094,1197,1304,5163", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "784,886,988,1089,1192,1299,1409,5259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,692,780,867,945,1031,1134,1208,1276,1373,1474,1547,1615,1680,1748,1834,1913,1996,2070,2152,2226,2299,2384,2468,2536,2599,2652,2710,2758,2819,2883,2950,3014,3082,3147,3206,3271,3337,3403,3456,3521,3603,3685", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,85,78,82,73,81,73,72,84,83,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "280,485,687,775,862,940,1026,1129,1203,1271,1368,1469,1542,1610,1675,1743,1829,1908,1991,2065,2147,2221,2294,2379,2463,2531,2594,2647,2705,2753,2814,2878,2945,3009,3077,3142,3201,3266,3332,3398,3451,3516,3598,3680,3737"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,1414,1502,1589,1667,1753,1856,1930,1998,2095,2196,2269,2337,2402,2470,2556,2635,2718,2792,2874,2948,3021,3106,3190,3258,4020,4073,4131,4179,4240,4304,4371,4435,4503,4568,4627,4692,4758,4824,4877,4942,5024,5106", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,85,78,82,73,81,73,72,84,83,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "280,485,687,1497,1584,1662,1748,1851,1925,1993,2090,2191,2264,2332,2397,2465,2551,2630,2713,2787,2869,2943,3016,3101,3185,3253,3316,4068,4126,4174,4235,4299,4366,4430,4498,4563,4622,4687,4753,4819,4872,4937,5019,5101,5158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,192,266,338,416,489,583,673", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "125,187,261,333,411,484,578,668,749"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3321,3396,3458,3532,3604,3682,3755,3849,3939", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "3391,3453,3527,3599,3677,3750,3844,3934,4015"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,662,748,835,908,1004,1100,1180,1248,1347,1446,1512,1581,1647,1718,1795,1870,1946,2017,2101,2177,2257,2352,2443,2509,2573,2626,2684,2732,2793,2858,2920,2986,3058,3122,3183,3249,3314,3380,3433,3498,3577,3656", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,76,74,75,70,83,75,79,94,90,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,470,657,743,830,903,999,1095,1175,1243,1342,1441,1507,1576,1642,1713,1790,1865,1941,2012,2096,2172,2252,2347,2438,2504,2568,2621,2679,2727,2788,2853,2915,2981,3053,3117,3178,3244,3309,3375,3428,3493,3572,3651,3709"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,1394,1480,1567,1640,1736,1832,1912,1980,2079,2178,2244,2313,2379,2450,2527,2602,2678,2749,2833,2909,2989,3084,3175,3241,3974,4027,4085,4133,4194,4259,4321,4387,4459,4523,4584,4650,4715,4781,4834,4899,4978,5057", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,76,74,75,70,83,75,79,94,90,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,470,657,1475,1562,1635,1731,1827,1907,1975,2074,2173,2239,2308,2374,2445,2522,2597,2673,2744,2828,2904,2984,3079,3170,3236,3300,4022,4080,4128,4189,4254,4316,4382,4454,4518,4579,4645,4710,4776,4829,4894,4973,5052,5110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3305,3380,3443,3508,3577,3654,3728,3817,3905", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "3375,3438,3503,3572,3649,3723,3812,3900,3969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "662,761,863,963,1061,1168,1274,5115", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "756,858,958,1056,1163,1269,1389,5211"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-my_values-my.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "687,790,894,997,1099,1204,1310,5193", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "785,889,992,1094,1199,1305,1424,5289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,292,491,687,771,853,940,1042,1138,1211,1278,1377,1472,1540,1607,1674,1741,1826,1917,2003,2074,2154,2227,2298,2387,2476,2541,2605,2658,2716,2766,2827,2885,2947,3020,3089,3154,3212,3276,3341,3409,3463,3525,3601,3677", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,84,90,85,70,79,72,70,88,88,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "287,486,682,766,848,935,1037,1133,1206,1273,1372,1467,1535,1602,1669,1736,1821,1912,1998,2069,2149,2222,2293,2382,2471,2536,2600,2653,2711,2761,2822,2880,2942,3015,3084,3149,3207,3271,3336,3404,3458,3520,3596,3672,3726"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,292,491,1429,1513,1595,1682,1784,1880,1953,2020,2119,2214,2282,2349,2416,2483,2568,2659,2745,2816,2896,2969,3040,3129,3218,3283,4067,4120,4178,4228,4289,4347,4409,4482,4551,4616,4674,4738,4803,4871,4925,4987,5063,5139", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,84,90,85,70,79,72,70,88,88,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "287,486,682,1508,1590,1677,1779,1875,1948,2015,2114,2209,2277,2344,2411,2478,2563,2654,2740,2811,2891,2964,3035,3124,3213,3278,3342,4115,4173,4223,4284,4342,4404,4477,4546,4611,4669,4733,4798,4866,4920,4982,5058,5134,5188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,275,344,426,501,602,697", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "125,197,270,339,421,496,597,692,770"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3347,3422,3494,3567,3636,3718,3793,3894,3989", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "3417,3489,3562,3631,3713,3788,3889,3984,4062"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-el_values-el.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "738,836,939,1039,1142,1250,1356,5249", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "831,934,1034,1137,1245,1351,1468,5345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,510,738,826,915,997,1080,1172,1269,1335,1431,1527,1592,1662,1727,1801,1880,1960,2049,2119,2202,2274,2371,2465,2556,2622,2697,2750,2808,2862,2923,2988,3057,3122,3194,3256,3316,3381,3448,3515,3573,3639,3719,3799", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,78,79,88,69,82,71,96,93,90,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "282,505,733,821,910,992,1075,1167,1264,1330,1426,1522,1587,1657,1722,1796,1875,1955,2044,2114,2197,2269,2366,2460,2551,2617,2692,2745,2803,2857,2918,2983,3052,3117,3189,3251,3311,3376,3443,3510,3568,3634,3714,3794,3848"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,510,1473,1561,1650,1732,1815,1907,2004,2070,2166,2262,2327,2397,2462,2536,2615,2695,2784,2854,2937,3009,3106,3200,3291,3357,4093,4146,4204,4258,4319,4384,4453,4518,4590,4652,4712,4777,4844,4911,4969,5035,5115,5195", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,78,79,88,69,82,71,96,93,90,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "282,505,733,1556,1645,1727,1810,1902,1999,2065,2161,2257,2322,2392,2457,2531,2610,2690,2779,2849,2932,3004,3101,3195,3286,3352,3427,4141,4199,4253,4314,4379,4448,4513,4585,4647,4707,4772,4839,4906,4964,5030,5110,5190,5244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,184,242,305,379,455,554,649", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "121,179,237,300,374,450,549,644,711"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3432,3503,3561,3619,3682,3756,3832,3931,4026", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "3498,3556,3614,3677,3751,3827,3926,4021,4088"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-uk_values-uk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,626,956,1040,1122,1205,1305,1404,1489,1552,1650,1749,1820,1889,1955,2023,2101,2180,2256,2333,2415,2490,2578,2666,2754,2822,2907,2960,3020,3068,3129,3196,3264,3328,3395,3460,3520,3586,3651,3717,3769,3830,3915,4000", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,77,78,75,76,81,74,87,87,87,67,84,52,59,47,60,66,67,63,66,64,59,65,64,65,51,60,84,84,54", "endOffsets": "282,621,951,1035,1117,1200,1300,1399,1484,1547,1645,1744,1815,1884,1950,2018,2096,2175,2251,2328,2410,2485,2573,2661,2749,2817,2902,2955,3015,3063,3124,3191,3259,3323,3390,3455,3515,3581,3646,3712,3764,3825,3910,3995,4050"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,626,1683,1767,1849,1932,2032,2131,2216,2279,2377,2476,2547,2616,2682,2750,2828,2907,2983,3060,3142,3217,3305,3393,3481,3549,4322,4375,4435,4483,4544,4611,4679,4743,4810,4875,4935,5001,5066,5132,5184,5245,5330,5415", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,77,78,75,76,81,74,87,87,87,67,84,52,59,47,60,66,67,63,66,64,59,65,64,65,51,60,84,84,54", "endOffsets": "282,621,951,1762,1844,1927,2027,2126,2211,2274,2372,2471,2542,2611,2677,2745,2823,2902,2978,3055,3137,3212,3300,3388,3476,3544,3629,4370,4430,4478,4539,4606,4674,4738,4805,4870,4930,4996,5061,5127,5179,5240,5325,5410,5465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3634,3708,3773,3841,3912,3992,4065,4158,4247", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "3703,3768,3836,3907,3987,4060,4153,4242,4317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "956,1056,1158,1259,1360,1465,1570,5470", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "1051,1153,1254,1355,1460,1565,1678,5566"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-mr_values-mr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "671,771,875,976,1079,1181,1286,5006", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "766,870,971,1074,1176,1281,1398,5102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1750,1828,1905,1977,2056,2127,2197,2278,2359,2423,2486,2539,2597,2645,2706,2767,2834,2896,2962,3021,3086,3151,3216,3284,3337,3397,3471,3545", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,78,77,76,71,78,70,69,80,80,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1745,1823,1900,1972,2051,2122,2192,2273,2354,2418,2481,2534,2592,2640,2701,2762,2829,2891,2957,3016,3081,3146,3211,3279,3332,3392,3466,3540,3593"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,1403,1485,1565,1648,1735,1829,1897,1961,2051,2142,2207,2275,2335,2403,2482,2560,2637,2709,2788,2859,2929,3010,3091,3155,3894,3947,4005,4053,4114,4175,4242,4304,4370,4429,4494,4559,4624,4692,4745,4805,4879,4953", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,78,77,76,71,78,70,69,80,80,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "281,482,666,1480,1560,1643,1730,1824,1892,1956,2046,2137,2202,2270,2330,2398,2477,2555,2632,2704,2783,2854,2924,3005,3086,3150,3213,3942,4000,4048,4109,4170,4237,4299,4365,4424,4489,4554,4619,4687,4740,4800,4874,4948,5001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3218,3288,3353,3422,3491,3566,3630,3727,3821", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "3283,3348,3417,3486,3561,3625,3722,3816,3889"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ne_values-ne.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "731,834,937,1039,1145,1243,1343,5262", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "829,932,1034,1140,1238,1338,1446,5358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3445,3516,3587,3657,3724,3802,3879,3979,4073", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "3511,3582,3652,3719,3797,3874,3974,4068,4137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,731,824,917,1007,1108,1211,1297,1361,1456,1551,1623,1696,1756,1826,1911,2002,2088,2167,2259,2327,2413,2505,2593,2662,2725,2778,2836,2884,2945,3007,3078,3140,3202,3261,3328,3394,3457,3524,3578,3640,3716,3792", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,84,90,85,78,91,67,85,91,87,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "305,524,726,819,912,1002,1103,1206,1292,1356,1451,1546,1618,1691,1751,1821,1906,1997,2083,2162,2254,2322,2408,2500,2588,2657,2720,2773,2831,2879,2940,3002,3073,3135,3197,3256,3323,3389,3452,3519,3573,3635,3711,3787,3840"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,1451,1544,1637,1727,1828,1931,2017,2081,2176,2271,2343,2416,2476,2546,2631,2722,2808,2887,2979,3047,3133,3225,3313,3382,4142,4195,4253,4301,4362,4424,4495,4557,4619,4678,4745,4811,4874,4941,4995,5057,5133,5209", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,84,90,85,78,91,67,85,91,87,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "305,524,726,1539,1632,1722,1823,1926,2012,2076,2171,2266,2338,2411,2471,2541,2626,2717,2803,2882,2974,3042,3128,3220,3308,3377,3440,4190,4248,4296,4357,4419,4490,4552,4614,4673,4740,4806,4869,4936,4990,5052,5128,5204,5257"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b30e4dc937276f023b4a6e07a5156550\\transformed\\media-1.4.3\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "141,142,143,333,357,604,606,607,612,614", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "5782,5871,5942,18041,19340,34790,34966,35088,35350,35545", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "5866,5937,6010,18098,19395,34851,35083,35144,35411,35607"}}, {"source": "C:\\python_programs\\DocScribe\\doc_transcribe\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "17,8", "startColumns": "4,4", "startOffsets": "827,387", "endLines": "19,10", "endColumns": "12,12", "endOffsets": "994,551"}, "to": {"startLines": "593,596", "startColumns": "4,4", "startOffsets": "34131,34303", "endLines": "595,598", "endColumns": "12,12", "endOffsets": "34298,34467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fd0a2dbf4f96c6dbec5c3dc6707cb9de\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "19226", "endColumns": "49", "endOffsets": "19271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bf6ae328e54b92e9215264b71b8866bc\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "329,335,356,717,722", "startColumns": "4,4,4,4,4", "startOffsets": "17848,18145,19276,39704,39874", "endLines": "329,335,356,721,725", "endColumns": "56,64,63,24,24", "endOffsets": "17900,18205,19335,39869,40018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,15,16,20,21,22,23,24,25,26,27,28,29,30,35,42,43,44,45,46,47,52,53,54,55,56,57,58,59,60,61,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,208,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,261,265,269,273,277,281,285,289,290,296,307,311,315,319,323,327,331,335,339,343,347,351,362,367,372,377,388,396,406,410,414,418,421,437,463,498,527", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,539,588,714,763,812,871,925,977,1027,1092,1149,1196,1251,1399,1637,1686,1747,1807,1863,1923,2093,2153,2206,2263,2318,2374,2431,2480,2531,2590,2877,2942,3000,3049,3097,3148,3205,3262,3324,3391,3462,3534,3578,3635,3691,3754,3827,3897,3956,4013,4060,4115,4160,4209,4264,4318,4368,4419,4473,4532,4582,4640,4696,4749,4812,4877,4940,4992,5052,5116,5182,5240,5312,5373,5443,5513,5578,5643,5714,5802,5900,5996,6070,6146,6220,6302,6388,6474,6560,6638,6726,6812,6882,6974,7052,7132,7210,7296,7378,7471,7549,7640,7721,7810,7913,8014,8098,8194,8291,8386,8479,8571,8664,8757,8850,8933,9020,9115,9208,9289,9384,9477,9554,9598,9639,9684,9732,9776,9819,9868,9915,9959,10015,10068,10110,10157,10205,10265,10303,10353,10397,10447,10499,10537,10584,10631,10672,10711,10749,10793,10841,10883,10921,10963,11017,11064,11101,11150,11192,11233,11274,11316,11359,11397,11433,11511,11589,11886,12156,12238,12320,12462,12540,12627,12712,12779,12842,12934,13026,13091,13154,13216,13287,13397,13508,13618,13685,13765,13836,13903,13988,14073,14136,14224,14288,14430,14530,14578,14721,14784,14846,14911,14982,15040,15098,15164,15228,15294,15346,15408,15484,15560,15614,15893,16117,16320,16526,16729,16944,17153,17350,17388,17742,18529,18770,19010,19267,19520,19773,20008,20255,20494,20738,20959,21154,21796,22087,22383,22686,23322,23856,24330,24541,24741,24917,25025,25601,26574,27863,28953", "endLines": "10,11,12,13,14,15,19,20,21,22,23,24,25,26,27,28,29,34,41,42,43,44,45,46,51,52,53,54,55,56,57,58,59,60,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,207,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,260,264,268,272,276,280,284,288,289,295,306,310,314,318,322,326,330,334,338,342,346,350,361,366,371,376,387,395,405,409,413,417,420,436,462,497,526,565", "endColumns": "17,49,53,53,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "330,380,434,488,534,583,709,758,807,866,920,972,1022,1087,1144,1191,1246,1394,1632,1681,1742,1802,1858,1918,2088,2148,2201,2258,2313,2369,2426,2475,2526,2585,2872,2937,2995,3044,3092,3143,3200,3257,3319,3386,3457,3529,3573,3630,3686,3749,3822,3892,3951,4008,4055,4110,4155,4204,4259,4313,4363,4414,4468,4527,4577,4635,4691,4744,4807,4872,4935,4987,5047,5111,5177,5235,5307,5368,5438,5508,5573,5638,5709,5797,5895,5991,6065,6141,6215,6297,6383,6469,6555,6633,6721,6807,6877,6969,7047,7127,7205,7291,7373,7466,7544,7635,7716,7805,7908,8009,8093,8189,8286,8381,8474,8566,8659,8752,8845,8928,9015,9110,9203,9284,9379,9472,9549,9593,9634,9679,9727,9771,9814,9863,9910,9954,10010,10063,10105,10152,10200,10260,10298,10348,10392,10442,10494,10532,10579,10626,10667,10706,10744,10788,10836,10878,10916,10958,11012,11059,11096,11145,11187,11228,11269,11311,11354,11392,11428,11506,11584,11881,12151,12233,12315,12457,12535,12622,12707,12774,12837,12929,13021,13086,13149,13211,13282,13392,13503,13613,13680,13760,13831,13898,13983,14068,14131,14219,14283,14425,14525,14573,14716,14779,14841,14906,14977,15035,15093,15159,15223,15289,15341,15403,15479,15555,15609,15888,16112,16315,16521,16724,16939,17148,17345,17383,17737,18524,18765,19005,19262,19515,19768,20003,20250,20489,20733,20954,21149,21791,22082,22378,22681,23317,23851,24325,24536,24736,24912,25020,25596,26569,27858,28948,30391"}, "to": {"startLines": "2,11,12,14,15,16,17,21,22,23,24,47,48,49,51,52,53,55,60,67,68,69,70,71,72,77,78,79,80,81,82,83,84,85,114,122,123,124,125,126,131,132,133,134,135,136,137,138,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,358,359,361,365,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,431,436,440,444,448,452,456,460,464,465,471,482,486,490,494,498,502,506,510,514,518,522,526,537,542,547,552,563,571,581,585,589,628,647,765,791,886,915", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,498,552,598,647,773,822,871,930,1595,1647,1697,1822,1879,1926,2037,2185,2423,2472,2533,2593,2649,2709,2879,2939,2992,3049,3104,3160,3217,3266,3317,4275,4601,4666,4724,4773,4821,5163,5220,5277,5339,5406,5477,5549,5593,6503,6559,6622,6695,6765,6824,6881,6928,6983,7028,7077,7132,7186,7236,7287,7341,7400,7450,7508,7564,7617,7680,7745,7808,7860,7920,7984,8050,8108,8180,8241,8311,8381,8446,8511,9946,10034,10132,10228,10302,10378,10452,10534,10620,10706,10792,10870,10958,11044,11114,11206,11284,11364,11442,11528,11610,11703,11781,11872,11953,12042,12145,12246,12330,12426,12523,12618,12711,12803,12896,12989,13082,13165,13252,13347,13440,13521,13616,13709,15969,16013,16054,16099,16147,16191,16234,16283,16330,16374,16430,16483,16525,16572,16620,16680,16718,16768,16812,16862,16914,16952,16999,17046,17087,17126,17164,17208,17256,17298,17336,17378,17432,17479,17516,17565,17607,17648,17689,17731,17774,17812,19400,19478,19626,19923,20787,20869,20951,21093,21171,21258,21343,21410,21473,21565,21657,21722,21785,21847,21918,22028,22139,22249,22316,22396,22467,22534,22619,22704,22767,22855,23565,23707,23807,23855,23998,24061,24123,24188,24259,24317,24375,24441,24505,24571,24623,24685,24761,24837,24962,25241,25465,25668,25874,26077,26292,26501,26698,26736,27090,27877,28118,28358,28615,28868,29121,29356,29603,29842,30086,30307,30502,31077,31368,31664,31967,32536,33070,33544,33755,33955,36104,36716,40872,41811,45377,46433", "endLines": "10,11,12,14,15,16,20,21,22,23,24,47,48,49,51,52,53,59,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,120,122,123,124,125,126,131,132,133,134,135,136,137,138,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,358,359,364,368,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,435,439,443,447,451,455,459,463,464,470,481,485,489,493,497,501,505,509,513,517,521,525,536,541,546,551,562,570,580,584,588,592,630,662,790,825,914,953", "endColumns": "17,49,53,53,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "330,380,434,547,593,642,768,817,866,925,979,1642,1692,1757,1874,1921,1976,2180,2418,2467,2528,2588,2644,2704,2874,2934,2987,3044,3099,3155,3212,3261,3312,3371,4557,4661,4719,4768,4816,4867,5215,5272,5334,5401,5472,5544,5588,5645,6554,6617,6690,6760,6819,6876,6923,6978,7023,7072,7127,7181,7231,7282,7336,7395,7445,7503,7559,7612,7675,7740,7803,7855,7915,7979,8045,8103,8175,8236,8306,8376,8441,8506,8577,10029,10127,10223,10297,10373,10447,10529,10615,10701,10787,10865,10953,11039,11109,11201,11279,11359,11437,11523,11605,11698,11776,11867,11948,12037,12140,12241,12325,12421,12518,12613,12706,12798,12891,12984,13077,13160,13247,13342,13435,13516,13611,13704,13781,16008,16049,16094,16142,16186,16229,16278,16325,16369,16425,16478,16520,16567,16615,16675,16713,16763,16807,16857,16909,16947,16994,17041,17082,17121,17159,17203,17251,17293,17331,17373,17427,17474,17511,17560,17602,17643,17684,17726,17769,17807,17843,19473,19551,19918,20188,20864,20946,21088,21166,21253,21338,21405,21468,21560,21652,21717,21780,21842,21913,22023,22134,22244,22311,22391,22462,22529,22614,22699,22762,22850,22914,23702,23802,23850,23993,24056,24118,24183,24254,24312,24370,24436,24500,24566,24618,24680,24756,24832,24886,25236,25460,25663,25869,26072,26287,26496,26693,26731,27085,27872,28113,28353,28610,28863,29116,29351,29598,29837,30081,30302,30497,31072,31363,31659,31962,32531,33065,33539,33750,33950,34126,36207,37287,41806,42982,46428,47752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6cc079d3a83268c2582a050621870aa2\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,38", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,2084"}, "to": {"startLines": "54,186,187,188,189,190,191,330,826", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1981,8582,8641,8689,8745,8820,8896,17905,42987", "endLines": "54,186,187,188,189,190,191,330,846", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "2032,8636,8684,8740,8815,8891,8963,17966,43822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68656057c0f9df10db2245ba342a2616\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "13,25,31,39,86,98,104,110,111,112,113,121,286,617,623,847,855,870", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "439,984,1157,1376,3376,3690,3878,4065,4118,4178,4230,4562,15909,35751,35946,43827,44109,44723", "endLines": "13,30,38,46,97,103,109,110,111,112,113,121,286,622,627,854,869,885", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "493,1152,1371,1590,3685,3873,4060,4113,4173,4225,4270,4596,15964,35941,36099,44104,44718,45372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bdf28e1efaec1f3184a5c597f2146f91\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "334,353", "startColumns": "4,4", "startOffsets": "18103,19112", "endColumns": "41,59", "endOffsets": "18140,19167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "50,127,128,129,130,139,140,144,145,146,147,148,149,150,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,331,332,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,360,370,371,372,373,374,375,376,430,599,600,605,608,613,615,616,631,637,663,696,726,759", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1762,4872,4944,5032,5097,5650,5719,6015,6085,6153,6225,6295,6356,6430,8968,9029,9090,9152,9216,9278,9339,9407,9507,9567,9633,9706,9775,9832,9884,13786,13858,13934,13999,14058,14117,14177,14237,14297,14357,14417,14477,14537,14597,14657,14717,14776,14836,14896,14956,15016,15076,15136,15196,15256,15316,15376,15435,15495,15555,15614,15673,15732,15791,15850,17971,18006,18210,18265,18328,18383,18441,18499,18560,18623,18680,18731,18781,18842,18899,18965,18999,19034,19556,20276,20343,20415,20484,20553,20627,20699,24891,34472,34589,34856,35149,35416,35612,35684,36212,36415,37292,39023,40023,40705", "endLines": "50,127,128,129,130,139,140,144,145,146,147,148,149,150,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,331,332,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,360,370,371,372,373,374,375,376,430,599,603,605,611,613,615,616,636,646,695,716,758,764", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1817,4939,5027,5092,5158,5714,5777,6080,6148,6220,6290,6351,6425,6498,9024,9085,9147,9211,9273,9334,9402,9502,9562,9628,9701,9770,9827,9879,9941,13853,13929,13994,14053,14112,14172,14232,14292,14352,14412,14472,14532,14592,14652,14712,14771,14831,14891,14951,15011,15071,15131,15191,15251,15311,15371,15430,15490,15550,15609,15668,15727,15786,15845,15904,18001,18036,18260,18323,18378,18436,18494,18555,18618,18675,18726,18776,18837,18894,18960,18994,19029,19064,19621,20338,20410,20479,20548,20622,20694,20782,24957,34584,34785,34961,35345,35540,35679,35746,36410,36711,39018,39699,40700,40867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db865cfc583742df8a08e9a06de1f57a\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "19172", "endColumns": "53", "endOffsets": "19221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\aa11fe827c549893c9e5c2fc6854bbf6\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "352", "startColumns": "4", "startOffsets": "19069", "endColumns": "42", "endOffsets": "19107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "403,404,405,406,407,408,409,410,411", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22919,22989,23051,23116,23180,23257,23322,23412,23496", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "22984,23046,23111,23175,23252,23317,23407,23491,23560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43980c3f7213c92c8646e00d332d5bd5\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "369", "startColumns": "4", "startOffsets": "20193", "endColumns": "82", "endOffsets": "20271"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-bg_values-bg.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "706,803,913,1015,1116,1223,1328,5261", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "798,908,1010,1111,1218,1323,1442,5357"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,500,706,796,887,970,1057,1145,1225,1290,1394,1499,1577,1651,1715,1783,1867,1945,2035,2112,2204,2276,2357,2448,2537,2603,2671,2725,2785,2833,2894,2966,3034,3097,3173,3238,3296,3367,3432,3503,3555,3614,3695,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,83,77,89,76,91,71,80,90,88,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "282,495,701,791,882,965,1052,1140,1220,1285,1389,1494,1572,1646,1710,1778,1862,1940,2030,2107,2199,2271,2352,2443,2532,2598,2666,2720,2780,2828,2889,2961,3029,3092,3168,3233,3291,3362,3427,3498,3550,3609,3690,3771,3828"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,500,1447,1537,1628,1711,1798,1886,1966,2031,2135,2240,2318,2392,2456,2524,2608,2686,2776,2853,2945,3017,3098,3189,3278,3344,4099,4153,4213,4261,4322,4394,4462,4525,4601,4666,4724,4795,4860,4931,4983,5042,5123,5204", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,83,77,89,76,91,71,80,90,88,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "282,495,701,1532,1623,1706,1793,1881,1961,2026,2130,2235,2313,2387,2451,2519,2603,2681,2771,2848,2940,3012,3093,3184,3273,3339,3407,4148,4208,4256,4317,4389,4457,4520,4596,4661,4719,4790,4855,4926,4978,5037,5118,5199,5256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,189,253,327,405,478,575,666", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "121,184,248,322,400,473,570,661,737"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3412,3483,3546,3610,3684,3762,3835,3932,4023", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "3478,3541,3605,3679,3757,3830,3927,4018,4094"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-et_values-et.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "702,797,899,997,1100,1206,1311,5085", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "792,894,992,1095,1201,1306,1426,5181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,514,702,783,864,940,1031,1124,1194,1258,1342,1425,1490,1554,1617,1687,1763,1844,1919,1991,2075,2144,2213,2308,2402,2467,2533,2586,2646,2694,2755,2820,2890,2955,3021,3085,3145,3210,3275,3341,3393,3455,3531,3607", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,75,80,74,71,83,68,68,94,93,64,65,52,59,47,60,64,69,64,65,63,59,64,64,65,51,61,75,75,54", "endOffsets": "318,509,697,778,859,935,1026,1119,1189,1253,1337,1420,1485,1549,1612,1682,1758,1839,1914,1986,2070,2139,2208,2303,2397,2462,2528,2581,2641,2689,2750,2815,2885,2950,3016,3080,3140,3205,3270,3336,3388,3450,3526,3602,3657"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,514,1431,1512,1593,1669,1760,1853,1923,1987,2071,2154,2219,2283,2346,2416,2492,2573,2648,2720,2804,2873,2942,3037,3131,3196,3956,4009,4069,4117,4178,4243,4313,4378,4444,4508,4568,4633,4698,4764,4816,4878,4954,5030", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,75,80,74,71,83,68,68,94,93,64,65,52,59,47,60,64,69,64,65,63,59,64,64,65,51,61,75,75,54", "endOffsets": "318,509,697,1507,1588,1664,1755,1848,1918,1982,2066,2149,2214,2278,2341,2411,2487,2568,2643,2715,2799,2868,2937,3032,3126,3191,3257,4004,4064,4112,4173,4238,4308,4373,4439,4503,4563,4628,4693,4759,4811,4873,4949,5025,5080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,263,337,419,490,580,672", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "124,191,258,332,414,485,575,667,744"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3262,3336,3403,3470,3544,3626,3697,3787,3879", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "3331,3398,3465,3539,3621,3692,3782,3874,3951"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-mn_values-mn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,672,758,847,931,1023,1114,1190,1255,1344,1437,1508,1576,1637,1705,1796,1890,1999,2066,2148,2219,2299,2382,2462,2528,2593,2646,2704,2752,2813,2875,2951,3013,3077,3138,3199,3263,3328,3394,3446,3510,3588,3666", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,90,93,108,66,81,70,79,82,79,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "280,491,667,753,842,926,1018,1109,1185,1250,1339,1432,1503,1571,1632,1700,1791,1885,1994,2061,2143,2214,2294,2377,2457,2523,2588,2641,2699,2747,2808,2870,2946,3008,3072,3133,3194,3258,3323,3389,3441,3505,3583,3661,3719"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,1407,1493,1582,1666,1758,1849,1925,1990,2079,2172,2243,2311,2372,2440,2531,2625,2734,2801,2883,2954,3034,3117,3197,3263,3989,4042,4100,4148,4209,4271,4347,4409,4473,4534,4595,4659,4724,4790,4842,4906,4984,5062", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,90,93,108,66,81,70,79,82,79,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "280,491,667,1488,1577,1661,1753,1844,1920,1985,2074,2167,2238,2306,2367,2435,2526,2620,2729,2796,2878,2949,3029,3112,3192,3258,3323,4037,4095,4143,4204,4266,4342,4404,4468,4529,4590,4654,4719,4785,4837,4901,4979,5057,5115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,244,308,382,456,547,635", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "115,174,239,303,377,451,542,630,711"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3328,3393,3452,3517,3581,3655,3729,3820,3908", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "3388,3447,3512,3576,3650,3724,3815,3903,3984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "672,770,872,973,1071,1176,1288,5120", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "765,867,968,1066,1171,1283,1402,5216"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,745,828,901,995,1085,1159,1226,1323,1420,1486,1555,1622,1693,1770,1853,1928,1995,2081,2154,2228,2323,2416,2480,2547,2600,2658,2706,2767,2832,2900,2965,3034,3098,3159,3225,3290,3356,3409,3469,3543,3617", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,76,82,74,66,85,72,73,94,92,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "280,469,659,740,823,896,990,1080,1154,1221,1318,1415,1481,1550,1617,1688,1765,1848,1923,1990,2076,2149,2223,2318,2411,2475,2542,2595,2653,2701,2762,2827,2895,2960,3029,3093,3154,3220,3285,3351,3404,3464,3538,3612,3671"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,1396,1477,1560,1633,1727,1817,1891,1958,2055,2152,2218,2287,2354,2425,2502,2585,2660,2727,2813,2886,2960,3055,3148,3212,3983,4036,4094,4142,4203,4268,4336,4401,4470,4534,4595,4661,4726,4792,4845,4905,4979,5053", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,76,82,74,66,85,72,73,94,92,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "280,469,659,1472,1555,1628,1722,1812,1886,1953,2050,2147,2213,2282,2349,2420,2497,2580,2655,2722,2808,2881,2955,3050,3143,3207,3274,4031,4089,4137,4198,4263,4331,4396,4465,4529,4590,4656,4721,4787,4840,4900,4974,5048,5107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "664,761,863,962,1062,1169,1275,5112", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "756,858,957,1057,1164,1270,1391,5208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,194,261,332,414,496,591,680", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "125,189,256,327,409,491,586,675,754"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3279,3354,3418,3485,3556,3638,3720,3815,3904", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "3349,3413,3480,3551,3633,3715,3810,3899,3978"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-nl_values-nl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3278,3349,3413,3477,3544,3621,3690,3779,3862", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "3344,3408,3472,3539,3616,3685,3774,3857,3929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "677,779,881,981,1081,1188,1292,5048", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "774,876,976,1076,1183,1287,1406,5144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1808,1887,1964,2037,2116,2191,2260,2337,2413,2479,2544,2597,2655,2703,2764,2829,2891,2956,3024,3082,3140,3206,3271,3337,3389,3451,3527,3603", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,78,78,76,72,78,74,68,76,75,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1803,1882,1959,2032,2111,2186,2255,2332,2408,2474,2539,2592,2650,2698,2759,2824,2886,2951,3019,3077,3135,3201,3266,3332,3384,3446,3522,3598,3653"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,1411,1500,1588,1668,1761,1854,1927,1994,2096,2194,2262,2329,2394,2463,2542,2621,2698,2771,2850,2925,2994,3071,3147,3213,3934,3987,4045,4093,4154,4219,4281,4346,4414,4472,4530,4596,4661,4727,4779,4841,4917,4993", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,78,78,76,72,78,74,68,76,75,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "281,481,672,1495,1583,1663,1756,1849,1922,1989,2091,2189,2257,2324,2389,2458,2537,2616,2693,2766,2845,2920,2989,3066,3142,3208,3273,3982,4040,4088,4149,4214,4276,4341,4409,4467,4525,4591,4656,4722,4774,4836,4912,4988,5043"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-th_values-th.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,194,262,330,407,480,571,657", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "126,189,257,325,402,475,566,652,731"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3193,3269,3332,3400,3468,3545,3618,3709,3795", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "3264,3327,3395,3463,3540,3613,3704,3790,3869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,656,737,816,895,983,1073,1144,1208,1299,1390,1454,1517,1582,1653,1732,1807,1888,1956,2039,2112,2183,2267,2351,2414,2478,2531,2589,2637,2698,2757,2825,2891,2959,3020,3079,3145,3212,3279,3333,3396,3478,3555", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,78,74,80,67,82,72,70,83,83,62,63,52,57,47,60,58,67,65,67,60,58,65,66,66,53,62,81,76,53", "endOffsets": "278,473,651,732,811,890,978,1068,1139,1203,1294,1385,1449,1512,1577,1648,1727,1802,1883,1951,2034,2107,2178,2262,2346,2409,2473,2526,2584,2632,2693,2752,2820,2886,2954,3015,3074,3140,3207,3274,3328,3391,3473,3550,3604"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,1371,1452,1531,1610,1698,1788,1859,1923,2014,2105,2169,2232,2297,2368,2447,2522,2603,2671,2754,2827,2898,2982,3066,3129,3874,3927,3985,4033,4094,4153,4221,4287,4355,4416,4475,4541,4608,4675,4729,4792,4874,4951", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,78,74,80,67,82,72,70,83,83,62,63,52,57,47,60,58,67,65,67,60,58,65,66,66,53,62,81,76,53", "endOffsets": "278,473,651,1447,1526,1605,1693,1783,1854,1918,2009,2100,2164,2227,2292,2363,2442,2517,2598,2666,2749,2822,2893,2977,3061,3124,3188,3922,3980,4028,4089,4148,4216,4282,4350,4411,4470,4536,4603,4670,4724,4787,4869,4946,5000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "656,752,855,953,1051,1154,1259,5005", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "747,850,948,1046,1149,1254,1366,5101"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ur_values-ur.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "675,773,875,977,1081,1184,1282,5006", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "768,870,972,1076,1179,1277,1391,5102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,488,675,763,854,939,1034,1129,1197,1259,1348,1437,1507,1572,1634,1702,1782,1864,1943,2017,2098,2168,2236,2308,2379,2443,2506,2559,2617,2665,2726,2786,2855,2915,2978,3038,3101,3166,3229,3295,3348,3405,3476,3547", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,79,81,78,73,80,69,67,71,70,63,62,52,57,47,60,59,68,59,62,59,62,64,62,65,52,56,70,70,53", "endOffsets": "277,483,670,758,849,934,1029,1124,1192,1254,1343,1432,1502,1567,1629,1697,1777,1859,1938,2012,2093,2163,2231,2303,2374,2438,2501,2554,2612,2660,2721,2781,2850,2910,2973,3033,3096,3161,3224,3290,3343,3400,3471,3542,3596"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,488,1396,1484,1575,1660,1755,1850,1918,1980,2069,2158,2228,2293,2355,2423,2503,2585,2664,2738,2819,2889,2957,3029,3100,3164,3911,3964,4022,4070,4131,4191,4260,4320,4383,4443,4506,4571,4634,4700,4753,4810,4881,4952", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,79,81,78,73,80,69,67,71,70,63,62,52,57,47,60,59,68,59,62,59,62,64,62,65,52,56,70,70,53", "endOffsets": "277,483,670,1479,1570,1655,1750,1845,1913,1975,2064,2153,2223,2288,2350,2418,2498,2580,2659,2733,2814,2884,2952,3024,3095,3159,3222,3959,4017,4065,4126,4186,4255,4315,4378,4438,4501,4566,4629,4695,4748,4805,4876,4947,5001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,194,266,336,413,484,575,660", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "122,189,261,331,408,479,570,655,734"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3227,3299,3366,3438,3508,3585,3656,3747,3832", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "3294,3361,3433,3503,3580,3651,3742,3827,3906"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ta_values-ta.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,700,783,866,947,1052,1156,1235,1301,1397,1494,1565,1630,1692,1764,1856,1946,2038,2107,2191,2264,2344,2433,2520,2587,2655,2708,2771,2819,2880,2947,3012,3073,3142,3205,3268,3334,3397,3464,3518,3582,3660,3738", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,82,80,104,103,78,65,95,96,70,64,61,71,91,89,91,68,83,72,79,88,86,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,778,861,942,1047,1151,1230,1296,1392,1489,1560,1625,1687,1759,1851,1941,2033,2102,2186,2259,2339,2428,2515,2582,2650,2703,2766,2814,2875,2942,3007,3068,3137,3200,3263,3329,3392,3459,3513,3577,3655,3733,3789"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,1446,1529,1612,1693,1798,1902,1981,2047,2143,2240,2311,2376,2438,2510,2602,2692,2784,2853,2937,3010,3090,3179,3266,3333,4109,4162,4225,4273,4334,4401,4466,4527,4596,4659,4722,4788,4851,4918,4972,5036,5114,5192", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,82,80,104,103,78,65,95,96,70,64,61,71,91,89,91,68,83,72,79,88,86,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,1524,1607,1688,1793,1897,1976,2042,2138,2235,2306,2371,2433,2505,2597,2687,2779,2848,2932,3005,3085,3174,3261,3328,3396,4157,4220,4268,4329,4396,4461,4522,4591,4654,4717,4783,4846,4913,4967,5031,5109,5187,5243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,197,266,336,418,499,596,681", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "119,192,261,331,413,494,591,676,758"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3401,3470,3543,3612,3682,3764,3845,3942,4027", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "3465,3538,3607,3677,3759,3840,3937,4022,4104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "700,796,899,998,1096,1203,1318,5248", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "791,894,993,1091,1198,1313,1441,5344"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-iw_values-iw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,582,875,954,1032,1108,1193,1277,1339,1401,1490,1576,1641,1705,1768,1836,1913,1997,2078,2149,2226,2295,2356,2443,2529,2593,2656,2710,2781,2829,2890,2949,3016,3077,3140,3201,3258,3324,3388,3454,3506,3560,3628,3696", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,76,83,80,70,76,68,60,86,85,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "279,577,870,949,1027,1103,1188,1272,1334,1396,1485,1571,1636,1700,1763,1831,1908,1992,2073,2144,2221,2290,2351,2438,2524,2588,2651,2705,2776,2824,2885,2944,3011,3072,3135,3196,3253,3319,3383,3449,3501,3555,3623,3691,3745"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,582,1572,1651,1729,1805,1890,1974,2036,2098,2187,2273,2338,2402,2465,2533,2610,2694,2775,2846,2923,2992,3053,3140,3226,3290,3967,4021,4092,4140,4201,4260,4327,4388,4451,4512,4569,4635,4699,4765,4817,4871,4939,5007", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,76,83,80,70,76,68,60,86,85,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "279,577,870,1646,1724,1800,1885,1969,2031,2093,2182,2268,2333,2397,2460,2528,2605,2689,2770,2841,2918,2987,3048,3135,3221,3285,3348,4016,4087,4135,4196,4255,4322,4383,4446,4507,4564,4630,4694,4760,4812,4866,4934,5002,5056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3353,3418,3477,3544,3609,3683,3745,3825,3905", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "3413,3472,3539,3604,3678,3740,3820,3900,3962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "875,969,1071,1168,1265,1366,1466,5061", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "964,1066,1163,1260,1361,1461,1567,5157"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-it_values-it.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3331,3402,3463,3535,3605,3681,3747,3834,3919", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "3397,3458,3530,3600,3676,3742,3829,3914,3988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "663,761,863,962,1064,1173,1280,5142", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "756,858,957,1059,1168,1275,1405,5238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,663,749,837,916,1008,1100,1178,1243,1343,1441,1506,1574,1639,1710,1787,1870,1945,2015,2108,2183,2259,2355,2447,2516,2584,2637,2695,2743,2804,2878,2949,3012,3093,3151,3212,3278,3343,3409,3461,3523,3599,3675", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,76,82,74,69,92,74,75,95,91,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "281,470,658,744,832,911,1003,1095,1173,1238,1338,1436,1501,1569,1634,1705,1782,1865,1940,2010,2103,2178,2254,2350,2442,2511,2579,2632,2690,2738,2799,2873,2944,3007,3088,3146,3207,3273,3338,3404,3456,3518,3594,3670,3728"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,1410,1496,1584,1663,1755,1847,1925,1990,2090,2188,2253,2321,2386,2457,2534,2617,2692,2762,2855,2930,3006,3102,3194,3263,3993,4046,4104,4152,4213,4287,4358,4421,4502,4560,4621,4687,4752,4818,4870,4932,5008,5084", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,76,82,74,69,92,74,75,95,91,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "281,470,658,1491,1579,1658,1750,1842,1920,1985,2085,2183,2248,2316,2381,2452,2529,2612,2687,2757,2850,2925,3001,3097,3189,3258,3326,4041,4099,4147,4208,4282,4353,4416,4497,4555,4616,4682,4747,4813,4865,4927,5003,5079,5137"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ja_values-ja.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "604,696,796,890,986,1079,1172,4583", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "691,791,885,981,1074,1167,1268,4679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,604,676,747,816,895,973,1039,1100,1178,1255,1319,1380,1439,1504,1576,1647,1720,1785,1851,1916,1980,2055,2128,2189,2252,2304,2362,2410,2471,2527,2589,2646,2706,2762,2818,2881,2943,3006,3056,3114,3186,3258", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,71,70,72,64,65,64,63,74,72,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "276,439,599,671,742,811,890,968,1034,1095,1173,1250,1314,1375,1434,1499,1571,1642,1715,1780,1846,1911,1975,2050,2123,2184,2247,2299,2357,2405,2466,2522,2584,2641,2701,2757,2813,2876,2938,3001,3051,3109,3181,3253,3302"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,1273,1345,1416,1485,1564,1642,1708,1769,1847,1924,1988,2049,2108,2173,2245,2316,2389,2454,2520,2585,2649,2724,2797,2858,3528,3580,3638,3686,3747,3803,3865,3922,3982,4038,4094,4157,4219,4282,4332,4390,4462,4534", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,71,70,72,64,65,64,63,74,72,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "276,439,599,1340,1411,1480,1559,1637,1703,1764,1842,1919,1983,2044,2103,2168,2240,2311,2384,2449,2515,2580,2644,2719,2792,2853,2916,3575,3633,3681,3742,3798,3860,3917,3977,4033,4089,4152,4214,4277,4327,4385,4457,4529,4578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,180,245,307,381,440,520,597", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "115,175,240,302,376,435,515,592,657"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2921,2986,3046,3111,3173,3247,3306,3386,3463", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "2981,3041,3106,3168,3242,3301,3381,3458,3523"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ro_values-ro.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,265,337,415,495,585,678", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "131,195,260,332,410,490,580,673,747"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3501,3582,3646,3711,3783,3861,3941,4031,4124", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "3577,3641,3706,3778,3856,3936,4026,4119,4193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,939,1028,1116,1212,1308,1383,1449,1548,1645,1716,1781,1844,1913,1998,2083,2161,2237,2317,2386,2462,2556,2646,2711,2774,2827,2885,2933,2994,3058,3128,3193,3262,3323,3381,3447,3511,3577,3629,3691,3767,3843", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,934,1023,1111,1207,1303,1378,1444,1543,1640,1711,1776,1839,1908,1993,2078,2156,2232,2312,2381,2457,2551,2641,2706,2769,2822,2880,2928,2989,3053,3123,3188,3257,3318,3376,3442,3506,3572,3624,3686,3762,3838,3895"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,1579,1666,1755,1843,1939,2035,2110,2176,2275,2372,2443,2508,2571,2640,2725,2810,2888,2964,3044,3113,3189,3283,3373,3438,4198,4251,4309,4357,4418,4482,4552,4617,4686,4747,4805,4871,4935,5001,5053,5115,5191,5267", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,1661,1750,1838,1934,2030,2105,2171,2270,2367,2438,2503,2566,2635,2720,2805,2883,2959,3039,3108,3184,3278,3368,3433,3496,4246,4304,4352,4413,4477,4547,4612,4681,4742,4800,4866,4930,4996,5048,5110,5186,5262,5319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "852,950,1052,1152,1251,1353,1462,5324", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "945,1047,1147,1246,1348,1457,1574,5420"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-be_values-be.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "957,1055,1157,1257,1358,1464,1567,5414", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "1050,1152,1252,1353,1459,1562,1683,5510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,627,957,1040,1123,1206,1304,1402,1491,1555,1648,1742,1807,1872,1937,2005,2083,2167,2255,2332,2411,2480,2570,2658,2744,2810,2875,2928,2988,3036,3097,3170,3235,3300,3373,3438,3496,3562,3627,3693,3745,3805,3879,3953", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,77,83,87,76,78,68,89,87,85,65,64,52,59,47,60,72,64,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "283,622,952,1035,1118,1201,1299,1397,1486,1550,1643,1737,1802,1867,1932,2000,2078,2162,2250,2327,2406,2475,2565,2653,2739,2805,2870,2923,2983,3031,3092,3165,3230,3295,3368,3433,3491,3557,3622,3688,3740,3800,3874,3948,4003"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,627,1688,1771,1854,1937,2035,2133,2222,2286,2379,2473,2538,2603,2668,2736,2814,2898,2986,3063,3142,3211,3301,3389,3475,3541,4281,4334,4394,4442,4503,4576,4641,4706,4779,4844,4902,4968,5033,5099,5151,5211,5285,5359", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,77,83,87,76,78,68,89,87,85,65,64,52,59,47,60,72,64,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "283,622,952,1766,1849,1932,2030,2128,2217,2281,2374,2468,2533,2598,2663,2731,2809,2893,2981,3058,3137,3206,3296,3384,3470,3536,3601,4329,4389,4437,4498,4571,4636,4701,4774,4839,4897,4963,5028,5094,5146,5206,5280,5354,5409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,257,320,397,465,564,660", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "121,185,252,315,392,460,559,655,725"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3606,3677,3741,3808,3871,3948,4016,4115,4211", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "3672,3736,3803,3866,3943,4011,4110,4206,4276"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-bn_values-bn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,691,776,860,941,1034,1130,1206,1272,1361,1450,1517,1581,1643,1716,1804,1895,1981,2052,2135,2204,2280,2370,2459,2523,2588,2641,2703,2751,2812,2872,2934,2998,3064,3121,3185,3250,3316,3382,3435,3498,3575,3652", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,87,90,85,70,82,68,75,89,88,63,64,52,61,47,60,59,61,63,65,56,63,64,65,65,52,62,76,76,51", "endOffsets": "283,492,686,771,855,936,1029,1125,1201,1267,1356,1445,1512,1576,1638,1711,1799,1890,1976,2047,2130,2199,2275,2365,2454,2518,2583,2636,2698,2746,2807,2867,2929,2993,3059,3116,3180,3245,3311,3377,3430,3493,3570,3647,3699"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,1420,1505,1589,1670,1763,1859,1935,2001,2090,2179,2246,2310,2372,2445,2533,2624,2710,2781,2864,2933,3009,3099,3188,3252,4076,4129,4191,4239,4300,4360,4422,4486,4552,4609,4673,4738,4804,4870,4923,4986,5063,5140", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,87,90,85,70,82,68,75,89,88,63,64,52,61,47,60,59,61,63,65,56,63,64,65,65,52,62,76,76,51", "endOffsets": "283,492,686,1500,1584,1665,1758,1854,1930,1996,2085,2174,2241,2305,2367,2440,2528,2619,2705,2776,2859,2928,3004,3094,3183,3247,3312,4124,4186,4234,4295,4355,4417,4481,4547,4604,4668,4733,4799,4865,4918,4981,5058,5135,5187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "691,790,892,994,1097,1198,1300,5192", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "785,887,989,1092,1193,1295,1415,5288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3317,3385,3451,3518,3584,3659,3726,3858,3987", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "3380,3446,3513,3579,3654,3721,3853,3982,4071"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-de_values-de.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "692,790,892,992,1092,1200,1305,5162", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "785,887,987,1087,1195,1300,1418,5258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,692,779,867,942,1032,1118,1197,1262,1366,1470,1539,1609,1681,1750,1831,1914,1996,2069,2153,2229,2306,2386,2464,2530,2595,2648,2708,2756,2817,2889,2959,3024,3095,3160,3218,3284,3348,3414,3466,3528,3604,3680", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,80,82,81,72,83,75,76,79,77,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "306,496,687,774,862,937,1027,1113,1192,1257,1361,1465,1534,1604,1676,1745,1826,1909,1991,2064,2148,2224,2301,2381,2459,2525,2590,2643,2703,2751,2812,2884,2954,3019,3090,3155,3213,3279,3343,3409,3461,3523,3599,3675,3731"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,1423,1510,1598,1673,1763,1849,1928,1993,2097,2201,2270,2340,2412,2481,2562,2645,2727,2800,2884,2960,3037,3117,3195,3261,4021,4074,4134,4182,4243,4315,4385,4450,4521,4586,4644,4710,4774,4840,4892,4954,5030,5106", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,80,82,81,72,83,75,76,79,77,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "306,496,687,1505,1593,1668,1758,1844,1923,1988,2092,2196,2265,2335,2407,2476,2557,2640,2722,2795,2879,2955,3032,3112,3190,3256,3321,4069,4129,4177,4238,4310,4380,4445,4516,4581,4639,4705,4769,4835,4887,4949,5025,5101,5157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,270,342,419,486,583,674", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "124,191,265,337,414,481,578,669,745"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3326,3400,3467,3541,3613,3690,3757,3854,3945", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "3395,3462,3536,3608,3685,3752,3849,3940,4016"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-bs_values-bs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,835,921,1008,1096,1194,1301,1371,1438,1534,1626,1691,1764,1827,1895,1970,2047,2124,2204,2288,2359,2430,2514,2596,2668,2738,2791,2849,2897,2958,3030,3097,3161,3232,3296,3355,3420,3485,3556,3608,3675,3756,3837", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,74,76,76,79,83,70,70,83,81,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "288,566,830,916,1003,1091,1189,1296,1366,1433,1529,1621,1686,1759,1822,1890,1965,2042,2119,2199,2283,2354,2425,2509,2591,2663,2733,2786,2844,2892,2953,3025,3092,3156,3227,3291,3350,3415,3480,3551,3603,3670,3751,3832,3888"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,1560,1646,1733,1821,1919,2026,2096,2163,2259,2351,2416,2489,2552,2620,2695,2772,2849,2929,3013,3084,3155,3239,3321,3393,4143,4196,4254,4302,4363,4435,4502,4566,4637,4701,4760,4825,4890,4961,5013,5080,5161,5242", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,74,76,76,79,83,70,70,83,81,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "288,566,830,1641,1728,1816,1914,2021,2091,2158,2254,2346,2411,2484,2547,2615,2690,2767,2844,2924,3008,3079,3150,3234,3316,3388,3458,4191,4249,4297,4358,4430,4497,4561,4632,4696,4755,4820,4885,4956,5008,5075,5156,5237,5293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,581,662", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "125,186,251,324,403,476,576,657,730"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3463,3538,3599,3664,3737,3816,3889,3989,4070", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "3533,3594,3659,3732,3811,3884,3984,4065,4138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "835,933,1035,1133,1237,1341,1443,5298", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "928,1030,1128,1232,1336,1438,1555,5394"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-az_values-az.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,249,314,392,459,548,641", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "120,181,244,309,387,454,543,636,707"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3261,3331,3392,3455,3520,3598,3665,3754,3847", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "3326,3387,3450,3515,3593,3660,3749,3842,3913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,476,660,748,837,914,1006,1094,1170,1234,1325,1416,1481,1546,1608,1676,1760,1848,1930,2001,2082,2152,2228,2315,2399,2468,2534,2587,2645,2693,2754,2818,2890,2949,3012,3075,3135,3201,3265,3331,3383,3441,3513,3585", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,83,87,81,70,80,69,75,86,83,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "280,471,655,743,832,909,1001,1089,1165,1229,1320,1411,1476,1541,1603,1671,1755,1843,1925,1996,2077,2147,2223,2310,2394,2463,2529,2582,2640,2688,2749,2813,2885,2944,3007,3070,3130,3196,3260,3326,3378,3436,3508,3580,3634"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,476,1387,1475,1564,1641,1733,1821,1897,1961,2052,2143,2208,2273,2335,2403,2487,2575,2657,2728,2809,2879,2955,3042,3126,3195,3918,3971,4029,4077,4138,4202,4274,4333,4396,4459,4519,4585,4649,4715,4767,4825,4897,4969", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,83,87,81,70,80,69,75,86,83,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "280,471,655,1470,1559,1636,1728,1816,1892,1956,2047,2138,2203,2268,2330,2398,2482,2570,2652,2723,2804,2874,2950,3037,3121,3190,3256,3966,4024,4072,4133,4197,4269,4328,4391,4454,4514,4580,4644,4710,4762,4820,4892,4964,5018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "660,761,863,966,1070,1171,1276,5023", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "756,858,961,1065,1166,1271,1382,5119"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,686,775,866,945,1043,1140,1219,1285,1382,1479,1544,1607,1671,1743,1827,1914,2005,2080,2168,2241,2321,2415,2505,2571,2635,2688,2746,2794,2855,2922,2999,3066,3138,3196,3255,3321,3386,3452,3504,3569,3648,3727", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,83,86,90,74,87,72,79,93,89,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "280,494,681,770,861,940,1038,1135,1214,1280,1377,1474,1539,1602,1666,1738,1822,1909,2000,2075,2163,2236,2316,2410,2500,2566,2630,2683,2741,2789,2850,2917,2994,3061,3133,3191,3250,3316,3381,3447,3499,3564,3643,3722,3776"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,1409,1498,1589,1668,1766,1863,1942,2008,2105,2202,2267,2330,2394,2466,2550,2637,2728,2803,2891,2964,3044,3138,3228,3294,4088,4141,4199,4247,4308,4375,4452,4519,4591,4649,4708,4774,4839,4905,4957,5022,5101,5180", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,83,86,90,74,87,72,79,93,89,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "280,494,681,1493,1584,1663,1761,1858,1937,2003,2100,2197,2262,2325,2389,2461,2545,2632,2723,2798,2886,2959,3039,3133,3223,3289,3353,4136,4194,4242,4303,4370,4447,4514,4586,4644,4703,4769,4834,4900,4952,5017,5096,5175,5229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "686,784,886,985,1087,1191,1295,5234", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "779,881,980,1082,1186,1290,1404,5330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,272,344,427,503,600,693", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "124,189,267,339,422,498,595,688,780"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3358,3432,3497,3575,3647,3730,3806,3903,3996", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "3427,3492,3570,3642,3725,3801,3898,3991,4083"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-v21_values-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endLines": "2,7", "endColumns": "58,10", "endOffsets": "109,396"}, "to": {"startLines": "3,7", "startColumns": "4,4", "startOffsets": "173,427", "endLines": "3,11", "endColumns": "58,10", "endOffsets": "227,709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,4,5,6,12,13,20,24,28,31", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,232,296,363,714,830,1287,1581,1876,2048", "endLines": "2,4,5,6,12,13,20,24,30,35", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,291,358,422,825,951,1408,1704,2043,2395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b30e4dc937276f023b4a6e07a5156550\\transformed\\media-1.4.3\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "14,17,21,25", "startColumns": "4,4,4,4", "startOffsets": "956,1124,1413,1709", "endLines": "16,19,23,27", "endColumns": "12,12,12,12", "endOffsets": "1119,1282,1576,1871"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-hy_values-hy.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,190,254,322,403,480,554,631", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "122,185,249,317,398,475,549,626,704"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3244,3316,3379,3443,3511,3592,3669,3743,3820", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "3311,3374,3438,3506,3587,3664,3738,3815,3893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "659,759,864,962,1061,1166,1268,5039", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "754,859,957,1056,1161,1263,1374,5135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,477,659,742,824,894,985,1081,1157,1220,1321,1424,1494,1562,1630,1696,1774,1846,1922,1986,2067,2144,2222,2307,2390,2459,2524,2577,2637,2685,2746,2814,2882,2955,3022,3083,3144,3211,3276,3346,3398,3460,3536,3612", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,77,71,75,63,80,76,77,84,82,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "283,472,654,737,819,889,980,1076,1152,1215,1316,1419,1489,1557,1625,1691,1769,1841,1917,1981,2062,2139,2217,2302,2385,2454,2519,2572,2632,2680,2741,2809,2877,2950,3017,3078,3139,3206,3271,3341,3393,3455,3531,3607,3660"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,477,1379,1462,1544,1614,1705,1801,1877,1940,2041,2144,2214,2282,2350,2416,2494,2566,2642,2706,2787,2864,2942,3027,3110,3179,3898,3951,4011,4059,4120,4188,4256,4329,4396,4457,4518,4585,4650,4720,4772,4834,4910,4986", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,77,71,75,63,80,76,77,84,82,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "283,472,654,1457,1539,1609,1700,1796,1872,1935,2036,2139,2209,2277,2345,2411,2489,2561,2637,2701,2782,2859,2937,3022,3105,3174,3239,3946,4006,4054,4115,4183,4251,4324,4391,4452,4513,4580,4645,4715,4767,4829,4905,4981,5034"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,689,769,837,915,992,1048,1109,1183,1257,1319,1380,1439,1504,1577,1647,1720,1783,1850,1915,1971,2044,2116,2177,2240,2292,2350,2397,2458,2515,2577,2634,2695,2751,2806,2869,2931,2994,3043,3095,3161,3227", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,72,69,72,62,66,64,55,72,71,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "282,445,603,684,764,832,910,987,1043,1104,1178,1252,1314,1375,1434,1499,1572,1642,1715,1778,1845,1910,1966,2039,2111,2172,2235,2287,2345,2392,2453,2510,2572,2629,2690,2746,2801,2864,2926,2989,3038,3090,3156,3222,3271"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,1269,1350,1430,1498,1576,1653,1709,1770,1844,1918,1980,2041,2100,2165,2238,2308,2381,2444,2511,2576,2632,2705,2777,2838,3460,3512,3570,3617,3678,3735,3797,3854,3915,3971,4026,4089,4151,4214,4263,4315,4381,4447", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,72,69,72,62,66,64,55,72,71,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "282,445,603,1345,1425,1493,1571,1648,1704,1765,1839,1913,1975,2036,2095,2160,2233,2303,2376,2439,2506,2571,2627,2700,2772,2833,2896,3507,3565,3612,3673,3730,3792,3849,3910,3966,4021,4084,4146,4209,4258,4310,4376,4442,4491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,224,277,347,401,477,555", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "106,162,219,272,342,396,472,550,609"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2901,2957,3013,3070,3123,3193,3247,3323,3401", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "2952,3008,3065,3118,3188,3242,3318,3396,3455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "608,700,799,893,987,1080,1173,4496", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "695,794,888,982,1075,1168,1264,4592"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-as_values-as.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-kk_values-kk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,184,252,316,401,488,585,681", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "115,179,247,311,396,483,580,676,754"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3318,3383,3447,3515,3579,3664,3751,3848,3944", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "3378,3442,3510,3574,3659,3746,3843,3939,4017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,483,677,757,837,924,1021,1118,1203,1268,1364,1461,1528,1593,1659,1729,1811,1897,1978,2054,2130,2204,2290,2382,2472,2538,2604,2657,2717,2765,2826,2886,2953,3018,3083,3146,3203,3275,3340,3406,3458,3519,3601,3683", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,81,85,80,75,75,73,85,91,89,65,65,52,59,47,60,59,66,64,64,62,56,71,64,65,51,60,81,81,54", "endOffsets": "281,478,672,752,832,919,1016,1113,1198,1263,1359,1456,1523,1588,1654,1724,1806,1892,1973,2049,2125,2199,2285,2377,2467,2533,2599,2652,2712,2760,2821,2881,2948,3013,3078,3141,3198,3270,3335,3401,3453,3514,3596,3678,3733"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,483,1391,1471,1551,1638,1735,1832,1917,1982,2078,2175,2242,2307,2373,2443,2525,2611,2692,2768,2844,2918,3004,3096,3186,3252,4022,4075,4135,4183,4244,4304,4371,4436,4501,4564,4621,4693,4758,4824,4876,4937,5019,5101", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,81,85,80,75,75,73,85,91,89,65,65,52,59,47,60,59,66,64,64,62,56,71,64,65,51,60,81,81,54", "endOffsets": "281,478,672,1466,1546,1633,1730,1827,1912,1977,2073,2170,2237,2302,2368,2438,2520,2606,2687,2763,2839,2913,2999,3091,3181,3247,3313,4070,4130,4178,4239,4299,4366,4431,4496,4559,4616,4688,4753,4819,4871,4932,5014,5096,5151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "677,772,874,976,1079,1183,1280,5156", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "767,869,971,1074,1178,1275,1386,5252"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-sv_values-sv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,255,330,411,485,579,665", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "123,186,250,325,406,480,574,660,733"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3247,3320,3383,3447,3522,3603,3677,3771,3857", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "3315,3378,3442,3517,3598,3672,3766,3852,3925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,674,760,847,930,1018,1102,1170,1234,1332,1430,1495,1563,1629,1702,1779,1856,1931,2006,2088,2164,2232,2314,2390,2455,2519,2572,2632,2680,2741,2805,2876,2940,3005,3070,3129,3194,3258,3324,3376,3436,3519,3602", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,76,76,74,74,81,75,67,81,75,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "280,475,669,755,842,925,1013,1097,1165,1229,1327,1425,1490,1558,1624,1697,1774,1851,1926,2001,2083,2159,2227,2309,2385,2450,2514,2567,2627,2675,2736,2800,2871,2935,3000,3065,3124,3189,3253,3319,3371,3431,3514,3597,3649"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,1402,1488,1575,1658,1746,1830,1898,1962,2060,2158,2223,2291,2357,2430,2507,2584,2659,2734,2816,2892,2960,3042,3118,3183,3930,3983,4043,4091,4152,4216,4287,4351,4416,4481,4540,4605,4669,4735,4787,4847,4930,5013", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,76,76,74,74,81,75,67,81,75,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "280,475,669,1483,1570,1653,1741,1825,1893,1957,2055,2153,2218,2286,2352,2425,2502,2579,2654,2729,2811,2887,2955,3037,3113,3178,3242,3978,4038,4086,4147,4211,4282,4346,4411,4476,4535,4600,4664,4730,4782,4842,4925,5008,5060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "674,769,871,969,1068,1176,1281,5065", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "764,866,964,1063,1171,1276,1397,5161"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-nb_values-nb.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,672,758,843,921,1007,1095,1170,1234,1327,1418,1491,1558,1624,1694,1770,1851,1925,1998,2081,2157,2230,2322,2413,2477,2542,2595,2653,2701,2762,2832,2900,2966,3036,3100,3159,3223,3288,3354,3406,3466,3540,3614", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,75,80,73,72,82,75,72,91,90,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "280,477,667,753,838,916,1002,1090,1165,1229,1322,1413,1486,1553,1619,1689,1765,1846,1920,1993,2076,2152,2225,2317,2408,2472,2537,2590,2648,2696,2757,2827,2895,2961,3031,3095,3154,3218,3283,3349,3401,3461,3535,3609,3662"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,1398,1484,1569,1647,1733,1821,1896,1960,2053,2144,2217,2284,2350,2420,2496,2577,2651,2724,2807,2883,2956,3048,3139,3203,3961,4014,4072,4120,4181,4251,4319,4385,4455,4519,4578,4642,4707,4773,4825,4885,4959,5033", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,75,80,73,72,82,75,72,91,90,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "280,477,667,1479,1564,1642,1728,1816,1891,1955,2048,2139,2212,2279,2345,2415,2491,2572,2646,2719,2802,2878,2951,3043,3134,3198,3263,4009,4067,4115,4176,4246,4314,4380,4450,4514,4573,4637,4702,4768,4820,4880,4954,5028,5081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "672,766,868,965,1064,1172,1278,5086", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "761,863,960,1059,1167,1273,1393,5182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3268,3344,3406,3470,3541,3621,3699,3793,3890", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "3339,3401,3465,3536,3616,3694,3788,3885,3956"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-v24_values-v24.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b30e4dc937276f023b4a6e07a5156550\\transformed\\media-1.4.3\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,121,182,248", "endColumns": "65,60,65,66", "endOffsets": "116,177,243,310"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-hi_values-hi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,699,782,863,946,1041,1141,1210,1273,1359,1445,1510,1574,1638,1706,1785,1867,1945,2018,2102,2171,2240,2322,2404,2471,2534,2587,2649,2703,2764,2824,2891,2954,3024,3085,3147,3213,3276,3343,3403,3463,3537,3611", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,78,81,77,72,83,68,68,81,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "281,488,694,777,858,941,1036,1136,1205,1268,1354,1440,1505,1569,1633,1701,1780,1862,1940,2013,2097,2166,2235,2317,2399,2466,2529,2582,2644,2698,2759,2819,2886,2949,3019,3080,3142,3208,3271,3338,3398,3458,3532,3606,3659"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,1452,1535,1616,1699,1794,1894,1963,2026,2112,2198,2263,2327,2391,2459,2538,2620,2698,2771,2855,2924,2993,3075,3157,3224,4055,4108,4170,4224,4285,4345,4412,4475,4545,4606,4668,4734,4797,4864,4924,4984,5058,5132", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,78,81,77,72,83,68,68,81,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "281,488,694,1530,1611,1694,1789,1889,1958,2021,2107,2193,2258,2322,2386,2454,2533,2615,2693,2766,2850,2919,2988,3070,3152,3219,3282,4103,4165,4219,4280,4340,4407,4470,4540,4601,4663,4729,4792,4859,4919,4979,5053,5127,5180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3287,3355,3421,3492,3560,3656,3724,3847,3968", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "3350,3416,3487,3555,3651,3719,3842,3963,4050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "699,797,900,1005,1106,1219,1325,5185", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "792,895,1000,1101,1214,1320,1447,5281"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-cs_values-cs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3543,3619,3681,3744,3813,3890,3960,4042,4122", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "3614,3676,3739,3808,3885,3955,4037,4117,4197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2036,2111,2190,2264,2344,2416,2497,2589,2680,2747,2812,2865,2923,2971,3032,3098,3165,3228,3295,3360,3419,3484,3548,3614,3666,3729,3806,3883", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,76,74,78,73,79,71,80,91,90,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2031,2106,2185,2259,2339,2411,2492,2584,2675,2742,2807,2860,2918,2966,3027,3093,3160,3223,3290,3355,3414,3479,3543,3609,3661,3724,3801,3878,3932"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,1655,1736,1816,1894,1996,2094,2172,2236,2325,2417,2487,2553,2618,2690,2767,2842,2921,2995,3075,3147,3228,3320,3411,3478,4202,4255,4313,4361,4422,4488,4555,4618,4685,4750,4809,4874,4938,5004,5056,5119,5196,5273", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,76,74,78,73,79,71,80,91,90,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1731,1811,1889,1991,2089,2167,2231,2320,2412,2482,2548,2613,2685,2762,2837,2916,2990,3070,3142,3223,3315,3406,3473,3538,4250,4308,4356,4417,4483,4550,4613,4680,4745,4804,4869,4933,4999,5051,5114,5191,5268,5322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "924,1022,1124,1225,1324,1429,1536,5327", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "1017,1119,1220,1319,1424,1531,1650,5423"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,678,747,815,892,968,1022,1084,1158,1232,1294,1355,1414,1480,1551,1621,1692,1755,1822,1887,1941,2016,2090,2151,2213,2265,2323,2370,2431,2488,2550,2607,2668,2724,2779,2842,2904,2967,3016,3067,3132,3197", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,70,69,70,62,66,64,53,74,73,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "282,445,603,673,742,810,887,963,1017,1079,1153,1227,1289,1350,1409,1475,1546,1616,1687,1750,1817,1882,1936,2011,2085,2146,2208,2260,2318,2365,2426,2483,2545,2602,2663,2719,2774,2837,2899,2962,3011,3062,3127,3192,3241"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,1272,1342,1411,1479,1556,1632,1686,1748,1822,1896,1958,2019,2078,2144,2215,2285,2356,2419,2486,2551,2605,2680,2754,2815,3438,3490,3548,3595,3656,3713,3775,3832,3893,3949,4004,4067,4129,4192,4241,4292,4357,4422", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,70,69,70,62,66,64,53,74,73,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "282,445,603,1337,1406,1474,1551,1627,1681,1743,1817,1891,1953,2014,2073,2139,2210,2280,2351,2414,2481,2546,2600,2675,2749,2810,2872,3485,3543,3590,3651,3708,3770,3827,3888,3944,3999,4062,4124,4187,4236,4287,4352,4417,4466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "608,700,801,895,989,1082,1176,4471", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "695,796,890,984,1077,1171,1267,4567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,479,557", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "106,162,220,273,345,399,474,552,611"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2877,2933,2989,3047,3100,3172,3226,3301,3379", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "2928,2984,3042,3095,3167,3221,3296,3374,3433"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-te_values-te.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "695,797,905,1007,1108,1214,1321,5238", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "792,900,1002,1103,1209,1316,1440,5334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,695,787,879,969,1069,1171,1248,1313,1405,1497,1568,1638,1699,1769,1861,1950,2040,2115,2199,2274,2345,2433,2520,2584,2663,2716,2774,2822,2883,2950,3012,3077,3144,3203,3265,3331,3395,3462,3516,3576,3650,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,91,88,89,74,83,74,70,87,86,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,782,874,964,1064,1166,1243,1308,1400,1492,1563,1633,1694,1764,1856,1945,2035,2110,2194,2269,2340,2428,2515,2579,2658,2711,2769,2817,2878,2945,3007,3072,3139,3198,3260,3326,3390,3457,3511,3571,3645,3719,3773"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,1445,1537,1629,1719,1819,1921,1998,2063,2155,2247,2318,2388,2449,2519,2611,2700,2790,2865,2949,3024,3095,3183,3270,3334,4123,4176,4234,4282,4343,4410,4472,4537,4604,4663,4725,4791,4855,4922,4976,5036,5110,5184", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,91,88,89,74,83,74,70,87,86,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,1532,1624,1714,1814,1916,1993,2058,2150,2242,2313,2383,2444,2514,2606,2695,2785,2860,2944,3019,3090,3178,3265,3329,3408,4171,4229,4277,4338,4405,4467,4532,4599,4658,4720,4786,4850,4917,4971,5031,5105,5179,5233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3413,3485,3553,3626,3694,3774,3851,3952,4045", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "3480,3548,3621,3689,3769,3846,3947,4040,4118"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ca_values-ca.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "666,762,864,963,1060,1166,1271,5200", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "757,859,958,1055,1161,1266,1392,5296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,487,666,752,840,923,1022,1120,1201,1267,1380,1490,1563,1632,1698,1769,1846,1931,2008,2077,2165,2240,2322,2419,2512,2576,2640,2693,2751,2799,2860,2925,2994,3059,3131,3195,3252,3318,3382,3448,3501,3561,3635,3709", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,76,84,76,68,87,74,81,96,92,63,63,52,57,47,60,64,68,64,71,63,56,65,63,65,52,59,73,73,56", "endOffsets": "280,482,661,747,835,918,1017,1115,1196,1262,1375,1485,1558,1627,1693,1764,1841,1926,2003,2072,2160,2235,2317,2414,2507,2571,2635,2688,2746,2794,2855,2920,2989,3054,3126,3190,3247,3313,3377,3443,3496,3556,3630,3704,3761"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,487,1397,1483,1571,1654,1753,1851,1932,1998,2111,2221,2294,2363,2429,2500,2577,2662,2739,2808,2896,2971,3053,3150,3243,3307,4074,4127,4185,4233,4294,4359,4428,4493,4565,4629,4686,4752,4816,4882,4935,4995,5069,5143", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,76,84,76,68,87,74,81,96,92,63,63,52,57,47,60,64,68,64,71,63,56,65,63,65,52,59,73,73,56", "endOffsets": "280,482,661,1478,1566,1649,1748,1846,1927,1993,2106,2216,2289,2358,2424,2495,2572,2657,2734,2803,2891,2966,3048,3145,3238,3302,3366,4122,4180,4228,4289,4354,4423,4488,4560,4624,4681,4747,4811,4877,4930,4990,5064,5138,5195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,261,331,407,483,581,676", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "128,187,256,326,402,478,576,671,753"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3371,3449,3508,3577,3647,3723,3799,3897,3992", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "3444,3503,3572,3642,3718,3794,3892,3987,4069"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ms_values-ms.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3219,3289,3353,3419,3484,3562,3628,3718,3801", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "3284,3348,3414,3479,3557,3623,3713,3796,3873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1744,1821,1896,1963,2045,2115,2186,2266,2346,2413,2476,2529,2587,2635,2696,2760,2822,2883,2949,3012,3071,3137,3201,3267,3319,3381,3457,3533", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,75,76,74,66,81,69,70,79,79,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1739,1816,1891,1958,2040,2110,2181,2261,2341,2408,2471,2524,2582,2630,2691,2755,2817,2878,2944,3007,3066,3132,3196,3262,3314,3376,3452,3528,3590"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,1386,1469,1553,1630,1721,1814,1887,1956,2052,2146,2210,2273,2338,2411,2487,2564,2639,2706,2788,2858,2929,3009,3089,3156,3878,3931,3989,4037,4098,4162,4224,4285,4351,4414,4473,4539,4603,4669,4721,4783,4859,4935", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,75,76,74,66,81,69,70,79,79,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "279,466,638,1464,1548,1625,1716,1809,1882,1951,2047,2141,2205,2268,2333,2406,2482,2559,2634,2701,2783,2853,2924,3004,3084,3151,3214,3926,3984,4032,4093,4157,4219,4280,4346,4409,4468,4534,4598,4664,4716,4778,4854,4930,4992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "643,738,840,937,1047,1153,1271,4997", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "733,835,932,1042,1148,1266,1381,5093"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-hr_values-hr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3486,3561,3622,3687,3760,3839,3912,3997,4079", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "3556,3617,3682,3755,3834,3907,3992,4074,4147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "864,962,1069,1166,1265,1369,1473,5346", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "957,1064,1161,1260,1364,1468,1585,5442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,1987,2067,2144,2212,2296,2367,2438,2534,2628,2695,2760,2813,2871,2919,2980,3054,3133,3209,3283,3347,3406,3477,3542,3613,3665,3728,3813,3898", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,74,79,76,67,83,70,70,95,93,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,1982,2062,2139,2207,2291,2362,2433,2529,2623,2690,2755,2808,2866,2914,2975,3049,3128,3204,3278,3342,3401,3472,3537,3608,3660,3723,3808,3893,3949"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,1590,1671,1753,1833,1940,2047,2117,2184,2275,2367,2432,2503,2566,2638,2713,2793,2870,2938,3022,3093,3164,3260,3354,3421,4152,4205,4263,4311,4372,4446,4525,4601,4675,4739,4798,4869,4934,5005,5057,5120,5205,5290", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,74,79,76,67,83,70,70,95,93,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "290,580,859,1666,1748,1828,1935,2042,2112,2179,2270,2362,2427,2498,2561,2633,2708,2788,2865,2933,3017,3088,3159,3255,3349,3416,3481,4200,4258,4306,4367,4441,4520,4596,4670,4734,4793,4864,4929,5000,5052,5115,5200,5285,5341"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-pt_values-pt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3261,3331,3401,3473,3539,3616,3683,3784,3877", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "3326,3396,3468,3534,3611,3678,3779,3872,3942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1763,1839,1915,1982,2068,2144,2218,2310,2398,2462,2526,2579,2637,2685,2746,2811,2873,2939,3009,3073,3134,3200,3265,3331,3384,3448,3526,3604", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,76,75,75,66,85,75,73,91,87,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1758,1834,1910,1977,2063,2139,2213,2305,2393,2457,2521,2574,2632,2680,2741,2806,2868,2934,3004,3068,3129,3195,3260,3326,3379,3443,3521,3599,3658"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,1385,1466,1549,1622,1721,1817,1891,1957,2053,2148,2214,2283,2350,2421,2498,2574,2650,2717,2803,2879,2953,3045,3133,3197,3947,4000,4058,4106,4167,4232,4294,4360,4430,4494,4555,4621,4686,4752,4805,4869,4947,5025", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,76,75,75,66,85,75,73,91,87,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "280,466,645,1461,1544,1617,1716,1812,1886,1952,2048,2143,2209,2278,2345,2416,2493,2569,2645,2712,2798,2874,2948,3040,3128,3192,3256,3995,4053,4101,4162,4227,4289,4355,4425,4489,4550,4616,4681,4747,4800,4864,4942,5020,5079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "650,747,849,948,1048,1155,1265,5084", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "742,844,943,1043,1150,1260,1380,5180"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-sr_values-sr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3423,3497,3558,3623,3694,3772,3844,3931,4014", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "3492,3553,3618,3689,3767,3839,3926,4009,4082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,898,980,1062,1151,1242,1312,1378,1471,1565,1633,1697,1760,1832,1907,1991,2068,2144,2231,2304,2375,2471,2565,2632,2697,2750,2808,2856,2917,2983,3047,3110,3175,3239,3300,3366,3431,3497,3549,3611,3687,3763", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,74,83,76,75,86,72,70,95,93,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,893,975,1057,1146,1237,1307,1373,1466,1560,1628,1692,1755,1827,1902,1986,2063,2139,2226,2299,2370,2466,2560,2627,2692,2745,2803,2851,2912,2978,3042,3105,3170,3234,3295,3361,3426,3492,3544,3606,3682,3758,3814"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,1543,1624,1706,1788,1877,1968,2038,2104,2197,2291,2359,2423,2486,2558,2633,2717,2794,2870,2957,3030,3101,3197,3291,3358,4087,4140,4198,4246,4307,4373,4437,4500,4565,4629,4690,4756,4821,4887,4939,5001,5077,5153", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,74,83,76,75,86,72,70,95,93,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,1619,1701,1783,1872,1963,2033,2099,2192,2286,2354,2418,2481,2553,2628,2712,2789,2865,2952,3025,3096,3192,3286,3353,3418,4135,4193,4241,4302,4368,4432,4495,4560,4624,4685,4751,4816,4882,4934,4996,5072,5148,5204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "817,915,1017,1114,1218,1322,1427,5209", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "910,1012,1109,1213,1317,1422,1538,5305"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ka_values-ka.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "673,769,871,970,1069,1175,1279,5168", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "764,866,965,1064,1170,1274,1392,5264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,483,673,757,841,920,1018,1120,1205,1270,1369,1468,1533,1598,1662,1729,1810,1893,1973,2048,2127,2201,2286,2377,2465,2532,2598,2651,2712,2760,2821,2887,2966,3030,3098,3162,3223,3289,3355,3421,3473,3535,3611,3687", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,80,82,79,74,78,73,84,90,87,66,65,52,60,47,60,65,78,63,67,63,60,65,65,65,51,61,75,75,52", "endOffsets": "285,478,668,752,836,915,1013,1115,1200,1265,1364,1463,1528,1593,1657,1724,1805,1888,1968,2043,2122,2196,2281,2372,2460,2527,2593,2646,2707,2755,2816,2882,2961,3025,3093,3157,3218,3284,3350,3416,3468,3530,3606,3682,3735"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,483,1397,1481,1565,1644,1742,1844,1929,1994,2093,2192,2257,2322,2386,2453,2534,2617,2697,2772,2851,2925,3010,3101,3189,3256,4026,4079,4140,4188,4249,4315,4394,4458,4526,4590,4651,4717,4783,4849,4901,4963,5039,5115", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,80,82,79,74,78,73,84,90,87,66,65,52,60,47,60,65,78,63,67,63,60,65,65,65,51,61,75,75,52", "endOffsets": "285,478,668,1476,1560,1639,1737,1839,1924,1989,2088,2187,2252,2317,2381,2448,2529,2612,2692,2767,2846,2920,3005,3096,3184,3251,3317,4074,4135,4183,4244,4310,4389,4453,4521,4585,4646,4712,4778,4844,4896,4958,5034,5110,5163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,192,270,343,424,499,587,674", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "122,187,265,338,419,494,582,669,754"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3322,3394,3459,3537,3610,3691,3766,3854,3941", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "3389,3454,3532,3605,3686,3761,3849,3936,4021"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ky_values-ky.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "676,776,878,981,1088,1192,1296,5136", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "771,873,976,1083,1187,1291,1402,5232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,484,676,768,858,936,1026,1123,1210,1276,1371,1467,1535,1601,1666,1736,1816,1894,1975,2047,2128,2200,2288,2370,2452,2519,2585,2638,2699,2747,2808,2881,2957,3017,3087,3145,3202,3268,3333,3399,3451,3510,3586,3662", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,89,77,89,96,86,65,94,95,67,65,64,69,79,77,80,71,80,71,87,81,81,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "280,479,671,763,853,931,1021,1118,1205,1271,1366,1462,1530,1596,1661,1731,1811,1889,1970,2042,2123,2195,2283,2365,2447,2514,2580,2633,2694,2742,2803,2876,2952,3012,3082,3140,3197,3263,3328,3394,3446,3505,3581,3657,3712"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,484,1407,1499,1589,1667,1757,1854,1941,2007,2102,2198,2266,2332,2397,2467,2547,2625,2706,2778,2859,2931,3019,3101,3183,3250,4004,4057,4118,4166,4227,4300,4376,4436,4506,4564,4621,4687,4752,4818,4870,4929,5005,5081", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,91,89,77,89,96,86,65,94,95,67,65,64,69,79,77,80,71,80,71,87,81,81,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "280,479,671,1494,1584,1662,1752,1849,1936,2002,2097,2193,2261,2327,2392,2462,2542,2620,2701,2773,2854,2926,3014,3096,3178,3245,3311,4052,4113,4161,4222,4295,4371,4431,4501,4559,4616,4682,4747,4813,4865,4924,5000,5076,5131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,262,333,420,491,578,662", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "121,186,257,328,415,486,573,657,738"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3316,3387,3452,3523,3594,3681,3752,3839,3923", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "3382,3447,3518,3589,3676,3747,3834,3918,3999"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-da_values-da.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,750,837,912,997,1084,1155,1219,1317,1413,1485,1550,1616,1686,1762,1839,1913,1986,2066,2142,2211,2295,2378,2441,2509,2562,2620,2668,2729,2799,2871,2939,3013,3077,3136,3200,3270,3336,3388,3449,3525,3600", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,75,76,73,72,79,75,68,83,82,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "280,469,659,745,832,907,992,1079,1150,1214,1312,1408,1480,1545,1611,1681,1757,1834,1908,1981,2061,2137,2206,2290,2373,2436,2504,2557,2615,2663,2724,2794,2866,2934,3008,3072,3131,3195,3265,3331,3383,3444,3520,3595,3648"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,1391,1477,1564,1639,1724,1811,1882,1946,2044,2140,2212,2277,2343,2413,2489,2566,2640,2713,2793,2869,2938,3022,3105,3168,3913,3966,4024,4072,4133,4203,4275,4343,4417,4481,4540,4604,4674,4740,4792,4853,4929,5004", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,75,76,73,72,79,75,68,83,82,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "280,469,659,1472,1559,1634,1719,1806,1877,1941,2039,2135,2207,2272,2338,2408,2484,2561,2635,2708,2788,2864,2933,3017,3100,3163,3231,3961,4019,4067,4128,4198,4270,4338,4412,4476,4535,4599,4669,4735,4787,4848,4924,4999,5052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,189,253,322,399,473,573,664", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "122,184,248,317,394,468,568,659,727"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3236,3308,3370,3434,3503,3580,3654,3754,3845", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "3303,3365,3429,3498,3575,3649,3749,3840,3908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "664,760,862,959,1057,1164,1273,5057", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "755,857,954,1052,1159,1268,1386,5153"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ml_values-ml.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "725,827,930,1032,1136,1239,1340,5277", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "822,925,1027,1131,1234,1335,1457,5373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,294,521,725,826,926,1017,1110,1219,1296,1363,1455,1547,1624,1695,1756,1830,1914,2000,2085,2164,2245,2317,2394,2478,2561,2630,2695,2748,2806,2856,2917,2983,3045,3106,3176,3238,3302,3368,3439,3506,3562,3624,3700,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,83,85,84,78,80,71,76,83,82,68,64,52,57,49,60,65,61,60,69,61,63,65,70,66,55,61,75,75,53", "endOffsets": "289,516,720,821,921,1012,1105,1214,1291,1358,1450,1542,1619,1690,1751,1825,1909,1995,2080,2159,2240,2312,2389,2473,2556,2625,2690,2743,2801,2851,2912,2978,3040,3101,3171,3233,3297,3363,3434,3501,3557,3619,3695,3771,3825"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,294,521,1462,1563,1663,1754,1847,1956,2033,2100,2192,2284,2361,2432,2493,2567,2651,2737,2822,2901,2982,3054,3131,3215,3298,3367,4142,4195,4253,4303,4364,4430,4492,4553,4623,4685,4749,4815,4886,4953,5009,5071,5147,5223", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,83,85,84,78,80,71,76,83,82,68,64,52,57,49,60,65,61,60,69,61,63,65,70,66,55,61,75,75,53", "endOffsets": "289,516,720,1558,1658,1749,1842,1951,2028,2095,2187,2279,2356,2427,2488,2562,2646,2732,2817,2896,2977,3049,3126,3210,3293,3362,3427,4190,4248,4298,4359,4425,4487,4548,4618,4680,4744,4810,4881,4948,5004,5066,5142,5218,5272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,406,487,590,687", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "121,182,254,324,401,482,585,682,760"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3432,3503,3564,3636,3706,3783,3864,3967,4064", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "3498,3559,3631,3701,3778,3859,3962,4059,4137"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-sl_values-sl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "958,1055,1157,1255,1359,1462,1564,5450", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "1050,1152,1250,1354,1457,1559,1676,5546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,958,1045,1133,1216,1314,1415,1498,1563,1660,1754,1825,1895,1959,2027,2108,2189,2269,2346,2426,2499,2579,2675,2769,2837,2902,2955,3013,3061,3122,3192,3261,3324,3389,3452,3509,3585,3654,3728,3780,3843,3920,3997", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,80,80,79,76,79,72,79,95,93,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "318,640,953,1040,1128,1211,1309,1410,1493,1558,1655,1749,1820,1890,1954,2022,2103,2184,2264,2341,2421,2494,2574,2670,2764,2832,2897,2950,3008,3056,3117,3187,3256,3319,3384,3447,3504,3580,3649,3723,3775,3838,3915,3992,4046"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,1681,1768,1856,1939,2037,2138,2221,2286,2383,2477,2548,2618,2682,2750,2831,2912,2992,3069,3149,3222,3302,3398,3492,3560,4301,4354,4412,4460,4521,4591,4660,4723,4788,4851,4908,4984,5053,5127,5179,5242,5319,5396", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,80,80,79,76,79,72,79,95,93,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "318,640,953,1763,1851,1934,2032,2133,2216,2281,2378,2472,2543,2613,2677,2745,2826,2907,2987,3064,3144,3217,3297,3393,3487,3555,3620,4349,4407,4455,4516,4586,4655,4718,4783,4846,4903,4979,5048,5122,5174,5237,5314,5391,5445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,183,247,311,386,467,566,657", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "118,178,242,306,381,462,561,652,726"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3625,3693,3753,3817,3881,3956,4037,4136,4227", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "3688,3748,3812,3876,3951,4032,4131,4222,4296"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-kn_values-kn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "747,845,948,1049,1155,1256,1364,5257", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "840,943,1044,1150,1251,1359,1487,5353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,316,543,747,842,939,1021,1118,1216,1293,1357,1460,1561,1626,1689,1749,1820,1907,1997,2082,2150,2237,2313,2389,2469,2548,2615,2679,2732,2790,2840,2901,2961,3023,3087,3149,3208,3273,3339,3403,3470,3524,3583,3656,3729", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,86,89,84,67,86,75,75,79,78,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "311,538,742,837,934,1016,1113,1211,1288,1352,1455,1556,1621,1684,1744,1815,1902,1992,2077,2145,2232,2308,2384,2464,2543,2610,2674,2727,2785,2835,2896,2956,3018,3082,3144,3203,3268,3334,3398,3465,3519,3578,3651,3724,3778"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,316,543,1492,1587,1684,1766,1863,1961,2038,2102,2205,2306,2371,2434,2494,2565,2652,2742,2827,2895,2982,3058,3134,3214,3293,3360,4153,4206,4264,4314,4375,4435,4497,4561,4623,4682,4747,4813,4877,4944,4998,5057,5130,5203", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,86,89,84,67,86,75,75,79,78,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "311,538,742,1582,1679,1761,1858,1956,2033,2097,2200,2301,2366,2429,2489,2560,2647,2737,2822,2890,2977,3053,3129,3209,3288,3355,3419,4201,4259,4309,4370,4430,4492,4556,4618,4677,4742,4808,4872,4939,4993,5052,5125,5198,5252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,193,270,341,422,504,605,698", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "124,188,265,336,417,499,600,693,779"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3424,3498,3562,3639,3710,3791,3873,3974,4067", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "3493,3557,3634,3705,3786,3868,3969,4062,4148"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-tl_values-tl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "692,789,891,992,1089,1196,1304,5166", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "784,886,987,1084,1191,1299,1421,5262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,692,783,874,954,1039,1130,1208,1274,1375,1478,1545,1610,1672,1743,1820,1899,1979,2048,2135,2209,2289,2379,2467,2532,2596,2649,2707,2755,2816,2881,2943,3008,3076,3140,3198,3264,3328,3394,3446,3508,3587,3666", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,76,78,79,68,86,73,79,89,87,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "280,492,687,778,869,949,1034,1125,1203,1269,1370,1473,1540,1605,1667,1738,1815,1894,1974,2043,2130,2204,2284,2374,2462,2527,2591,2644,2702,2750,2811,2876,2938,3003,3071,3135,3193,3259,3323,3389,3441,3503,3582,3661,3718"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,1426,1517,1608,1688,1773,1864,1942,2008,2109,2212,2279,2344,2406,2477,2554,2633,2713,2782,2869,2943,3023,3113,3201,3266,4039,4092,4150,4198,4259,4324,4386,4451,4519,4583,4641,4707,4771,4837,4889,4951,5030,5109", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,76,78,79,68,86,73,79,89,87,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "280,492,687,1512,1603,1683,1768,1859,1937,2003,2104,2207,2274,2339,2401,2472,2549,2628,2708,2777,2864,2938,3018,3108,3196,3261,3325,4087,4145,4193,4254,4319,4381,4446,4514,4578,4636,4702,4766,4832,4884,4946,5025,5104,5161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,196,264,330,410,488,588,686", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "127,191,259,325,405,483,583,681,759"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3330,3407,3471,3539,3605,3685,3763,3863,3961", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "3402,3466,3534,3600,3680,3758,3858,3956,4034"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-tr_values-tr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,652,741,834,909,994,1080,1155,1221,1306,1392,1460,1522,1582,1651,1731,1816,1896,1965,2049,2119,2195,2278,2358,2423,2487,2540,2598,2646,2707,2771,2838,2900,2966,3028,3085,3149,3214,3280,3332,3392,3466,3540", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,79,84,79,68,83,69,75,82,79,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "280,467,647,736,829,904,989,1075,1150,1216,1301,1387,1455,1517,1577,1646,1726,1811,1891,1960,2044,2114,2190,2273,2353,2418,2482,2535,2593,2641,2702,2766,2833,2895,2961,3023,3080,3144,3209,3275,3327,3387,3461,3535,3592"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,1365,1454,1547,1622,1707,1793,1868,1934,2019,2105,2173,2235,2295,2364,2444,2529,2609,2678,2762,2832,2908,2991,3071,3136,3876,3929,3987,4035,4096,4160,4227,4289,4355,4417,4474,4538,4603,4669,4721,4781,4855,4929", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,79,84,79,68,83,69,75,82,79,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "280,467,647,1449,1542,1617,1702,1788,1863,1929,2014,2100,2168,2230,2290,2359,2439,2524,2604,2673,2757,2827,2903,2986,3066,3131,3195,3924,3982,4030,4091,4155,4222,4284,4350,4412,4469,4533,4598,4664,4716,4776,4850,4924,4981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3200,3277,3336,3401,3462,3542,3614,3704,3800", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "3272,3331,3396,3457,3537,3609,3699,3795,3871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "652,749,851,949,1046,1148,1254,4986", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "744,846,944,1041,1143,1249,1360,5082"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1720,1796,1871,1938,2018,2089,2156,2233,2308,2371,2435,2488,2546,2594,2655,2720,2782,2847,2918,2976,3034,3100,3164,3230,3282,3344,3420,3496", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,74,75,74,66,79,70,66,76,74,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1715,1791,1866,1933,2013,2084,2151,2228,2303,2366,2430,2483,2541,2589,2650,2715,2777,2842,2913,2971,3029,3095,3159,3225,3277,3339,3415,3491,3545"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,1375,1457,1539,1617,1704,1789,1856,1919,2011,2103,2168,2231,2293,2364,2439,2515,2590,2657,2737,2808,2875,2952,3027,3090,3801,3854,3912,3960,4021,4086,4148,4213,4284,4342,4400,4466,4530,4596,4648,4710,4786,4862", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,74,75,74,66,79,70,66,76,74,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,1452,1534,1612,1699,1784,1851,1914,2006,2098,2163,2226,2288,2359,2434,2510,2585,2652,2732,2803,2870,2947,3022,3085,3149,3849,3907,3955,4016,4081,4143,4208,4279,4337,4395,4461,4525,4591,4643,4705,4781,4857,4911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "656,752,854,953,1052,1156,1259,4916", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "747,849,948,1047,1151,1254,1370,5012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3154,3224,3286,3351,3415,3492,3557,3647,3732", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "3219,3281,3346,3410,3487,3552,3642,3727,3796"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-es_values-es.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3293,3376,3439,3504,3578,3655,3722,3809,3895", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "3371,3434,3499,3573,3650,3717,3804,3890,3959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "658,757,859,959,1057,1164,1270,5103", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "752,854,954,1052,1159,1265,1385,5199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1790,1865,1941,2011,2098,2168,2248,2340,2431,2497,2561,2614,2672,2720,2779,2844,2906,2972,3044,3108,3169,3235,3300,3366,3419,3484,3563,3642", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,76,74,75,69,86,69,79,91,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1785,1860,1936,2006,2093,2163,2243,2335,2426,2492,2556,2609,2667,2715,2774,2839,2901,2967,3039,3103,3164,3230,3295,3361,3414,3479,3558,3637,3695"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,1390,1476,1563,1648,1744,1840,1915,1983,2078,2173,2239,2308,2374,2445,2522,2597,2673,2743,2830,2900,2980,3072,3163,3229,3964,4017,4075,4123,4182,4247,4309,4375,4447,4511,4572,4638,4703,4769,4822,4887,4966,5045", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,76,74,75,69,86,69,79,91,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,466,653,1471,1558,1643,1739,1835,1910,1978,2073,2168,2234,2303,2369,2440,2517,2592,2668,2738,2825,2895,2975,3067,3158,3224,3288,4012,4070,4118,4177,4242,4304,4370,4442,4506,4567,4633,4698,4764,4817,4882,4961,5040,5098"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-fr_values-fr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "692,790,892,991,1093,1197,1301,5244", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "785,887,986,1088,1192,1296,1414,5340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1855,1942,2030,2105,2193,2266,2346,2440,2530,2596,2660,2713,2773,2821,2882,2953,3024,3091,3169,3234,3293,3359,3424,3490,3542,3602,3676,3750", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,83,86,87,74,87,72,79,93,89,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1850,1937,2025,2100,2188,2261,2341,2435,2525,2591,2655,2708,2768,2816,2877,2948,3019,3086,3164,3229,3288,3354,3419,3485,3537,3597,3671,3745,3799"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,1419,1508,1599,1678,1776,1873,1952,2018,2124,2231,2296,2362,2426,2498,2582,2669,2757,2832,2920,2993,3073,3167,3257,3323,4100,4153,4213,4261,4322,4393,4464,4531,4609,4674,4733,4799,4864,4930,4982,5042,5116,5190", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,83,86,87,74,87,72,79,93,89,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "286,478,687,1503,1594,1673,1771,1868,1947,2013,2119,2226,2291,2357,2421,2493,2577,2664,2752,2827,2915,2988,3068,3162,3252,3318,3382,4148,4208,4256,4317,4388,4459,4526,4604,4669,4728,4794,4859,4925,4977,5037,5111,5185,5239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3387,3461,3526,3595,3667,3750,3827,3924,4017", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "3456,3521,3590,3662,3745,3822,3919,4012,4095"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-sk_values-sk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2070,2145,2224,2298,2380,2453,2535,2631,2726,2793,2858,2911,2969,3017,3078,3150,3224,3287,3360,3425,3485,3550,3614,3680,3732,3796,3874,3952", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,79,74,78,73,81,72,81,95,94,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2065,2140,2219,2293,2375,2448,2530,2626,2721,2788,2853,2906,2964,3012,3073,3145,3219,3282,3355,3420,3480,3545,3609,3675,3727,3791,3869,3947,4001"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,1675,1756,1836,1918,2021,2120,2199,2264,2355,2449,2519,2585,2650,2727,2807,2882,2961,3035,3117,3190,3272,3368,3463,3530,4266,4319,4377,4425,4486,4558,4632,4695,4768,4833,4893,4958,5022,5088,5140,5204,5282,5360", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,79,74,78,73,81,72,81,95,94,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1751,1831,1913,2016,2115,2194,2259,2350,2444,2514,2580,2645,2722,2802,2877,2956,3030,3112,3185,3267,3363,3458,3525,3590,4314,4372,4420,4481,4553,4627,4690,4763,4828,4888,4953,5017,5083,5135,5199,5277,5355,5409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3595,3672,3734,3798,3869,3946,4020,4104,4186", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "3667,3729,3793,3864,3941,4015,4099,4181,4261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "938,1034,1136,1237,1335,1445,1553,5414", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "1029,1131,1232,1330,1440,1548,1670,5510"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-lo_values-lo.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,185,251,316,391,461,553,640", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "119,180,246,311,386,456,548,635,707"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3173,3242,3303,3369,3434,3509,3579,3671,3758", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "3237,3298,3364,3429,3504,3574,3666,3753,3825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,655,737,817,894,982,1064,1140,1204,1297,1389,1459,1523,1586,1656,1735,1811,1885,1953,2030,2100,2176,2259,2342,2404,2467,2520,2578,2626,2687,2746,2814,2875,2941,3005,3064,3128,3195,3262,3316,3376,3450,3524", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,78,75,73,67,76,69,75,82,82,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "281,472,650,732,812,889,977,1059,1135,1199,1292,1384,1454,1518,1581,1651,1730,1806,1880,1948,2025,2095,2171,2254,2337,2399,2462,2515,2573,2621,2682,2741,2809,2870,2936,3000,3059,3123,3190,3257,3311,3371,3445,3519,3575"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,1361,1443,1523,1600,1688,1770,1846,1910,2003,2095,2165,2229,2292,2362,2441,2517,2591,2659,2736,2806,2882,2965,3048,3110,3830,3883,3941,3989,4050,4109,4177,4238,4304,4368,4427,4491,4558,4625,4679,4739,4813,4887", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,78,75,73,67,76,69,75,82,82,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "281,472,650,1438,1518,1595,1683,1765,1841,1905,1998,2090,2160,2224,2287,2357,2436,2512,2586,2654,2731,2801,2877,2960,3043,3105,3168,3878,3936,3984,4045,4104,4172,4233,4299,4363,4422,4486,4553,4620,4674,4734,4808,4882,4938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "655,751,854,953,1051,1152,1250,4943", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "746,849,948,1046,1147,1245,1356,5039"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3154,3224,3286,3351,3415,3492,3557,3647,3732", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "3219,3281,3346,3410,3487,3552,3642,3727,3796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1720,1796,1871,1938,2018,2089,2156,2233,2308,2371,2435,2488,2546,2594,2655,2720,2782,2847,2918,2976,3034,3100,3164,3230,3282,3344,3420,3496", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,74,75,74,66,79,70,66,76,74,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1715,1791,1866,1933,2013,2084,2151,2228,2303,2366,2430,2483,2541,2589,2650,2715,2777,2842,2913,2971,3029,3095,3159,3225,3277,3339,3415,3491,3545"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,1375,1457,1539,1617,1704,1789,1856,1919,2011,2103,2168,2231,2293,2364,2439,2515,2590,2657,2737,2808,2875,2952,3027,3090,3801,3854,3912,3960,4021,4086,4148,4213,4284,4342,4400,4466,4530,4596,4648,4710,4786,4862", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,74,75,74,66,79,70,66,76,74,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,1452,1534,1612,1699,1784,1851,1914,2006,2098,2163,2226,2288,2359,2434,2510,2585,2652,2732,2803,2870,2947,3022,3085,3149,3849,3907,3955,4016,4081,4143,4208,4279,4337,4395,4461,4525,4591,4643,4705,4781,4857,4911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "656,752,854,953,1052,1156,1259,4916", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "747,849,948,1047,1151,1254,1370,5012"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-eu_values-eu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,253,325,402,476,587,685", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "119,182,248,320,397,471,582,680,748"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3351,3420,3483,3549,3621,3698,3772,3883,3981", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "3415,3478,3544,3616,3693,3767,3878,3976,4044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "657,755,858,958,1061,1166,1269,5203", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "750,853,953,1056,1161,1264,1383,5299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,657,741,827,902,1001,1092,1187,1255,1351,1447,1514,1586,1651,1722,1805,1883,1962,2031,2120,2192,2287,2389,2487,2553,2620,2673,2731,2780,2841,2903,2975,3039,3106,3171,3235,3302,3368,3435,3489,3556,3637,3718", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,82,77,78,68,88,71,94,101,97,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "281,470,652,736,822,897,996,1087,1182,1250,1346,1442,1509,1581,1646,1717,1800,1878,1957,2026,2115,2187,2282,2384,2482,2548,2615,2668,2726,2775,2836,2898,2970,3034,3101,3166,3230,3297,3363,3430,3484,3551,3632,3713,3769"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,1388,1472,1558,1633,1732,1823,1918,1986,2082,2178,2245,2317,2382,2453,2536,2614,2693,2762,2851,2923,3018,3120,3218,3284,4049,4102,4160,4209,4270,4332,4404,4468,4535,4600,4664,4731,4797,4864,4918,4985,5066,5147", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,82,77,78,68,88,71,94,101,97,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "281,470,652,1467,1553,1628,1727,1818,1913,1981,2077,2173,2240,2312,2377,2448,2531,2609,2688,2757,2846,2918,3013,3115,3213,3279,3346,4097,4155,4204,4265,4327,4399,4463,4530,4595,4659,4726,4792,4859,4913,4980,5061,5142,5198"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-or_values-or.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ar_values-ar.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3863,3928,3987,4054,4116,4198,4279,4380,4475", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "3923,3982,4049,4111,4193,4274,4375,4470,4554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "27,28,29,30,31,32,33,85", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1314,1407,1509,1604,1707,1810,1912,5683", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "1402,1504,1599,1702,1805,1907,2021,5779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2395,2471,2556,2622,2705,2777,2849,2937,3024,3088,3151,3204,3275,3330,3391,3449,3523,3587,3651,3711,3776,3840,3902,3968,4020,4077,4148,4219", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,74,75,84,65,82,71,71,87,86,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2390,2466,2551,2617,2700,2772,2844,2932,3019,3083,3146,3199,3270,3325,3386,3444,3518,3582,3646,3706,3771,3835,3897,3963,4015,4072,4143,4214,4270"}, "to": {"startLines": "2,11,19,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,2026,2105,2183,2259,2353,2445,2519,2584,2676,2766,2836,2900,2963,3032,3107,3183,3268,3334,3417,3489,3561,3649,3736,3800,4559,4612,4683,4738,4799,4857,4931,4995,5059,5119,5184,5248,5310,5376,5428,5485,5556,5627", "endLines": "10,18,26,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,74,75,84,65,82,71,71,87,86,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,2100,2178,2254,2348,2440,2514,2579,2671,2761,2831,2895,2958,3027,3102,3178,3263,3329,3412,3484,3556,3644,3731,3795,3858,4607,4678,4733,4794,4852,4926,4990,5054,5114,5179,5243,5305,5371,5423,5480,5551,5622,5678"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-in_values-in.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "646,741,843,940,1037,1143,1261,5001", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "736,838,935,1032,1138,1256,1371,5097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,185,250,313,389,453,553,647", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "118,180,245,308,384,448,548,642,711"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3216,3284,3346,3411,3474,3550,3614,3714,3808", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "3279,3341,3406,3469,3545,3609,3709,3803,3872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,646,729,814,890,978,1071,1148,1217,1313,1407,1471,1535,1601,1674,1751,1829,1902,1974,2054,2124,2198,2280,2355,2422,2486,2539,2597,2645,2706,2770,2832,2895,2961,3023,3086,3152,3216,3282,3334,3396,3472,3548", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,76,77,72,71,79,69,73,81,74,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "280,467,641,724,809,885,973,1066,1143,1212,1308,1402,1466,1530,1596,1669,1746,1824,1897,1969,2049,2119,2193,2275,2350,2417,2481,2534,2592,2640,2701,2765,2827,2890,2956,3018,3081,3147,3211,3277,3329,3391,3467,3543,3605"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,1376,1459,1544,1620,1708,1801,1878,1947,2043,2137,2201,2265,2331,2404,2481,2559,2632,2704,2784,2854,2928,3010,3085,3152,3877,3930,3988,4036,4097,4161,4223,4286,4352,4414,4477,4543,4607,4673,4725,4787,4863,4939", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,76,77,72,71,79,69,73,81,74,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "280,467,641,1454,1539,1615,1703,1796,1873,1942,2038,2132,2196,2260,2326,2399,2476,2554,2627,2699,2779,2849,2923,3005,3080,3147,3211,3925,3983,4031,4092,4156,4218,4281,4347,4409,4472,4538,4602,4668,4720,4782,4858,4934,4996"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-ru_values-ru.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "949,1047,1149,1250,1351,1456,1559,5440", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "1042,1144,1245,1346,1451,1554,1671,5536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,949,1032,1115,1198,1288,1388,1459,1532,1631,1732,1805,1877,1942,2020,2098,2175,2254,2331,2426,2498,2571,2660,2747,2816,2881,2934,2996,3044,3105,3172,3240,3306,3388,3446,3503,3569,3634,3700,3752,3813,3898,3983", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,77,76,78,76,94,71,72,88,86,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "275,614,944,1027,1110,1193,1283,1383,1454,1527,1626,1727,1800,1872,1937,2015,2093,2170,2249,2326,2421,2493,2566,2655,2742,2811,2876,2929,2991,3039,3100,3167,3235,3301,3383,3441,3498,3564,3629,3695,3747,3808,3893,3978,4041"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,1676,1759,1842,1925,2015,2115,2186,2259,2358,2459,2532,2604,2669,2747,2825,2902,2981,3058,3153,3225,3298,3387,3474,3543,4275,4328,4390,4438,4499,4566,4634,4700,4782,4840,4897,4963,5028,5094,5146,5207,5292,5377", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,77,76,78,76,94,71,72,88,86,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "275,614,944,1754,1837,1920,2010,2110,2181,2254,2353,2454,2527,2599,2664,2742,2820,2897,2976,3053,3148,3220,3293,3382,3469,3538,3603,4323,4385,4433,4494,4561,4629,4695,4777,4835,4892,4958,5023,5089,5141,5202,5287,5372,5435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3608,3680,3741,3806,3872,3950,4024,4112,4198", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "3675,3736,3801,3867,3945,4019,4107,4193,4270"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-is_values-is.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "689,784,891,988,1088,1191,1295,5002", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "779,886,983,1083,1186,1290,1401,5098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,689,774,858,935,1024,1121,1190,1254,1345,1436,1499,1563,1625,1693,1772,1853,1932,2007,2088,2161,2230,2312,2393,2458,2538,2591,2652,2702,2763,2822,2892,2955,3017,3081,3141,3207,3272,3342,3394,3454,3528,3602", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,78,80,78,74,80,72,68,81,80,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "283,483,684,769,853,930,1019,1116,1185,1249,1340,1431,1494,1558,1620,1688,1767,1848,1927,2002,2083,2156,2225,2307,2388,2453,2533,2586,2647,2697,2758,2817,2887,2950,3012,3076,3136,3202,3267,3337,3389,3449,3523,3597,3650"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,1406,1491,1575,1652,1741,1838,1907,1971,2062,2153,2216,2280,2342,2410,2489,2570,2649,2724,2805,2878,2947,3029,3110,3175,3885,3938,3999,4049,4110,4169,4239,4302,4364,4428,4488,4554,4619,4689,4741,4801,4875,4949", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,78,80,78,74,80,72,68,81,80,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "283,483,684,1486,1570,1647,1736,1833,1902,1966,2057,2148,2211,2275,2337,2405,2484,2565,2644,2719,2800,2873,2942,3024,3105,3170,3250,3933,3994,4044,4105,4164,4234,4297,4359,4423,4483,4549,4614,4684,4736,4796,4870,4944,4997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3255,3322,3381,3440,3506,3582,3645,3734,3816", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "3317,3376,3435,3501,3577,3640,3729,3811,3880"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-sw_values-sw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "706,800,902,999,1100,1207,1314,5107", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "795,897,994,1095,1202,1309,1424,5203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,177,239,306,384,465,552,634", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "113,172,234,301,379,460,547,629,699"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3315,3378,3437,3499,3566,3644,3725,3812,3894", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "3373,3432,3494,3561,3639,3720,3807,3889,3959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,289,506,706,785,862,947,1037,1125,1201,1267,1360,1455,1522,1586,1647,1722,1797,1877,1952,2026,2107,2180,2258,2352,2446,2514,2592,2645,2703,2751,2812,2873,2940,3004,3070,3133,3192,3258,3327,3393,3445,3511,3594,3677", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,74,79,74,73,80,72,77,93,93,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "284,501,701,780,857,942,1032,1120,1196,1262,1355,1450,1517,1581,1642,1717,1792,1872,1947,2021,2102,2175,2253,2347,2441,2509,2587,2640,2698,2746,2807,2868,2935,2999,3065,3128,3187,3253,3322,3388,3440,3506,3589,3672,3730"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,289,506,1429,1508,1585,1670,1760,1848,1924,1990,2083,2178,2245,2309,2370,2445,2520,2600,2675,2749,2830,2903,2981,3075,3169,3237,3964,4017,4075,4123,4184,4245,4312,4376,4442,4505,4564,4630,4699,4765,4817,4883,4966,5049", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,74,79,74,73,80,72,77,93,93,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "284,501,701,1503,1580,1665,1755,1843,1919,1985,2078,2173,2240,2304,2365,2440,2515,2595,2670,2744,2825,2898,2976,3070,3164,3232,3310,4012,4070,4118,4179,4240,4307,4371,4437,4500,4559,4625,4694,4760,4812,4878,4961,5044,5102"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "656,752,854,953,1052,1156,1259,4916", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "747,849,948,1047,1151,1254,1370,5012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3154,3224,3286,3351,3415,3492,3557,3647,3732", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "3219,3281,3346,3410,3487,3552,3642,3727,3796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1720,1796,1871,1938,2018,2089,2156,2233,2308,2371,2435,2488,2546,2594,2655,2720,2782,2847,2918,2976,3034,3100,3164,3230,3282,3344,3420,3496", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,74,75,74,66,79,70,66,76,74,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1715,1791,1866,1933,2013,2084,2151,2228,2303,2366,2430,2483,2541,2589,2650,2715,2777,2842,2913,2971,3029,3095,3159,3225,3277,3339,3415,3491,3545"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,1375,1457,1539,1617,1704,1789,1856,1919,2011,2103,2168,2231,2293,2364,2439,2515,2590,2657,2737,2808,2875,2952,3027,3090,3801,3854,3912,3960,4021,4086,4148,4213,4284,4342,4400,4466,4530,4596,4648,4710,4786,4862", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,74,75,74,66,79,70,66,76,74,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,1452,1534,1612,1699,1784,1851,1914,2006,2098,2163,2226,2288,2359,2434,2510,2585,2652,2732,2803,2870,2947,3022,3085,3149,3849,3907,3955,4016,4081,4143,4208,4279,4337,4395,4461,4525,4591,4643,4705,4781,4857,4911"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-si_values-si.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "666,768,871,976,1081,1180,1284,5031", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "763,866,971,1076,1175,1279,1393,5127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,488,666,750,833,914,1007,1099,1162,1224,1313,1404,1475,1545,1606,1672,1761,1853,1940,2011,2090,2160,2225,2319,2409,2476,2544,2597,2655,2702,2763,2823,2890,2951,3016,3075,3140,3209,3272,3339,3393,3450,3521,3592", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,88,91,86,70,78,69,64,93,89,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "282,483,661,745,828,909,1002,1094,1157,1219,1308,1399,1470,1540,1601,1667,1756,1848,1935,2006,2085,2155,2220,2314,2404,2471,2539,2592,2650,2697,2758,2818,2885,2946,3011,3070,3135,3204,3267,3334,3388,3445,3516,3587,3639"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,488,1398,1482,1565,1646,1739,1831,1894,1956,2045,2136,2207,2277,2338,2404,2493,2585,2672,2743,2822,2892,2957,3051,3141,3208,3931,3984,4042,4089,4150,4210,4277,4338,4403,4462,4527,4596,4659,4726,4780,4837,4908,4979", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,88,91,86,70,78,69,64,93,89,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "282,483,661,1477,1560,1641,1734,1826,1889,1951,2040,2131,2202,2272,2333,2399,2488,2580,2667,2738,2817,2887,2952,3046,3136,3203,3271,3979,4037,4084,4145,4205,4272,4333,4398,4457,4522,4591,4654,4721,4775,4832,4903,4974,5026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,191,254,320,396,465,554,640", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "126,186,249,315,391,460,549,635,705"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3276,3352,3412,3475,3541,3617,3686,3775,3861", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "3347,3407,3470,3536,3612,3681,3770,3856,3926"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-pa_values-pa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,268,334,409,475,574,670", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "121,186,263,329,404,470,569,665,750"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3260,3331,3396,3473,3539,3614,3680,3779,3875", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "3326,3391,3468,3534,3609,3675,3774,3870,3955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "667,765,867,970,1071,1173,1271,5066", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "760,862,965,1066,1168,1266,1395,5162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,487,667,756,843,926,1017,1111,1182,1245,1336,1427,1491,1554,1614,1682,1764,1847,1926,1996,2072,2143,2214,2306,2398,2464,2527,2580,2638,2686,2747,2807,2879,2941,3003,3064,3126,3191,3255,3321,3373,3433,3507,3581", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,81,82,78,69,75,70,70,91,91,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "279,482,662,751,838,921,1012,1106,1177,1240,1331,1422,1486,1549,1609,1677,1759,1842,1921,1991,2067,2138,2209,2301,2393,2459,2522,2575,2633,2681,2742,2802,2874,2936,2998,3059,3121,3186,3250,3316,3368,3428,3502,3576,3628"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,487,1400,1489,1576,1659,1750,1844,1915,1978,2069,2160,2224,2287,2347,2415,2497,2580,2659,2729,2805,2876,2947,3039,3131,3197,3960,4013,4071,4119,4180,4240,4312,4374,4436,4497,4559,4624,4688,4754,4806,4866,4940,5014", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,81,82,78,69,75,70,70,91,91,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "279,482,662,1484,1571,1654,1745,1839,1910,1973,2064,2155,2219,2282,2342,2410,2492,2575,2654,2724,2800,2871,2942,3034,3126,3192,3255,4008,4066,4114,4175,4235,4307,4369,4431,4492,4554,4619,4683,4749,4801,4861,4935,5009,5061"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-night-v8_values-night-v8.arsc.flat", "map": [{"source": "C:\\python_programs\\DocScribe\\doc_transcribe\\android\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "17,8", "startColumns": "4,4", "startOffsets": "827,387", "endLines": "19,10", "endColumns": "12,12", "endOffsets": "994,551"}, "to": {"startLines": "2,5", "startColumns": "4,4", "startOffsets": "55,227", "endLines": "4,7", "endColumns": "12,12", "endOffsets": "222,391"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-lv_values-lv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,196,268,338,418,495,596,694", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "125,191,263,333,413,490,591,689,768"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3464,3539,3605,3677,3747,3827,3904,4005,4103", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "3534,3600,3672,3742,3822,3899,4000,4098,4177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,831,915,998,1081,1176,1271,1344,1411,1505,1599,1665,1732,1795,1871,1949,2032,2111,2185,2267,2341,2414,2508,2601,2667,2733,2786,2844,2892,2953,3011,3087,3151,3216,3281,3338,3404,3470,3536,3588,3652,3730,3808", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,77,82,78,73,81,73,72,93,92,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "281,562,826,910,993,1076,1171,1266,1339,1406,1500,1594,1660,1727,1790,1866,1944,2027,2106,2180,2262,2336,2409,2503,2596,2662,2728,2781,2839,2887,2948,3006,3082,3146,3211,3276,3333,3399,3465,3531,3583,3647,3725,3803,3858"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,1562,1646,1729,1812,1907,2002,2075,2142,2236,2330,2396,2463,2526,2602,2680,2763,2842,2916,2998,3072,3145,3239,3332,3398,4182,4235,4293,4341,4402,4460,4536,4600,4665,4730,4787,4853,4919,4985,5037,5101,5179,5257", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,77,82,78,73,81,73,72,93,92,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "281,562,826,1641,1724,1807,1902,1997,2070,2137,2231,2325,2391,2458,2521,2597,2675,2758,2837,2911,2993,3067,3140,3234,3327,3393,3459,4230,4288,4336,4397,4455,4531,4595,4660,4725,4782,4848,4914,4980,5032,5096,5174,5252,5307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "831,929,1031,1131,1232,1339,1447,5312", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "924,1026,1126,1227,1334,1442,1557,5408"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-uz_values-uz.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,186,256,320,399,467,569,663", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "115,181,251,315,394,462,564,658,738"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3311,3376,3442,3512,3576,3655,3723,3825,3919", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "3371,3437,3507,3571,3650,3718,3820,3914,3994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "644,746,848,949,1049,1157,1261,5121", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "741,843,944,1044,1152,1256,1375,5217"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,644,730,825,908,1006,1105,1180,1248,1349,1450,1515,1578,1641,1713,1798,1877,1962,2039,2113,2186,2261,2352,2440,2509,2575,2628,2688,2736,2797,2858,2929,2989,3057,3120,3185,3251,3315,3381,3433,3494,3569,3644", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,84,78,84,76,73,72,74,90,87,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "280,463,639,725,820,903,1001,1100,1175,1243,1344,1445,1510,1573,1636,1708,1793,1872,1957,2034,2108,2181,2256,2347,2435,2504,2570,2623,2683,2731,2792,2853,2924,2984,3052,3115,3180,3246,3310,3376,3428,3489,3564,3639,3692"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,1380,1466,1561,1644,1742,1841,1916,1984,2085,2186,2251,2314,2377,2449,2534,2613,2698,2775,2849,2922,2997,3088,3176,3245,3999,4052,4112,4160,4221,4282,4353,4413,4481,4544,4609,4675,4739,4805,4857,4918,4993,5068", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,84,78,84,76,73,72,74,90,87,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "280,463,639,1461,1556,1639,1737,1836,1911,1979,2080,2181,2246,2309,2372,2444,2529,2608,2693,2770,2844,2917,2992,3083,3171,3240,3306,4047,4107,4155,4216,4277,4348,4408,4476,4539,4604,4670,4734,4800,4852,4913,4988,5063,5116"}}]}, {"outputFile": "com.example.doc_transcribe.app-merged_res-31:/values-fi_values-fi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,491,692,783,876,957,1053,1145,1216,1283,1372,1459,1527,1592,1655,1727,1815,1899,1985,2062,2144,2216,2287,2380,2469,2534,2598,2651,2709,2757,2818,2886,2958,3027,3099,3166,3221,3286,3352,3418,3470,3531,3606,3681", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,87,83,85,76,81,71,70,92,88,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "282,486,687,778,871,952,1048,1140,1211,1278,1367,1454,1522,1587,1650,1722,1810,1894,1980,2057,2139,2211,2282,2375,2464,2529,2593,2646,2704,2752,2813,2881,2953,3022,3094,3161,3216,3281,3347,3413,3465,3526,3601,3676,3733"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,491,1426,1517,1610,1691,1787,1879,1950,2017,2106,2193,2261,2326,2389,2461,2549,2633,2719,2796,2878,2950,3021,3114,3203,3268,3989,4042,4100,4148,4209,4277,4349,4418,4490,4557,4612,4677,4743,4809,4861,4922,4997,5072", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,87,83,85,76,81,71,70,92,88,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "282,486,687,1512,1605,1686,1782,1874,1945,2012,2101,2188,2256,2321,2384,2456,2544,2628,2714,2791,2873,2945,3016,3109,3198,3263,3327,4037,4095,4143,4204,4272,4344,4413,4485,4552,4607,4672,4738,4804,4856,4917,4992,5067,5124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "692,788,890,988,1093,1198,1310,5129", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "783,885,983,1088,1193,1305,1421,5225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,241,308,385,455,549,641", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "115,174,236,303,380,450,544,636,707"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3332,3397,3456,3518,3585,3662,3732,3826,3918", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "3392,3451,3513,3580,3657,3727,3821,3913,3984"}}]}]}
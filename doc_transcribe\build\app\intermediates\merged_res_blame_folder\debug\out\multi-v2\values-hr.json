{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3486,3561,3622,3687,3760,3839,3912,3997,4079", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "3556,3617,3682,3755,3834,3907,3992,4074,4147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "864,962,1069,1166,1265,1369,1473,5346", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "957,1064,1161,1260,1364,1468,1585,5442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,1987,2067,2144,2212,2296,2367,2438,2534,2628,2695,2760,2813,2871,2919,2980,3054,3133,3209,3283,3347,3406,3477,3542,3613,3665,3728,3813,3898", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,74,79,76,67,83,70,70,95,93,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,1982,2062,2139,2207,2291,2362,2433,2529,2623,2690,2755,2808,2866,2914,2975,3049,3128,3204,3278,3342,3401,3472,3537,3608,3660,3723,3808,3893,3949"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,1590,1671,1753,1833,1940,2047,2117,2184,2275,2367,2432,2503,2566,2638,2713,2793,2870,2938,3022,3093,3164,3260,3354,3421,4152,4205,4263,4311,4372,4446,4525,4601,4675,4739,4798,4869,4934,5005,5057,5120,5205,5290", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,74,79,76,67,83,70,70,95,93,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "290,580,859,1666,1748,1828,1935,2042,2112,2179,2270,2362,2427,2498,2561,2633,2708,2788,2865,2933,3017,3088,3159,3255,3349,3416,3481,4200,4258,4306,4367,4441,4520,4596,4670,4734,4793,4864,4929,5000,5052,5115,5200,5285,5341"}}]}]}
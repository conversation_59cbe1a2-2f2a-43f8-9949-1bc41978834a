import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../widgets/app_scaffold.dart';
import 'patient_info_screen.dart';
import 'history_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        return AppScaffold(
          title: 'DocTranscribe',
          showBackButton: false,
          body: _buildBody(context, appState),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, AppStateProvider appState) {
    switch (appState.currentScreen) {
      case AppScreen.home:
        return _buildHomeContent(context, appState);
      case AppScreen.patientInfo:
        return const PatientInfoScreen();
      case AppScreen.history:
        return const HistoryScreen();
      case AppScreen.settings:
        return const SettingsScreen();
      default:
        return _buildHomeContent(context, appState);
    }
  }

  Widget _buildHomeContent(BuildContext context, AppStateProvider appState) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Welcome section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.medical_services,
                        size: 32,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome to DocTranscribe',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Record, transcribe, and generate professional consultation reports',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (appState.doctorName != null) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        'Dr. ${appState.doctorName}',
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 32),

          // Main action buttons
          _buildActionButton(
            context: context,
            icon: Icons.add_circle,
            title: 'New Consultation',
            subtitle: 'Start a new patient consultation',
            onTap: () => _startNewConsultation(context, appState),
            isPrimary: true,
          ),

          const SizedBox(height: 16),

          _buildActionButton(
            context: context,
            icon: Icons.history,
            title: 'Consultation History',
            subtitle: 'View past consultations and reports',
            onTap: () => appState.navigateToHistory(),
          ),

          const SizedBox(height: 16),

          _buildActionButton(
            context: context,
            icon: Icons.settings,
            title: 'Settings',
            subtitle: 'Configure app preferences and API settings',
            onTap: () => appState.navigateToSettings(),
          ),

          const Spacer(),

          // Status indicators
          _buildStatusSection(context, appState),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isPrimary = false,
  }) {
    return Card(
      elevation: isPrimary ? 8 : 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isPrimary 
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 28,
                  color: isPrimary 
                      ? Colors.white
                      : Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusSection(BuildContext context, AppStateProvider appState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Status',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              context: context,
              label: 'API Connection',
              isGood: appState.hasValidApiKey && !appState.isOffline,
              goodText: 'Connected',
              badText: appState.isOffline ? 'Offline' : 'Not configured',
            ),
            const SizedBox(height: 8),
            _buildStatusItem(
              context: context,
              label: 'Language',
              isGood: true,
              goodText: appState.supportedLanguages[appState.selectedLanguage] ?? 'Unknown',
              badText: '',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem({
    required BuildContext context,
    required String label,
    required bool isGood,
    required String goodText,
    required String badText,
  }) {
    return Row(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: isGood ? Colors.green : Colors.red,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        Text(
          isGood ? goodText : badText,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: isGood ? Colors.green : Colors.red,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  void _startNewConsultation(BuildContext context, AppStateProvider appState) {
    if (!appState.hasValidApiKey) {
      _showApiKeyRequiredDialog(context, appState);
      return;
    }

    appState.navigateToPatientInfo();
  }

  void _showApiKeyRequiredDialog(BuildContext context, AppStateProvider appState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('API Key Required'),
        content: const Text(
          'Please configure your NVIDIA Riva API key in Settings before starting a consultation.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              appState.navigateToSettings();
            },
            child: const Text('Go to Settings'),
          ),
        ],
      ),
    );
  }
}

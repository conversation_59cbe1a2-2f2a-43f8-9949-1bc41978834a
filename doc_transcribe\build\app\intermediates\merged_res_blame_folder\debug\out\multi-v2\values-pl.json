{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,622,944,1022,1100,1183,1272,1361,1444,1511,1605,1699,1768,1834,1899,1971,2053,2131,2209,2285,2366,2439,2522,2613,2702,2770,2834,2887,2945,2993,3054,3127,3193,3257,3334,3401,3459,3526,3591,3656,3708,3775,3866,3957", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,81,77,77,75,80,72,82,90,88,67,63,52,57,47,60,72,65,63,76,66,57,66,64,64,51,66,90,90,54", "endOffsets": "282,617,939,1017,1095,1178,1267,1356,1439,1506,1600,1694,1763,1829,1894,1966,2048,2126,2204,2280,2361,2434,2517,2608,2697,2765,2829,2882,2940,2988,3049,3122,3188,3252,3329,3396,3454,3521,3586,3651,3703,3770,3861,3952,4007"}, "to": {"startLines": "2,11,17,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,672,4443,4521,4599,4682,4771,4860,4943,5010,5104,5198,5267,5333,5398,5470,5552,5630,5708,5784,5865,5938,6021,6112,6201,6269,6995,7048,7106,7154,7215,7288,7354,7418,7495,7562,7620,7687,7752,7817,7869,7936,8027,8118", "endLines": "10,16,22,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,81,77,77,75,80,72,82,90,88,67,63,52,57,47,60,72,65,63,76,66,57,66,64,64,51,66,90,90,54", "endOffsets": "332,667,989,4516,4594,4677,4766,4855,4938,5005,5099,5193,5262,5328,5393,5465,5547,5625,5703,5779,5860,5933,6016,6107,6196,6264,6328,7043,7101,7149,7210,7283,7349,7413,7490,7557,7615,7682,7747,7812,7864,7931,8022,8113,8168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "994,1109,1211,1319,1405,1512,1631,1710,1786,1877,1970,2065,2159,2260,2353,2448,2543,2634,2725,2807,2916,3016,3115,3224,3336,3447,3610,8173", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "1104,1206,1314,1400,1507,1626,1705,1781,1872,1965,2060,2154,2255,2348,2443,2538,2629,2720,2802,2911,3011,3110,3219,3331,3442,3605,3701,8251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,189,251,320,398,468,561,652", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "123,184,246,315,393,463,556,647,712"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6333,6406,6467,6529,6598,6676,6746,6839,6930", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "6401,6462,6524,6593,6671,6741,6834,6925,6990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "50,51,52,53,54,55,56,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3706,3803,3905,4003,4102,4216,4321,8256", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3798,3900,3998,4097,4211,4316,4438,8352"}}]}]}
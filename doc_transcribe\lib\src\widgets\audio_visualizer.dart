import 'package:flutter/material.dart';
import 'dart:math' as math;

class AudioVisualizer extends StatefulWidget {
  final bool isRecording;
  final double audioLevel;
  final int barCount;
  final Color? activeColor;
  final Color? inactiveColor;

  const AudioVisualizer({
    super.key,
    required this.isRecording,
    required this.audioLevel,
    this.barCount = 20,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  State<AudioVisualizer> createState() => _AudioVisualizerState();
}

class _AudioVisualizerState extends State<AudioVisualizer>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  final List<double> _barHeights = [];

  @override
  void initState() {
    super.initState();
    
    // Initialize bar heights
    _barHeights.addAll(List.generate(widget.barCount, (index) => 0.1));
    
    // Setup animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.addListener(_updateBars);
  }

  @override
  void didUpdateWidget(AudioVisualizer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isRecording != oldWidget.isRecording) {
      if (widget.isRecording) {
        _animationController.repeat();
      } else {
        _animationController.stop();
        _resetBars();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _updateBars() {
    if (!widget.isRecording) return;

    setState(() {
      final random = math.Random();
      for (int i = 0; i < _barHeights.length; i++) {
        // Simulate audio levels with some randomness
        final baseLevel = widget.audioLevel;
        final randomFactor = 0.3 + random.nextDouble() * 0.7;
        final newHeight = (baseLevel * randomFactor).clamp(0.1, 1.0);
        
        // Smooth transition
        _barHeights[i] = (_barHeights[i] * 0.7) + (newHeight * 0.3);
      }
    });
  }

  void _resetBars() {
    setState(() {
      for (int i = 0; i < _barHeights.length; i++) {
        _barHeights[i] = 0.1;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final activeColor = widget.activeColor ?? Theme.of(context).primaryColor;
    final inactiveColor = widget.inactiveColor ?? Colors.grey[300]!;

    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(widget.barCount, (index) {
          return AnimatedContainer(
            duration: const Duration(milliseconds: 100),
            width: 3,
            height: 80 * _barHeights[index],
            margin: const EdgeInsets.symmetric(horizontal: 1),
            decoration: BoxDecoration(
              color: widget.isRecording ? activeColor : inactiveColor,
              borderRadius: BorderRadius.circular(2),
              boxShadow: widget.isRecording
                  ? [
                      BoxShadow(
                        color: activeColor.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
          );
        }),
      ),
    );
  }
}

{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "695,797,905,1007,1108,1214,1321,5238", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "792,900,1002,1103,1209,1316,1440,5334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,695,787,879,969,1069,1171,1248,1313,1405,1497,1568,1638,1699,1769,1861,1950,2040,2115,2199,2274,2345,2433,2520,2584,2663,2716,2774,2822,2883,2950,3012,3077,3144,3203,3265,3331,3395,3462,3516,3576,3650,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,91,88,89,74,83,74,70,87,86,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,782,874,964,1064,1166,1243,1308,1400,1492,1563,1633,1694,1764,1856,1945,2035,2110,2194,2269,2340,2428,2515,2579,2658,2711,2769,2817,2878,2945,3007,3072,3139,3198,3260,3326,3390,3457,3511,3571,3645,3719,3773"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,1445,1537,1629,1719,1819,1921,1998,2063,2155,2247,2318,2388,2449,2519,2611,2700,2790,2865,2949,3024,3095,3183,3270,3334,4123,4176,4234,4282,4343,4410,4472,4537,4604,4663,4725,4791,4855,4922,4976,5036,5110,5184", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,91,88,89,74,83,74,70,87,86,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,1532,1624,1714,1814,1916,1993,2058,2150,2242,2313,2383,2444,2514,2606,2695,2785,2860,2944,3019,3090,3178,3265,3329,3408,4171,4229,4277,4338,4405,4467,4532,4599,4658,4720,4786,4850,4917,4971,5031,5105,5179,5233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3413,3485,3553,3626,3694,3774,3851,3952,4045", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "3480,3548,3621,3689,3769,3846,3947,4040,4118"}}]}]}
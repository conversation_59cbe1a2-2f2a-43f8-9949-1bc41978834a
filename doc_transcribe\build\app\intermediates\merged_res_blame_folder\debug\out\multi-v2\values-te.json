{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3485,3587,3695,3797,3898,4004,4111,8109", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3582,3690,3792,3893,3999,4106,4230,8205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,695,787,879,969,1069,1171,1248,1313,1405,1497,1568,1638,1699,1769,1861,1950,2040,2115,2199,2274,2345,2433,2520,2584,2663,2716,2774,2822,2883,2950,3012,3077,3144,3203,3265,3331,3395,3462,3516,3576,3650,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,91,88,89,74,83,74,70,87,86,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,782,874,964,1064,1166,1243,1308,1400,1492,1563,1633,1694,1764,1856,1945,2035,2110,2194,2269,2340,2428,2515,2579,2658,2711,2769,2817,2878,2945,3007,3072,3139,3198,3260,3326,3390,3457,3511,3571,3645,3719,3773"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,552,4235,4327,4419,4509,4609,4711,4788,4853,4945,5037,5108,5178,5239,5309,5401,5490,5580,5655,5739,5814,5885,5973,6060,6124,6913,6966,7024,7072,7133,7200,7262,7327,7394,7453,7515,7581,7645,7712,7766,7826,7900,7974", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,91,88,89,74,83,74,70,87,86,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "331,547,740,4322,4414,4504,4604,4706,4783,4848,4940,5032,5103,5173,5234,5304,5396,5485,5575,5650,5734,5809,5880,5968,6055,6119,6198,6961,7019,7067,7128,7195,7257,7322,7389,7448,7510,7576,7640,7707,7761,7821,7895,7969,8023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6203,6275,6343,6416,6484,6564,6641,6742,6835", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "6270,6338,6411,6479,6559,6636,6737,6830,6908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,913,1004,1097,1192,1286,1386,1479,1574,1669,1760,1851,1934,2048,2150,2247,2362,2465,2580,2742,2845", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,96,114,102,114,161,102,80", "endOffsets": "217,329,440,530,635,754,832,908,999,1092,1187,1281,1381,1474,1569,1664,1755,1846,1929,2043,2145,2242,2357,2460,2575,2737,2840,2921"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "745,862,974,1085,1175,1280,1399,1477,1553,1644,1737,1832,1926,2026,2119,2214,2309,2400,2491,2574,2688,2790,2887,3002,3105,3220,3382,8028", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,96,114,102,114,161,102,80", "endOffsets": "857,969,1080,1170,1275,1394,1472,1548,1639,1732,1827,1921,2021,2114,2209,2304,2395,2486,2569,2683,2785,2882,2997,3100,3215,3377,3480,8104"}}]}]}
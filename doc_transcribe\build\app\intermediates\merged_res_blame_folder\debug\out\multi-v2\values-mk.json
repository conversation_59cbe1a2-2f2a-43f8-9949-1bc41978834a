{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,516,702,791,881,962,1052,1143,1221,1286,1389,1494,1559,1623,1686,1758,1838,1926,2003,2080,2169,2240,2319,2405,2489,2553,2621,2674,2732,2780,2841,2907,2974,3037,3107,3171,3229,3295,3360,3426,3478,3543,3622,3701", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,79,87,76,76,88,70,78,85,83,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "306,511,697,786,876,957,1047,1138,1216,1281,1384,1489,1554,1618,1681,1753,1833,1921,1998,2075,2164,2235,2314,2400,2484,2548,2616,2669,2727,2775,2836,2902,2969,3032,3102,3166,3224,3290,3355,3421,3473,3538,3617,3696,3752"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,516,1421,1510,1600,1681,1771,1862,1940,2005,2108,2213,2278,2342,2405,2477,2557,2645,2722,2799,2888,2959,3038,3124,3208,3272,4005,4058,4116,4164,4225,4291,4358,4421,4491,4555,4613,4679,4744,4810,4862,4927,5006,5085", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,79,87,76,76,88,70,78,85,83,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "306,511,697,1505,1595,1676,1766,1857,1935,2000,2103,2208,2273,2337,2400,2472,2552,2640,2717,2794,2883,2954,3033,3119,3203,3267,3335,4053,4111,4159,4220,4286,4353,4416,4486,4550,4608,4674,4739,4805,4857,4922,5001,5080,5136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,250,318,395,468,557,642", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "120,181,245,313,390,463,552,637,715"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3340,3410,3471,3535,3603,3680,3753,3842,3927", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "3405,3466,3530,3598,3675,3748,3837,3922,4000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "702,800,902,999,1097,1202,1305,5141", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "795,897,994,1092,1197,1300,1416,5237"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,835,921,1008,1096,1194,1301,1371,1438,1534,1626,1691,1764,1827,1895,1970,2047,2124,2204,2288,2359,2430,2514,2596,2668,2738,2791,2849,2897,2958,3030,3097,3161,3232,3296,3355,3420,3485,3556,3608,3675,3756,3837", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,74,76,76,79,83,70,70,83,81,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "288,566,830,916,1003,1091,1189,1296,1366,1433,1529,1621,1686,1759,1822,1890,1965,2042,2119,2199,2283,2354,2425,2509,2591,2663,2733,2786,2844,2892,2953,3025,3092,3156,3227,3291,3350,3415,3480,3551,3603,3670,3751,3832,3888"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,1560,1646,1733,1821,1919,2026,2096,2163,2259,2351,2416,2489,2552,2620,2695,2772,2849,2929,3013,3084,3155,3239,3321,3393,4143,4196,4254,4302,4363,4435,4502,4566,4637,4701,4760,4825,4890,4961,5013,5080,5161,5242", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,74,76,76,79,83,70,70,83,81,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "288,566,830,1641,1728,1816,1914,2021,2091,2158,2254,2346,2411,2484,2547,2615,2690,2767,2844,2924,3008,3079,3150,3234,3316,3388,3458,4191,4249,4297,4358,4430,4497,4561,4632,4696,4755,4820,4885,4956,5008,5075,5156,5237,5293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,581,662", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "125,186,251,324,403,476,576,657,730"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3463,3538,3599,3664,3737,3816,3889,3989,4070", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "3533,3594,3659,3732,3811,3884,3984,4065,4138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "835,933,1035,1133,1237,1341,1443,5298", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "928,1030,1128,1232,1336,1438,1555,5394"}}]}]}
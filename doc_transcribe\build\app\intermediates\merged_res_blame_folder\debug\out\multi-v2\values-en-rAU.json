{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3154,3224,3286,3351,3415,3492,3557,3647,3732", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "3219,3281,3346,3410,3487,3552,3642,3727,3796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1720,1796,1871,1938,2018,2089,2156,2233,2308,2371,2435,2488,2546,2594,2655,2720,2782,2847,2918,2976,3034,3100,3164,3230,3282,3344,3420,3496", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,74,75,74,66,79,70,66,76,74,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1715,1791,1866,1933,2013,2084,2151,2228,2303,2366,2430,2483,2541,2589,2650,2715,2777,2842,2913,2971,3029,3095,3159,3225,3277,3339,3415,3491,3545"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,1375,1457,1539,1617,1704,1789,1856,1919,2011,2103,2168,2231,2293,2364,2439,2515,2590,2657,2737,2808,2875,2952,3027,3090,3801,3854,3912,3960,4021,4086,4148,4213,4284,4342,4400,4466,4530,4596,4648,4710,4786,4862", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,74,75,74,66,79,70,66,76,74,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,1452,1534,1612,1699,1784,1851,1914,2006,2098,2163,2226,2288,2359,2434,2510,2585,2652,2732,2803,2870,2947,3022,3085,3149,3849,3907,3955,4016,4081,4143,4208,4279,4337,4395,4461,4525,4591,4643,4705,4781,4857,4911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "656,752,854,953,1052,1156,1259,4916", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "747,849,948,1047,1151,1254,1370,5012"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,745,828,901,995,1085,1159,1226,1323,1420,1486,1555,1622,1693,1770,1853,1928,1995,2081,2154,2228,2323,2416,2480,2547,2600,2658,2706,2767,2832,2900,2965,3034,3098,3159,3225,3290,3356,3409,3469,3543,3617", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,76,82,74,66,85,72,73,94,92,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "280,469,659,740,823,896,990,1080,1154,1221,1318,1415,1481,1550,1617,1688,1765,1848,1923,1990,2076,2149,2223,2318,2411,2475,2542,2595,2653,2701,2762,2827,2895,2960,3029,3093,3154,3220,3285,3351,3404,3464,3538,3612,3671"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,524,4189,4270,4353,4426,4520,4610,4684,4751,4848,4945,5011,5080,5147,5218,5295,5378,5453,5520,5606,5679,5753,5848,5941,6005,6776,6829,6887,6935,6996,7061,7129,7194,7263,7327,7388,7454,7519,7585,7638,7698,7772,7846", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,76,82,74,66,85,72,73,94,92,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "330,519,709,4265,4348,4421,4515,4605,4679,4746,4843,4940,5006,5075,5142,5213,5290,5373,5448,5515,5601,5674,5748,5843,5936,6000,6067,6824,6882,6930,6991,7056,7124,7189,7258,7322,7383,7449,7514,7580,7633,7693,7767,7841,7900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3457,3554,3656,3755,3855,3962,4068,7991", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3549,3651,3750,3850,3957,4063,4184,8087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,194,261,332,414,496,591,680", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "125,189,256,327,409,491,586,675,754"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6072,6147,6211,6278,6349,6431,6513,6608,6697", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "6142,6206,6273,6344,6426,6508,6603,6692,6771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,740,825,905,997,1091,1188,1282,1381,1475,1571,1666,1758,1850,1935,2042,2153,2255,2363,2471,2578,2749,2848", "endColumns": "107,105,106,88,100,123,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,170,98,85", "endOffsets": "208,314,421,510,611,735,820,900,992,1086,1183,1277,1376,1470,1566,1661,1753,1845,1930,2037,2148,2250,2358,2466,2573,2744,2843,2929"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "714,822,928,1035,1124,1225,1349,1434,1514,1606,1700,1797,1891,1990,2084,2180,2275,2367,2459,2544,2651,2762,2864,2972,3080,3187,3358,7905", "endColumns": "107,105,106,88,100,123,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,170,98,85", "endOffsets": "817,923,1030,1119,1220,1344,1429,1509,1601,1695,1792,1886,1985,2079,2175,2270,2362,2454,2539,2646,2757,2859,2967,3075,3182,3353,3452,7986"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,745,828,901,995,1085,1159,1226,1323,1420,1486,1555,1622,1693,1770,1853,1928,1995,2081,2154,2228,2323,2416,2480,2547,2600,2658,2706,2767,2832,2900,2965,3034,3098,3159,3225,3290,3356,3409,3469,3543,3617", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,76,82,74,66,85,72,73,94,92,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "280,469,659,740,823,896,990,1080,1154,1221,1318,1415,1481,1550,1617,1688,1765,1848,1923,1990,2076,2149,2223,2318,2411,2475,2542,2595,2653,2701,2762,2827,2895,2960,3029,3093,3154,3220,3285,3351,3404,3464,3538,3612,3671"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,1396,1477,1560,1633,1727,1817,1891,1958,2055,2152,2218,2287,2354,2425,2502,2585,2660,2727,2813,2886,2960,3055,3148,3212,3983,4036,4094,4142,4203,4268,4336,4401,4470,4534,4595,4661,4726,4792,4845,4905,4979,5053", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,76,82,74,66,85,72,73,94,92,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "280,469,659,1472,1555,1628,1722,1812,1886,1953,2050,2147,2213,2282,2349,2420,2497,2580,2655,2722,2808,2881,2955,3050,3143,3207,3274,4031,4089,4137,4198,4263,4331,4396,4465,4529,4590,4656,4721,4787,4840,4900,4974,5048,5107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "664,761,863,962,1062,1169,1275,5112", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "756,858,957,1057,1164,1270,1391,5208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,194,261,332,414,496,591,680", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "125,189,256,327,409,491,586,675,754"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3279,3354,3418,3485,3556,3638,3720,3815,3904", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "3349,3413,3480,3551,3633,3715,3810,3899,3978"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3219,3289,3353,3419,3484,3562,3628,3718,3801", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "3284,3348,3414,3479,3557,3623,3713,3796,3873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1744,1821,1896,1963,2045,2115,2186,2266,2346,2413,2476,2529,2587,2635,2696,2760,2822,2883,2949,3012,3071,3137,3201,3267,3319,3381,3457,3533", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,75,76,74,66,81,69,70,79,79,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1739,1816,1891,1958,2040,2110,2181,2261,2341,2408,2471,2524,2582,2630,2691,2755,2817,2878,2944,3007,3066,3132,3196,3262,3314,3376,3452,3528,3590"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,1386,1469,1553,1630,1721,1814,1887,1956,2052,2146,2210,2273,2338,2411,2487,2564,2639,2706,2788,2858,2929,3009,3089,3156,3878,3931,3989,4037,4098,4162,4224,4285,4351,4414,4473,4539,4603,4669,4721,4783,4859,4935", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,75,76,74,66,81,69,70,79,79,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "279,466,638,1464,1548,1625,1716,1809,1882,1951,2047,2141,2205,2268,2333,2406,2482,2559,2634,2701,2783,2853,2924,3004,3084,3151,3214,3926,3984,4032,4093,4157,4219,4280,4346,4409,4468,4534,4598,4664,4716,4778,4854,4930,4992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "643,738,840,937,1047,1153,1271,4997", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "733,835,932,1042,1148,1266,1381,5093"}}]}]}
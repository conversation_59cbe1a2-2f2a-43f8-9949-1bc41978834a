{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b30e4dc937276f023b4a6e07a5156550\\transformed\\media-1.4.3\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "141,142,143,333,357,604,606,607,612,614", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "5782,5871,5942,18041,19340,34790,34966,35088,35350,35545", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "5866,5937,6010,18098,19395,34851,35083,35144,35411,35607"}}, {"source": "C:\\python_programs\\DocScribe\\doc_transcribe\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "17,8", "startColumns": "4,4", "startOffsets": "827,387", "endLines": "19,10", "endColumns": "12,12", "endOffsets": "994,551"}, "to": {"startLines": "593,596", "startColumns": "4,4", "startOffsets": "34131,34303", "endLines": "595,598", "endColumns": "12,12", "endOffsets": "34298,34467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fd0a2dbf4f96c6dbec5c3dc6707cb9de\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "19226", "endColumns": "49", "endOffsets": "19271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bf6ae328e54b92e9215264b71b8866bc\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "329,335,356,717,722", "startColumns": "4,4,4,4,4", "startOffsets": "17848,18145,19276,39704,39874", "endLines": "329,335,356,721,725", "endColumns": "56,64,63,24,24", "endOffsets": "17900,18205,19335,39869,40018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,15,16,20,21,22,23,24,25,26,27,28,29,30,35,42,43,44,45,46,47,52,53,54,55,56,57,58,59,60,61,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,208,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,261,265,269,273,277,281,285,289,290,296,307,311,315,319,323,327,331,335,339,343,347,351,362,367,372,377,388,396,406,410,414,418,421,437,463,498,527", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,539,588,714,763,812,871,925,977,1027,1092,1149,1196,1251,1399,1637,1686,1747,1807,1863,1923,2093,2153,2206,2263,2318,2374,2431,2480,2531,2590,2877,2942,3000,3049,3097,3148,3205,3262,3324,3391,3462,3534,3578,3635,3691,3754,3827,3897,3956,4013,4060,4115,4160,4209,4264,4318,4368,4419,4473,4532,4582,4640,4696,4749,4812,4877,4940,4992,5052,5116,5182,5240,5312,5373,5443,5513,5578,5643,5714,5802,5900,5996,6070,6146,6220,6302,6388,6474,6560,6638,6726,6812,6882,6974,7052,7132,7210,7296,7378,7471,7549,7640,7721,7810,7913,8014,8098,8194,8291,8386,8479,8571,8664,8757,8850,8933,9020,9115,9208,9289,9384,9477,9554,9598,9639,9684,9732,9776,9819,9868,9915,9959,10015,10068,10110,10157,10205,10265,10303,10353,10397,10447,10499,10537,10584,10631,10672,10711,10749,10793,10841,10883,10921,10963,11017,11064,11101,11150,11192,11233,11274,11316,11359,11397,11433,11511,11589,11886,12156,12238,12320,12462,12540,12627,12712,12779,12842,12934,13026,13091,13154,13216,13287,13397,13508,13618,13685,13765,13836,13903,13988,14073,14136,14224,14288,14430,14530,14578,14721,14784,14846,14911,14982,15040,15098,15164,15228,15294,15346,15408,15484,15560,15614,15893,16117,16320,16526,16729,16944,17153,17350,17388,17742,18529,18770,19010,19267,19520,19773,20008,20255,20494,20738,20959,21154,21796,22087,22383,22686,23322,23856,24330,24541,24741,24917,25025,25601,26574,27863,28953", "endLines": "10,11,12,13,14,15,19,20,21,22,23,24,25,26,27,28,29,34,41,42,43,44,45,46,51,52,53,54,55,56,57,58,59,60,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,207,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,260,264,268,272,276,280,284,288,289,295,306,310,314,318,322,326,330,334,338,342,346,350,361,366,371,376,387,395,405,409,413,417,420,436,462,497,526,565", "endColumns": "17,49,53,53,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "330,380,434,488,534,583,709,758,807,866,920,972,1022,1087,1144,1191,1246,1394,1632,1681,1742,1802,1858,1918,2088,2148,2201,2258,2313,2369,2426,2475,2526,2585,2872,2937,2995,3044,3092,3143,3200,3257,3319,3386,3457,3529,3573,3630,3686,3749,3822,3892,3951,4008,4055,4110,4155,4204,4259,4313,4363,4414,4468,4527,4577,4635,4691,4744,4807,4872,4935,4987,5047,5111,5177,5235,5307,5368,5438,5508,5573,5638,5709,5797,5895,5991,6065,6141,6215,6297,6383,6469,6555,6633,6721,6807,6877,6969,7047,7127,7205,7291,7373,7466,7544,7635,7716,7805,7908,8009,8093,8189,8286,8381,8474,8566,8659,8752,8845,8928,9015,9110,9203,9284,9379,9472,9549,9593,9634,9679,9727,9771,9814,9863,9910,9954,10010,10063,10105,10152,10200,10260,10298,10348,10392,10442,10494,10532,10579,10626,10667,10706,10744,10788,10836,10878,10916,10958,11012,11059,11096,11145,11187,11228,11269,11311,11354,11392,11428,11506,11584,11881,12151,12233,12315,12457,12535,12622,12707,12774,12837,12929,13021,13086,13149,13211,13282,13392,13503,13613,13680,13760,13831,13898,13983,14068,14131,14219,14283,14425,14525,14573,14716,14779,14841,14906,14977,15035,15093,15159,15223,15289,15341,15403,15479,15555,15609,15888,16112,16315,16521,16724,16939,17148,17345,17383,17737,18524,18765,19005,19262,19515,19768,20003,20250,20489,20733,20954,21149,21791,22082,22378,22681,23317,23851,24325,24536,24736,24912,25020,25596,26569,27858,28948,30391"}, "to": {"startLines": "2,11,12,14,15,16,17,21,22,23,24,47,48,49,51,52,53,55,60,67,68,69,70,71,72,77,78,79,80,81,82,83,84,85,114,122,123,124,125,126,131,132,133,134,135,136,137,138,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,358,359,361,365,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,431,436,440,444,448,452,456,460,464,465,471,482,486,490,494,498,502,506,510,514,518,522,526,537,542,547,552,563,571,581,585,589,628,647,765,791,886,915", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,498,552,598,647,773,822,871,930,1595,1647,1697,1822,1879,1926,2037,2185,2423,2472,2533,2593,2649,2709,2879,2939,2992,3049,3104,3160,3217,3266,3317,4275,4601,4666,4724,4773,4821,5163,5220,5277,5339,5406,5477,5549,5593,6503,6559,6622,6695,6765,6824,6881,6928,6983,7028,7077,7132,7186,7236,7287,7341,7400,7450,7508,7564,7617,7680,7745,7808,7860,7920,7984,8050,8108,8180,8241,8311,8381,8446,8511,9946,10034,10132,10228,10302,10378,10452,10534,10620,10706,10792,10870,10958,11044,11114,11206,11284,11364,11442,11528,11610,11703,11781,11872,11953,12042,12145,12246,12330,12426,12523,12618,12711,12803,12896,12989,13082,13165,13252,13347,13440,13521,13616,13709,15969,16013,16054,16099,16147,16191,16234,16283,16330,16374,16430,16483,16525,16572,16620,16680,16718,16768,16812,16862,16914,16952,16999,17046,17087,17126,17164,17208,17256,17298,17336,17378,17432,17479,17516,17565,17607,17648,17689,17731,17774,17812,19400,19478,19626,19923,20787,20869,20951,21093,21171,21258,21343,21410,21473,21565,21657,21722,21785,21847,21918,22028,22139,22249,22316,22396,22467,22534,22619,22704,22767,22855,23565,23707,23807,23855,23998,24061,24123,24188,24259,24317,24375,24441,24505,24571,24623,24685,24761,24837,24962,25241,25465,25668,25874,26077,26292,26501,26698,26736,27090,27877,28118,28358,28615,28868,29121,29356,29603,29842,30086,30307,30502,31077,31368,31664,31967,32536,33070,33544,33755,33955,36104,36716,40872,41811,45377,46433", "endLines": "10,11,12,14,15,16,20,21,22,23,24,47,48,49,51,52,53,59,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,120,122,123,124,125,126,131,132,133,134,135,136,137,138,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,358,359,364,368,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,435,439,443,447,451,455,459,463,464,470,481,485,489,493,497,501,505,509,513,517,521,525,536,541,546,551,562,570,580,584,588,592,630,662,790,825,914,953", "endColumns": "17,49,53,53,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "330,380,434,547,593,642,768,817,866,925,979,1642,1692,1757,1874,1921,1976,2180,2418,2467,2528,2588,2644,2704,2874,2934,2987,3044,3099,3155,3212,3261,3312,3371,4557,4661,4719,4768,4816,4867,5215,5272,5334,5401,5472,5544,5588,5645,6554,6617,6690,6760,6819,6876,6923,6978,7023,7072,7127,7181,7231,7282,7336,7395,7445,7503,7559,7612,7675,7740,7803,7855,7915,7979,8045,8103,8175,8236,8306,8376,8441,8506,8577,10029,10127,10223,10297,10373,10447,10529,10615,10701,10787,10865,10953,11039,11109,11201,11279,11359,11437,11523,11605,11698,11776,11867,11948,12037,12140,12241,12325,12421,12518,12613,12706,12798,12891,12984,13077,13160,13247,13342,13435,13516,13611,13704,13781,16008,16049,16094,16142,16186,16229,16278,16325,16369,16425,16478,16520,16567,16615,16675,16713,16763,16807,16857,16909,16947,16994,17041,17082,17121,17159,17203,17251,17293,17331,17373,17427,17474,17511,17560,17602,17643,17684,17726,17769,17807,17843,19473,19551,19918,20188,20864,20946,21088,21166,21253,21338,21405,21468,21560,21652,21717,21780,21842,21913,22023,22134,22244,22311,22391,22462,22529,22614,22699,22762,22850,22914,23702,23802,23850,23993,24056,24118,24183,24254,24312,24370,24436,24500,24566,24618,24680,24756,24832,24886,25236,25460,25663,25869,26072,26287,26496,26693,26731,27085,27872,28113,28353,28610,28863,29116,29351,29598,29837,30081,30302,30497,31072,31363,31659,31962,32531,33065,33539,33750,33950,34126,36207,37287,41806,42982,46428,47752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6cc079d3a83268c2582a050621870aa2\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,38", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,2084"}, "to": {"startLines": "54,186,187,188,189,190,191,330,826", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1981,8582,8641,8689,8745,8820,8896,17905,42987", "endLines": "54,186,187,188,189,190,191,330,846", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "2032,8636,8684,8740,8815,8891,8963,17966,43822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68656057c0f9df10db2245ba342a2616\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "13,25,31,39,86,98,104,110,111,112,113,121,286,617,623,847,855,870", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "439,984,1157,1376,3376,3690,3878,4065,4118,4178,4230,4562,15909,35751,35946,43827,44109,44723", "endLines": "13,30,38,46,97,103,109,110,111,112,113,121,286,622,627,854,869,885", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "493,1152,1371,1590,3685,3873,4060,4113,4173,4225,4270,4596,15964,35941,36099,44104,44718,45372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bdf28e1efaec1f3184a5c597f2146f91\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "334,353", "startColumns": "4,4", "startOffsets": "18103,19112", "endColumns": "41,59", "endOffsets": "18140,19167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "50,127,128,129,130,139,140,144,145,146,147,148,149,150,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,331,332,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,360,370,371,372,373,374,375,376,430,599,600,605,608,613,615,616,631,637,663,696,726,759", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1762,4872,4944,5032,5097,5650,5719,6015,6085,6153,6225,6295,6356,6430,8968,9029,9090,9152,9216,9278,9339,9407,9507,9567,9633,9706,9775,9832,9884,13786,13858,13934,13999,14058,14117,14177,14237,14297,14357,14417,14477,14537,14597,14657,14717,14776,14836,14896,14956,15016,15076,15136,15196,15256,15316,15376,15435,15495,15555,15614,15673,15732,15791,15850,17971,18006,18210,18265,18328,18383,18441,18499,18560,18623,18680,18731,18781,18842,18899,18965,18999,19034,19556,20276,20343,20415,20484,20553,20627,20699,24891,34472,34589,34856,35149,35416,35612,35684,36212,36415,37292,39023,40023,40705", "endLines": "50,127,128,129,130,139,140,144,145,146,147,148,149,150,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,331,332,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,360,370,371,372,373,374,375,376,430,599,603,605,611,613,615,616,636,646,695,716,758,764", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1817,4939,5027,5092,5158,5714,5777,6080,6148,6220,6290,6351,6425,6498,9024,9085,9147,9211,9273,9334,9402,9502,9562,9628,9701,9770,9827,9879,9941,13853,13929,13994,14053,14112,14172,14232,14292,14352,14412,14472,14532,14592,14652,14712,14771,14831,14891,14951,15011,15071,15131,15191,15251,15311,15371,15430,15490,15550,15609,15668,15727,15786,15845,15904,18001,18036,18260,18323,18378,18436,18494,18555,18618,18675,18726,18776,18837,18894,18960,18994,19029,19064,19621,20338,20410,20479,20548,20622,20694,20782,24957,34584,34785,34961,35345,35540,35679,35746,36410,36711,39018,39699,40700,40867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db865cfc583742df8a08e9a06de1f57a\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "19172", "endColumns": "53", "endOffsets": "19221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\aa11fe827c549893c9e5c2fc6854bbf6\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "352", "startColumns": "4", "startOffsets": "19069", "endColumns": "42", "endOffsets": "19107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "403,404,405,406,407,408,409,410,411", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22919,22989,23051,23116,23180,23257,23322,23412,23496", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "22984,23046,23111,23175,23252,23317,23407,23491,23560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43980c3f7213c92c8646e00d332d5bd5\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "369", "startColumns": "4", "startOffsets": "20193", "endColumns": "82", "endOffsets": "20271"}}]}]}
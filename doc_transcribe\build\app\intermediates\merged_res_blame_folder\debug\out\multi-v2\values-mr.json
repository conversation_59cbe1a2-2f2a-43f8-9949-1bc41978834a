{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "671,771,875,976,1079,1181,1286,5006", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "766,870,971,1074,1176,1281,1398,5102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1750,1828,1905,1977,2056,2127,2197,2278,2359,2423,2486,2539,2597,2645,2706,2767,2834,2896,2962,3021,3086,3151,3216,3284,3337,3397,3471,3545", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,78,77,76,71,78,70,69,80,80,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1745,1823,1900,1972,2051,2122,2192,2273,2354,2418,2481,2534,2592,2640,2701,2762,2829,2891,2957,3016,3081,3146,3211,3279,3332,3392,3466,3540,3593"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,1403,1485,1565,1648,1735,1829,1897,1961,2051,2142,2207,2275,2335,2403,2482,2560,2637,2709,2788,2859,2929,3010,3091,3155,3894,3947,4005,4053,4114,4175,4242,4304,4370,4429,4494,4559,4624,4692,4745,4805,4879,4953", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,78,77,76,71,78,70,69,80,80,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "281,482,666,1480,1560,1643,1730,1824,1892,1956,2046,2137,2202,2270,2330,2398,2477,2555,2632,2704,2783,2854,2924,3005,3086,3150,3213,3942,4000,4048,4109,4170,4237,4299,4365,4424,4489,4554,4619,4687,4740,4800,4874,4948,5001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3218,3288,3353,3422,3491,3566,3630,3727,3821", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "3283,3348,3417,3486,3561,3625,3722,3816,3889"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "687,790,894,997,1099,1204,1310,5193", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "785,889,992,1094,1199,1305,1424,5289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,292,491,687,771,853,940,1042,1138,1211,1278,1377,1472,1540,1607,1674,1741,1826,1917,2003,2074,2154,2227,2298,2387,2476,2541,2605,2658,2716,2766,2827,2885,2947,3020,3089,3154,3212,3276,3341,3409,3463,3525,3601,3677", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,84,90,85,70,79,72,70,88,88,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "287,486,682,766,848,935,1037,1133,1206,1273,1372,1467,1535,1602,1669,1736,1821,1912,1998,2069,2149,2222,2293,2382,2471,2536,2600,2653,2711,2761,2822,2880,2942,3015,3084,3149,3207,3271,3336,3404,3458,3520,3596,3672,3726"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,292,491,1429,1513,1595,1682,1784,1880,1953,2020,2119,2214,2282,2349,2416,2483,2568,2659,2745,2816,2896,2969,3040,3129,3218,3283,4067,4120,4178,4228,4289,4347,4409,4482,4551,4616,4674,4738,4803,4871,4925,4987,5063,5139", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,84,90,85,70,79,72,70,88,88,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "287,486,682,1508,1590,1677,1779,1875,1948,2015,2114,2209,2277,2344,2411,2478,2563,2654,2740,2811,2891,2964,3035,3124,3213,3278,3342,4115,4173,4223,4284,4342,4404,4477,4546,4611,4669,4733,4798,4866,4920,4982,5058,5134,5188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,275,344,426,501,602,697", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "125,197,270,339,421,496,597,692,770"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3347,3422,3494,3567,3636,3718,3793,3894,3989", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "3417,3489,3562,3631,3713,3788,3889,3984,4062"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3504,3607,3711,3814,3916,4021,4127,8096", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3602,3706,3809,3911,4016,4122,4241,8192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,292,491,687,771,853,940,1042,1138,1211,1278,1377,1472,1540,1607,1674,1741,1826,1917,2003,2074,2154,2227,2298,2387,2476,2541,2605,2658,2716,2766,2827,2885,2947,3020,3089,3154,3212,3276,3341,3409,3463,3525,3601,3677", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,84,90,85,70,79,72,70,88,88,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "287,486,682,766,848,935,1037,1133,1206,1273,1372,1467,1535,1602,1669,1736,1821,1912,1998,2069,2149,2222,2293,2382,2471,2536,2600,2653,2711,2761,2822,2880,2942,3015,3084,3149,3207,3271,3336,3404,3458,3520,3596,3672,3726"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,342,541,4246,4330,4412,4499,4601,4697,4770,4837,4936,5031,5099,5166,5233,5300,5385,5476,5562,5633,5713,5786,5857,5946,6035,6100,6884,6937,6995,7045,7106,7164,7226,7299,7368,7433,7491,7555,7620,7688,7742,7804,7880,7956", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,84,90,85,70,79,72,70,88,88,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "337,536,732,4325,4407,4494,4596,4692,4765,4832,4931,5026,5094,5161,5228,5295,5380,5471,5557,5628,5708,5781,5852,5941,6030,6095,6159,6932,6990,7040,7101,7159,7221,7294,7363,7428,7486,7550,7615,7683,7737,7799,7875,7951,8005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,275,344,426,501,602,697", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "125,197,270,339,421,496,597,692,770"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6164,6239,6311,6384,6453,6535,6610,6711,6806", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "6234,6306,6379,6448,6530,6605,6706,6801,6879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1945,2060,2169,2268,2394,2501,2609,2769,2872", "endColumns": "112,106,115,86,108,122,81,80,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,837,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1940,2055,2164,2263,2389,2496,2604,2764,2867,2953"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "737,850,957,1073,1160,1269,1392,1474,1555,1646,1739,1834,1928,2028,2121,2216,2310,2401,2492,2577,2692,2801,2900,3026,3133,3241,3401,8010", "endColumns": "112,106,115,86,108,122,81,80,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "845,952,1068,1155,1264,1387,1469,1550,1641,1734,1829,1923,2023,2116,2211,2305,2396,2487,2572,2687,2796,2895,3021,3128,3236,3396,3499,8091"}}]}]}
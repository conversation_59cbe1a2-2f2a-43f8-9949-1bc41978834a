{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "958,1055,1157,1255,1359,1462,1564,5450", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "1050,1152,1250,1354,1457,1559,1676,5546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,958,1045,1133,1216,1314,1415,1498,1563,1660,1754,1825,1895,1959,2027,2108,2189,2269,2346,2426,2499,2579,2675,2769,2837,2902,2955,3013,3061,3122,3192,3261,3324,3389,3452,3509,3585,3654,3728,3780,3843,3920,3997", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,80,80,79,76,79,72,79,95,93,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "318,640,953,1040,1128,1211,1309,1410,1493,1558,1655,1749,1820,1890,1954,2022,2103,2184,2264,2341,2421,2494,2574,2670,2764,2832,2897,2950,3008,3056,3117,3187,3256,3319,3384,3447,3504,3580,3649,3723,3775,3838,3915,3992,4046"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,1681,1768,1856,1939,2037,2138,2221,2286,2383,2477,2548,2618,2682,2750,2831,2912,2992,3069,3149,3222,3302,3398,3492,3560,4301,4354,4412,4460,4521,4591,4660,4723,4788,4851,4908,4984,5053,5127,5179,5242,5319,5396", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,80,80,79,76,79,72,79,95,93,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "318,640,953,1763,1851,1934,2032,2133,2216,2281,2378,2472,2543,2613,2677,2745,2826,2907,2987,3064,3144,3217,3297,3393,3487,3555,3620,4349,4407,4455,4516,4586,4655,4718,4783,4846,4903,4979,5048,5122,5174,5237,5314,5391,5445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,183,247,311,386,467,566,657", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "118,178,242,306,381,462,561,652,726"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3625,3693,3753,3817,3881,3956,4037,4136,4227", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "3688,3748,3812,3876,3951,4032,4131,4222,4296"}}]}]}
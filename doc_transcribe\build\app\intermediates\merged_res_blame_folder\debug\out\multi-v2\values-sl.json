{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2568,2751,2851", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,107,182,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2563,2746,2846,2930"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1008,1120,1222,1330,1417,1520,1639,1720,1798,1890,1984,2079,2173,2268,2362,2458,2558,2650,2742,2826,2934,3042,3142,3255,3363,3471,3654,8246", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,107,182,99,83", "endOffsets": "1115,1217,1325,1412,1515,1634,1715,1793,1885,1979,2074,2168,2263,2357,2453,2553,2645,2737,2821,2929,3037,3137,3250,3358,3466,3649,3749,8325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "50,51,52,53,54,55,56,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3754,3851,3953,4051,4155,4258,4360,8330", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3846,3948,4046,4150,4253,4355,4472,8426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,958,1045,1133,1216,1314,1415,1498,1563,1660,1754,1825,1895,1959,2027,2108,2189,2269,2346,2426,2499,2579,2675,2769,2837,2902,2955,3013,3061,3122,3192,3261,3324,3389,3452,3509,3585,3654,3728,3780,3843,3920,3997", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,80,80,79,76,79,72,79,95,93,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "318,640,953,1040,1128,1211,1309,1410,1493,1558,1655,1749,1820,1890,1954,2022,2103,2184,2264,2341,2421,2494,2574,2670,2764,2832,2897,2950,3008,3056,3117,3187,3256,3319,3384,3447,3504,3580,3649,3723,3775,3838,3915,3992,4046"}, "to": {"startLines": "2,11,17,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,373,695,4477,4564,4652,4735,4833,4934,5017,5082,5179,5273,5344,5414,5478,5546,5627,5708,5788,5865,5945,6018,6098,6194,6288,6356,7097,7150,7208,7256,7317,7387,7456,7519,7584,7647,7704,7780,7849,7923,7975,8038,8115,8192", "endLines": "10,16,22,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,80,80,79,76,79,72,79,95,93,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "368,690,1003,4559,4647,4730,4828,4929,5012,5077,5174,5268,5339,5409,5473,5541,5622,5703,5783,5860,5940,6013,6093,6189,6283,6351,6416,7145,7203,7251,7312,7382,7451,7514,7579,7642,7699,7775,7844,7918,7970,8033,8110,8187,8241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,183,247,311,386,467,566,657", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "118,178,242,306,381,462,561,652,726"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6421,6489,6549,6613,6677,6752,6833,6932,7023", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "6484,6544,6608,6672,6747,6828,6927,7018,7092"}}]}]}
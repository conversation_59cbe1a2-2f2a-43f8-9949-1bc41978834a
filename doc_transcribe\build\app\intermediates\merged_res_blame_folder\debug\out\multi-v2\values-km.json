{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,185,251,320,393,464,562,657", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "121,180,246,315,388,459,557,652,721"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3198,3269,3328,3394,3463,3536,3607,3705,3800", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "3264,3323,3389,3458,3531,3602,3700,3795,3864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "654,749,852,950,1050,1151,1263,5003", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "744,847,945,1045,1146,1258,1370,5099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,470,654,731,807,886,970,1058,1139,1205,1292,1381,1446,1508,1570,1636,1719,1798,1879,1951,2036,2107,2190,2270,2349,2411,2477,2530,2588,2638,2699,2758,2826,2896,2965,3032,3091,3157,3222,3289,3344,3401,3478,3555", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,82,78,80,71,84,70,82,79,78,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "280,465,649,726,802,881,965,1053,1134,1200,1287,1376,1441,1503,1565,1631,1714,1793,1874,1946,2031,2102,2185,2265,2344,2406,2472,2525,2583,2633,2694,2753,2821,2891,2960,3027,3086,3152,3217,3284,3339,3396,3473,3550,3606"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,470,1375,1452,1528,1607,1691,1779,1860,1926,2013,2102,2167,2229,2291,2357,2440,2519,2600,2672,2757,2828,2911,2991,3070,3132,3869,3922,3980,4030,4091,4150,4218,4288,4357,4424,4483,4549,4614,4681,4736,4793,4870,4947", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,82,78,80,71,84,70,82,79,78,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "280,465,649,1447,1523,1602,1686,1774,1855,1921,2008,2097,2162,2224,2286,2352,2435,2514,2595,2667,2752,2823,2906,2986,3065,3127,3193,3917,3975,4025,4086,4145,4213,4283,4352,4419,4478,4544,4609,4676,4731,4788,4865,4942,4998"}}]}]}
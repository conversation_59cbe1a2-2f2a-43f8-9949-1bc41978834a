{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2070,2145,2224,2298,2380,2453,2535,2631,2726,2793,2858,2911,2969,3017,3078,3150,3224,3287,3360,3425,3485,3550,3614,3680,3732,3796,3874,3952", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,79,74,78,73,81,72,81,95,94,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2065,2140,2219,2293,2375,2448,2530,2626,2721,2788,2853,2906,2964,3012,3073,3145,3219,3282,3355,3420,3480,3545,3609,3675,3727,3791,3869,3947,4001"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,1675,1756,1836,1918,2021,2120,2199,2264,2355,2449,2519,2585,2650,2727,2807,2882,2961,3035,3117,3190,3272,3368,3463,3530,4266,4319,4377,4425,4486,4558,4632,4695,4768,4833,4893,4958,5022,5088,5140,5204,5282,5360", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,79,74,78,73,81,72,81,95,94,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1751,1831,1913,2016,2115,2194,2259,2350,2444,2514,2580,2645,2722,2802,2877,2956,3030,3112,3185,3267,3363,3458,3525,3590,4314,4372,4420,4481,4553,4627,4690,4763,4828,4888,4953,5017,5083,5135,5199,5277,5355,5409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3595,3672,3734,3798,3869,3946,4020,4104,4186", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "3667,3729,3793,3864,3941,4015,4099,4181,4261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "938,1034,1136,1237,1335,1445,1553,5414", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "1029,1131,1232,1330,1440,1548,1670,5510"}}]}]}
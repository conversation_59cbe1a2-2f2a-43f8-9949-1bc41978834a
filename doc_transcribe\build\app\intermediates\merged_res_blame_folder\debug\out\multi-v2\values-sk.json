{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2070,2145,2224,2298,2380,2453,2535,2631,2726,2793,2858,2911,2969,3017,3078,3150,3224,3287,3360,3425,3485,3550,3614,3680,3732,3796,3874,3952", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,79,74,78,73,81,72,81,95,94,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2065,2140,2219,2293,2375,2448,2530,2626,2721,2788,2853,2906,2964,3012,3073,3145,3219,3282,3355,3420,3480,3545,3609,3675,3727,3791,3869,3947,4001"}, "to": {"startLines": "2,11,17,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,674,4437,4518,4598,4680,4783,4882,4961,5026,5117,5211,5281,5347,5412,5489,5569,5644,5723,5797,5879,5952,6034,6130,6225,6292,7028,7081,7139,7187,7248,7320,7394,7457,7530,7595,7655,7720,7784,7850,7902,7966,8044,8122", "endLines": "10,16,22,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,79,74,78,73,81,72,81,95,94,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "338,669,983,4513,4593,4675,4778,4877,4956,5021,5112,5206,5276,5342,5407,5484,5564,5639,5718,5792,5874,5947,6029,6125,6220,6287,6352,7076,7134,7182,7243,7315,7389,7452,7525,7590,7650,7715,7779,7845,7897,7961,8039,8117,8171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6357,6434,6496,6560,6631,6708,6782,6866,6948", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "6429,6491,6555,6626,6703,6777,6861,6943,7023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "988,1095,1196,1307,1393,1501,1619,1698,1775,1866,1959,2057,2151,2251,2344,2439,2537,2628,2719,2803,2908,3016,3115,3221,3333,3436,3602,8176", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "1090,1191,1302,1388,1496,1614,1693,1770,1861,1954,2052,2146,2246,2339,2434,2532,2623,2714,2798,2903,3011,3110,3216,3328,3431,3597,3695,8254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "50,51,52,53,54,55,56,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3700,3796,3898,3999,4097,4207,4315,8259", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3791,3893,3994,4092,4202,4310,4432,8355"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,184,252,316,401,488,585,681", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "115,179,247,311,396,483,580,676,754"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3318,3383,3447,3515,3579,3664,3751,3848,3944", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "3378,3442,3510,3574,3659,3746,3843,3939,4017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,483,677,757,837,924,1021,1118,1203,1268,1364,1461,1528,1593,1659,1729,1811,1897,1978,2054,2130,2204,2290,2382,2472,2538,2604,2657,2717,2765,2826,2886,2953,3018,3083,3146,3203,3275,3340,3406,3458,3519,3601,3683", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,81,85,80,75,75,73,85,91,89,65,65,52,59,47,60,59,66,64,64,62,56,71,64,65,51,60,81,81,54", "endOffsets": "281,478,672,752,832,919,1016,1113,1198,1263,1359,1456,1523,1588,1654,1724,1806,1892,1973,2049,2125,2199,2285,2377,2467,2533,2599,2652,2712,2760,2821,2881,2948,3013,3078,3141,3198,3270,3335,3401,3453,3514,3596,3678,3733"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,483,1391,1471,1551,1638,1735,1832,1917,1982,2078,2175,2242,2307,2373,2443,2525,2611,2692,2768,2844,2918,3004,3096,3186,3252,4022,4075,4135,4183,4244,4304,4371,4436,4501,4564,4621,4693,4758,4824,4876,4937,5019,5101", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,81,85,80,75,75,73,85,91,89,65,65,52,59,47,60,59,66,64,64,62,56,71,64,65,51,60,81,81,54", "endOffsets": "281,478,672,1466,1546,1633,1730,1827,1912,1977,2073,2170,2237,2302,2368,2438,2520,2606,2687,2763,2839,2913,2999,3091,3181,3247,3313,4070,4130,4178,4239,4299,4366,4431,4496,4559,4616,4688,4753,4819,4871,4932,5014,5096,5151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "677,772,874,976,1079,1183,1280,5156", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "767,869,971,1074,1178,1275,1386,5252"}}]}]}
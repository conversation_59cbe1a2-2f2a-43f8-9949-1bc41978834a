{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,194,262,330,407,480,571,657", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "126,189,257,325,402,475,566,652,731"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3193,3269,3332,3400,3468,3545,3618,3709,3795", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "3264,3327,3395,3463,3540,3613,3704,3790,3869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,656,737,816,895,983,1073,1144,1208,1299,1390,1454,1517,1582,1653,1732,1807,1888,1956,2039,2112,2183,2267,2351,2414,2478,2531,2589,2637,2698,2757,2825,2891,2959,3020,3079,3145,3212,3279,3333,3396,3478,3555", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,78,74,80,67,82,72,70,83,83,62,63,52,57,47,60,58,67,65,67,60,58,65,66,66,53,62,81,76,53", "endOffsets": "278,473,651,732,811,890,978,1068,1139,1203,1294,1385,1449,1512,1577,1648,1727,1802,1883,1951,2034,2107,2178,2262,2346,2409,2473,2526,2584,2632,2693,2752,2820,2886,2954,3015,3074,3140,3207,3274,3328,3391,3473,3550,3604"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,1371,1452,1531,1610,1698,1788,1859,1923,2014,2105,2169,2232,2297,2368,2447,2522,2603,2671,2754,2827,2898,2982,3066,3129,3874,3927,3985,4033,4094,4153,4221,4287,4355,4416,4475,4541,4608,4675,4729,4792,4874,4951", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,78,74,80,67,82,72,70,83,83,62,63,52,57,47,60,58,67,65,67,60,58,65,66,66,53,62,81,76,53", "endOffsets": "278,473,651,1447,1526,1605,1693,1783,1854,1918,2009,2100,2164,2227,2292,2363,2442,2517,2598,2666,2749,2822,2893,2977,3061,3124,3188,3922,3980,4028,4089,4148,4216,4282,4350,4411,4470,4536,4603,4670,4724,4787,4869,4946,5000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "656,752,855,953,1051,1154,1259,5005", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "747,850,948,1046,1149,1254,1366,5101"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,491,692,783,876,957,1053,1145,1216,1283,1372,1459,1527,1592,1655,1727,1815,1899,1985,2062,2144,2216,2287,2380,2469,2534,2598,2651,2709,2757,2818,2886,2958,3027,3099,3166,3221,3286,3352,3418,3470,3531,3606,3681", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,87,83,85,76,81,71,70,92,88,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "282,486,687,778,871,952,1048,1140,1211,1278,1367,1454,1522,1587,1650,1722,1810,1894,1980,2057,2139,2211,2282,2375,2464,2529,2593,2646,2704,2752,2813,2881,2953,3022,3094,3161,3216,3281,3347,3413,3465,3526,3601,3676,3733"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,491,1426,1517,1610,1691,1787,1879,1950,2017,2106,2193,2261,2326,2389,2461,2549,2633,2719,2796,2878,2950,3021,3114,3203,3268,3989,4042,4100,4148,4209,4277,4349,4418,4490,4557,4612,4677,4743,4809,4861,4922,4997,5072", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,87,83,85,76,81,71,70,92,88,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "282,486,687,1512,1605,1686,1782,1874,1945,2012,2101,2188,2256,2321,2384,2456,2544,2628,2714,2791,2873,2945,3016,3109,3198,3263,3327,4037,4095,4143,4204,4272,4344,4413,4485,4552,4607,4672,4738,4804,4856,4917,4992,5067,5124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "692,788,890,988,1093,1198,1310,5129", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "783,885,983,1088,1193,1305,1421,5225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,241,308,385,455,549,641", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "115,174,236,303,380,450,544,636,707"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3332,3397,3456,3518,3585,3662,3732,3826,3918", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "3392,3451,3513,3580,3657,3727,3821,3913,3984"}}]}]}
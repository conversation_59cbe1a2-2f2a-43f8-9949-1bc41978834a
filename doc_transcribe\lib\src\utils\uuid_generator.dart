import 'dart:math';

class UuidGenerator {
  static final Random _random = Random();

  /// Generates a UUID v4 string
  static String generate() {
    // Generate 16 random bytes
    final bytes = List<int>.generate(16, (i) => _random.nextInt(256));
    
    // Set version (4) and variant bits
    bytes[6] = (bytes[6] & 0x0f) | 0x40; // Version 4
    bytes[8] = (bytes[8] & 0x3f) | 0x80; // Variant bits
    
    // Convert to hex string with hyphens
    final hex = bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    
    return '${hex.substring(0, 8)}-'
           '${hex.substring(8, 12)}-'
           '${hex.substring(12, 16)}-'
           '${hex.substring(16, 20)}-'
           '${hex.substring(20, 32)}';
  }

  /// Generates a short UUID (8 characters)
  static String generateShort() {
    const chars = 'abcdef<PERSON><PERSON>jklmnopqrstuvwxyz0123456789';
    return List.generate(8, (index) => chars[_random.nextInt(chars.length)]).join();
  }

  /// Validates if a string is a valid UUID
  static bool isValid(String uuid) {
    final pattern = RegExp(
      r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
      caseSensitive: false,
    );
    return pattern.hasMatch(uuid);
  }
}

import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/transcript.dart';
import '../services/riva_service.dart';
import '../services/speaker_filter_service.dart';

enum TranscriptState {
  idle,
  streaming,
  processing,
  completed,
  error,
}

class TranscriptProvider extends ChangeNotifier {
  final RivaService _rivaService = RivaService();
  final SpeakerFilterService _speakerFilterService = SpeakerFilterService.instance;

  TranscriptState _state = TranscriptState.idle;
  Transcript? _currentTranscript;
  List<TranscriptSegment> _liveSegments = [];
  String? _errorMessage;
  double _processingProgress = 0.0;
  StreamSubscription? _transcriptSubscription;

  // Getters
  TranscriptState get state => _state;
  Transcript? get currentTranscript => _currentTranscript;
  List<TranscriptSegment> get liveSegments => _liveSegments;
  String? get errorMessage => _errorMessage;
  double get processingProgress => _processingProgress;

  bool get isStreaming => _state == TranscriptState.streaming;
  bool get isProcessing => _state == TranscriptState.processing;
  bool get isCompleted => _state == TranscriptState.completed;
  bool get hasError => _state == TranscriptState.error;
  bool get hasTranscript => _currentTranscript != null;

  String get liveTranscriptText {
    return _liveSegments
        .where((segment) => segment.text.isNotEmpty)
        .map((segment) => segment.text)
        .join(' ');
  }

  List<TranscriptSegment> get finalSegments {
    return _liveSegments.where((segment) => segment.isFinal).toList();
  }

  // Start live transcription
  Future<void> startLiveTranscription({
    required String transcriptId,
    required String language,
  }) async {
    try {
      _setState(TranscriptState.streaming);
      _clearError();
      _liveSegments.clear();

      // Create new transcript
      _currentTranscript = Transcript(
        id: transcriptId,
        segments: [],
        startTime: DateTime.now(),
        language: language,
      );

      // Start listening to transcript stream from Riva service
      _transcriptSubscription = _rivaService.transcriptStream.listen(
        _onTranscriptReceived,
        onError: _onTranscriptError,
        onDone: _onTranscriptDone,
      );

      notifyListeners();
    } catch (e) {
      _setError('Failed to start live transcription: $e');
    }
  }

  // Stop live transcription and process final transcript
  Future<void> stopLiveTranscription() async {
    try {
      _setState(TranscriptState.processing);
      _processingProgress = 0.0;

      // Cancel subscription
      await _transcriptSubscription?.cancel();
      _transcriptSubscription = null;

      // Process final transcript with speaker filtering
      await _processFinalTranscript();

      _setState(TranscriptState.completed);
      notifyListeners();
    } catch (e) {
      _setError('Failed to stop live transcription: $e');
    }
  }

  // Process audio file for transcription (offline mode)
  Future<void> processAudioFile({
    required String audioFilePath,
    required String transcriptId,
    required String language,
  }) async {
    try {
      _setState(TranscriptState.processing);
      _clearError();
      _processingProgress = 0.0;

      // Create new transcript
      _currentTranscript = Transcript(
        id: transcriptId,
        segments: [],
        startTime: DateTime.now(),
        language: language,
      );

      // Process audio file through Riva service
      _processingProgress = 0.2;
      notifyListeners();

      final rawSegments = await _rivaService.transcribeAudioFile(
        audioFilePath,
        language,
      );

      _processingProgress = 0.6;
      notifyListeners();

      // Apply speaker filtering
      final filteredSegments = await _speakerFilterService.filterSpeakers(
        audioFilePath,
        rawSegments,
      );

      _processingProgress = 0.9;
      notifyListeners();

      // Update transcript with filtered segments
      _currentTranscript = _currentTranscript!.copyWith(
        segments: filteredSegments,
        endTime: DateTime.now(),
      );

      _processingProgress = 1.0;
      _setState(TranscriptState.completed);
      notifyListeners();
    } catch (e) {
      _setError('Failed to process audio file: $e');
    }
  }

  // Update transcript with doctor's notes
  void updateDoctorNotes(String notes) {
    if (_currentTranscript != null) {
      _currentTranscript = _currentTranscript!.copyWith(doctorNotes: notes);
      notifyListeners();
    }
  }

  // Edit transcript segment
  void editTranscriptSegment(String segmentId, String newText) {
    if (_currentTranscript != null) {
      final updatedSegments = _currentTranscript!.segments.map((segment) {
        if (segment.id == segmentId) {
          return segment.copyWith(text: newText);
        }
        return segment;
      }).toList();

      _currentTranscript = _currentTranscript!.copyWith(segments: updatedSegments);
      notifyListeners();
    }
  }

  // Private methods
  void _onTranscriptReceived(TranscriptSegment segment) {
    // Update or add segment
    final existingIndex = _liveSegments.indexWhere((s) => s.id == segment.id);
    
    if (existingIndex >= 0) {
      _liveSegments[existingIndex] = segment;
    } else {
      _liveSegments.add(segment);
    }

    // Sort segments by timestamp
    _liveSegments.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    notifyListeners();
  }

  void _onTranscriptError(dynamic error) {
    _setError('Transcription error: $error');
  }

  void _onTranscriptDone() {
    // Stream completed normally
    if (_state == TranscriptState.streaming) {
      stopLiveTranscription();
    }
  }

  Future<void> _processFinalTranscript() async {
    if (_currentTranscript == null) return;

    try {
      // Get final segments only
      final finalSegments = _liveSegments.where((s) => s.isFinal).toList();
      
      _processingProgress = 0.3;
      notifyListeners();

      // Apply speaker filtering if we have audio file
      List<TranscriptSegment> filteredSegments = finalSegments;
      
      // For now, we'll use a simple filter that removes segments marked as "other"
      filteredSegments = finalSegments
          .where((segment) => segment.speaker != SpeakerType.other)
          .toList();

      _processingProgress = 0.8;
      notifyListeners();

      // Update transcript with final segments
      _currentTranscript = _currentTranscript!.copyWith(
        segments: filteredSegments,
        endTime: DateTime.now(),
      );

      _processingProgress = 1.0;
      notifyListeners();
    } catch (e) {
      throw Exception('Failed to process final transcript: $e');
    }
  }

  void _setState(TranscriptState newState) {
    _state = newState;
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = TranscriptState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Reset to initial state
  void reset() {
    _transcriptSubscription?.cancel();
    _transcriptSubscription = null;
    _state = TranscriptState.idle;
    _currentTranscript = null;
    _liveSegments.clear();
    _errorMessage = null;
    _processingProgress = 0.0;
    notifyListeners();
  }

  @override
  void dispose() {
    _transcriptSubscription?.cancel();
    super.dispose();
  }
}

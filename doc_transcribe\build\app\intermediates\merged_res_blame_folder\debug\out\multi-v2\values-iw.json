{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,582,875,954,1032,1108,1193,1277,1339,1401,1490,1576,1641,1705,1768,1836,1913,1997,2078,2149,2226,2295,2356,2443,2529,2593,2656,2710,2781,2829,2890,2949,3016,3077,3140,3201,3258,3324,3388,3454,3506,3560,3628,3696", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,76,83,80,70,76,68,60,86,85,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "279,577,870,949,1027,1103,1188,1272,1334,1396,1485,1571,1636,1700,1763,1831,1908,1992,2073,2144,2221,2290,2351,2438,2524,2588,2651,2705,2776,2824,2885,2944,3011,3072,3135,3196,3253,3319,3383,3449,3501,3555,3623,3691,3745"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,582,1572,1651,1729,1805,1890,1974,2036,2098,2187,2273,2338,2402,2465,2533,2610,2694,2775,2846,2923,2992,3053,3140,3226,3290,3967,4021,4092,4140,4201,4260,4327,4388,4451,4512,4569,4635,4699,4765,4817,4871,4939,5007", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,76,83,80,70,76,68,60,86,85,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "279,577,870,1646,1724,1800,1885,1969,2031,2093,2182,2268,2333,2397,2460,2528,2605,2689,2770,2841,2918,2987,3048,3135,3221,3285,3348,4016,4087,4135,4196,4255,4322,4383,4446,4507,4564,4630,4694,4760,4812,4866,4934,5002,5056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3353,3418,3477,3544,3609,3683,3745,3825,3905", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "3413,3472,3539,3604,3678,3740,3820,3900,3962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "875,969,1071,1168,1265,1366,1466,5061", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "964,1066,1163,1260,1361,1461,1567,5157"}}]}]}
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../services/real_audio_service.dart';
import '../services/riva_service.dart';

enum RecordingState {
  idle,
  recording,
  paused,
  stopped,
  processing,
  error,
}

class RecordingProvider extends ChangeNotifier {
  final RealAudioService _audioService = RealAudioService.instance;
  final RivaService _rivaService = RivaService();

  RecordingState _state = RecordingState.idle;
  Duration _recordingDuration = Duration.zero;
  Timer? _durationTimer;
  String? _errorMessage;
  double _audioLevel = 0.0;
  bool _isInitialized = false;

  // Getters
  RecordingState get state => _state;
  Duration get recordingDuration => _recordingDuration;
  String? get errorMessage => _errorMessage;
  double get audioLevel => _audioLevel;
  bool get isInitialized => _isInitialized;

  bool get isRecording => _state == RecordingState.recording;
  bool get isPaused => _state == RecordingState.paused;
  bool get isStopped => _state == RecordingState.stopped;
  bool get isProcessing => _state == RecordingState.processing;
  bool get hasError => _state == RecordingState.error;
  bool get canRecord => _state == RecordingState.idle || _state == RecordingState.stopped;
  bool get canStop => _state == RecordingState.recording || _state == RecordingState.paused;
  bool get canPause => _state == RecordingState.recording;
  bool get canResume => _state == RecordingState.paused;

  String get formattedDuration {
    final hours = _recordingDuration.inHours.toString().padLeft(2, '0');
    final minutes = (_recordingDuration.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (_recordingDuration.inSeconds % 60).toString().padLeft(2, '0');
    return '$hours:$minutes:$seconds';
  }

  // Initialize the recording provider
  Future<void> initialize() async {
    try {
      await _audioService.initialize();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      _setError('Failed to initialize audio service: $e');
    }
  }

  // Start recording
  Future<void> startRecording({
    required String apiKey,
    required String language,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!canRecord) {
      throw Exception('Cannot start recording in current state: $_state');
    }

    try {
      _setState(RecordingState.recording);
      _recordingDuration = Duration.zero;
      _clearError();

      // Initialize Riva service first
      await _rivaService.initialize(apiKey: apiKey, language: language);

      // Start audio recording with streaming enabled
      await _audioService.startRecording(enableStreaming: true);

      // Connect audio stream to Riva for real-time transcription
      if (_audioService.audioStream != null) {
        await _rivaService.startStreamingRecognition(_audioService.audioStream!);
      }

      // Start duration timer
      _startDurationTimer();

      // Start audio level monitoring
      _startAudioLevelMonitoring();

      notifyListeners();
    } catch (e) {
      _setError('Failed to start recording: $e');
      await _cleanup();
    }
  }

  // Stop recording
  Future<String?> stopRecording() async {
    if (!canStop) {
      throw Exception('Cannot stop recording in current state: $_state');
    }

    try {
      _setState(RecordingState.stopped);
      _stopDurationTimer();
      _stopAudioLevelMonitoring();

      // Stop audio recording and get file path
      final audioFilePath = await _audioService.stopRecording();

      // Stop Riva service
      await _rivaService.disconnect();

      notifyListeners();
      return audioFilePath;
    } catch (e) {
      _setError('Failed to stop recording: $e');
      await _cleanup();
      return null;
    }
  }

  // Pause recording
  Future<void> pauseRecording() async {
    if (!canPause) {
      throw Exception('Cannot pause recording in current state: $_state');
    }

    try {
      _setState(RecordingState.paused);
      await _audioService.pauseRecording();
      _stopDurationTimer();
      _stopAudioLevelMonitoring();
      notifyListeners();
    } catch (e) {
      _setError('Failed to pause recording: $e');
    }
  }

  // Resume recording
  Future<void> resumeRecording() async {
    if (!canResume) {
      throw Exception('Cannot resume recording in current state: $_state');
    }

    try {
      _setState(RecordingState.recording);
      await _audioService.resumeRecording();
      _startDurationTimer();
      _startAudioLevelMonitoring();
      notifyListeners();
    } catch (e) {
      _setError('Failed to resume recording: $e');
    }
  }

  // Cancel recording
  Future<void> cancelRecording() async {
    try {
      _setState(RecordingState.idle);
      await _cleanup();
      _recordingDuration = Duration.zero;
      notifyListeners();
    } catch (e) {
      _setError('Failed to cancel recording: $e');
    }
  }

  // Private methods
  void _setState(RecordingState newState) {
    _state = newState;
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = RecordingState.error;
    _cleanup();
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  void _startDurationTimer() {
    _durationTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _recordingDuration = Duration(seconds: _recordingDuration.inSeconds + 1);
      notifyListeners();
    });
  }

  void _stopDurationTimer() {
    _durationTimer?.cancel();
    _durationTimer = null;
  }

  void _startAudioLevelMonitoring() {
    // Monitor real audio levels from the microphone
    Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_state != RecordingState.recording) {
        timer.cancel();
        return;
      }
      // Get real audio level from the audio service
      _audioLevel = _audioService.getCurrentRecordingLevel();
      notifyListeners();
    });
  }

  void _stopAudioLevelMonitoring() {
    _audioLevel = 0.0;
  }

  Future<void> _cleanup() async {
    _stopDurationTimer();
    _stopAudioLevelMonitoring();
    await _audioService.cleanup();
    await _rivaService.disconnect();
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }

  // Reset to initial state
  void reset() {
    _cleanup();
    _state = RecordingState.idle;
    _recordingDuration = Duration.zero;
    _errorMessage = null;
    _audioLevel = 0.0;
    notifyListeners();
  }
}

{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,190,254,322,403,480,554,631", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "122,185,249,317,398,475,549,626,704"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3244,3316,3379,3443,3511,3592,3669,3743,3820", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "3311,3374,3438,3506,3587,3664,3738,3815,3893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "659,759,864,962,1061,1166,1268,5039", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "754,859,957,1056,1161,1263,1374,5135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,477,659,742,824,894,985,1081,1157,1220,1321,1424,1494,1562,1630,1696,1774,1846,1922,1986,2067,2144,2222,2307,2390,2459,2524,2577,2637,2685,2746,2814,2882,2955,3022,3083,3144,3211,3276,3346,3398,3460,3536,3612", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,77,71,75,63,80,76,77,84,82,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "283,472,654,737,819,889,980,1076,1152,1215,1316,1419,1489,1557,1625,1691,1769,1841,1917,1981,2062,2139,2217,2302,2385,2454,2519,2572,2632,2680,2741,2809,2877,2950,3017,3078,3139,3206,3271,3341,3393,3455,3531,3607,3660"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,477,1379,1462,1544,1614,1705,1801,1877,1940,2041,2144,2214,2282,2350,2416,2494,2566,2642,2706,2787,2864,2942,3027,3110,3179,3898,3951,4011,4059,4120,4188,4256,4329,4396,4457,4518,4585,4650,4720,4772,4834,4910,4986", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,77,71,75,63,80,76,77,84,82,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "283,472,654,1457,1539,1609,1700,1796,1872,1935,2036,2139,2209,2277,2345,2411,2489,2561,2637,2701,2782,2859,2937,3022,3105,3174,3239,3946,4006,4054,4115,4183,4251,4324,4391,4452,4513,4580,4645,4715,4767,4829,4905,4981,5034"}}]}]}
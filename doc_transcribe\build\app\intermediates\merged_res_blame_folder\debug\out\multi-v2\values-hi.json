{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,699,782,863,946,1041,1141,1210,1273,1359,1445,1510,1574,1638,1706,1785,1867,1945,2018,2102,2171,2240,2322,2404,2471,2534,2587,2649,2703,2764,2824,2891,2954,3024,3085,3147,3213,3276,3343,3403,3463,3537,3611", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,78,81,77,72,83,68,68,81,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "281,488,694,777,858,941,1036,1136,1205,1268,1354,1440,1505,1569,1633,1701,1780,1862,1940,2013,2097,2166,2235,2317,2399,2466,2529,2582,2644,2698,2759,2819,2886,2949,3019,3080,3142,3208,3271,3338,3398,3458,3532,3606,3659"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,1452,1535,1616,1699,1794,1894,1963,2026,2112,2198,2263,2327,2391,2459,2538,2620,2698,2771,2855,2924,2993,3075,3157,3224,4055,4108,4170,4224,4285,4345,4412,4475,4545,4606,4668,4734,4797,4864,4924,4984,5058,5132", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,78,81,77,72,83,68,68,81,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "281,488,694,1530,1611,1694,1789,1889,1958,2021,2107,2193,2258,2322,2386,2454,2533,2615,2693,2766,2850,2919,2988,3070,3152,3219,3282,4103,4165,4219,4280,4340,4407,4470,4540,4601,4663,4729,4792,4859,4919,4979,5053,5127,5180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3287,3355,3421,3492,3560,3656,3724,3847,3968", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "3350,3416,3487,3555,3651,3719,3842,3963,4050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "699,797,900,1005,1106,1219,1325,5185", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "792,895,1000,1101,1214,1320,1447,5281"}}]}]}
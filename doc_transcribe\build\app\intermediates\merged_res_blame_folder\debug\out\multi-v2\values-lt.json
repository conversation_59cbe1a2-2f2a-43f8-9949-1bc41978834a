{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,298,622,933,1017,1100,1178,1275,1372,1446,1510,1606,1702,1773,1838,1901,1974,2052,2132,2210,2282,2358,2431,2505,2589,2671,2740,2807,2860,2918,2973,3034,3100,3169,3234,3302,3366,3424,3497,3564,3638,3697,3760,3837,3914", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,77,79,77,71,75,72,73,83,81,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "293,617,928,1012,1095,1173,1270,1367,1441,1505,1601,1697,1768,1833,1896,1969,2047,2127,2205,2277,2353,2426,2500,2584,2666,2735,2802,2855,2913,2968,3029,3095,3164,3229,3297,3361,3419,3492,3559,3633,3692,3755,3832,3909,3965"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,298,622,1684,1768,1851,1929,2026,2123,2197,2261,2357,2453,2524,2589,2652,2725,2803,2883,2961,3033,3109,3182,3256,3340,3422,3491,4260,4313,4371,4426,4487,4553,4622,4687,4755,4819,4877,4950,5017,5091,5150,5213,5290,5367", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,77,79,77,71,75,72,73,83,81,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "293,617,928,1763,1846,1924,2021,2118,2192,2256,2352,2448,2519,2584,2647,2720,2798,2878,2956,3028,3104,3177,3251,3335,3417,3486,3553,4308,4366,4421,4482,4548,4617,4682,4750,4814,4872,4945,5012,5086,5145,5208,5285,5362,5418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "933,1031,1141,1240,1343,1454,1564,5423", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "1026,1136,1235,1338,1449,1559,1679,5519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,267,335,416,490,587,682", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "131,195,262,330,411,485,582,677,752"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3558,3639,3703,3770,3838,3919,3993,4090,4185", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "3634,3698,3765,3833,3914,3988,4085,4180,4255"}}]}]}
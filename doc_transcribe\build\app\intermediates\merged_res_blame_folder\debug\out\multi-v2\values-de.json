{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "692,790,892,992,1092,1200,1305,5162", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "785,887,987,1087,1195,1300,1418,5258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,692,779,867,942,1032,1118,1197,1262,1366,1470,1539,1609,1681,1750,1831,1914,1996,2069,2153,2229,2306,2386,2464,2530,2595,2648,2708,2756,2817,2889,2959,3024,3095,3160,3218,3284,3348,3414,3466,3528,3604,3680", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,80,82,81,72,83,75,76,79,77,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "306,496,687,774,862,937,1027,1113,1192,1257,1361,1465,1534,1604,1676,1745,1826,1909,1991,2064,2148,2224,2301,2381,2459,2525,2590,2643,2703,2751,2812,2884,2954,3019,3090,3155,3213,3279,3343,3409,3461,3523,3599,3675,3731"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,1423,1510,1598,1673,1763,1849,1928,1993,2097,2201,2270,2340,2412,2481,2562,2645,2727,2800,2884,2960,3037,3117,3195,3261,4021,4074,4134,4182,4243,4315,4385,4450,4521,4586,4644,4710,4774,4840,4892,4954,5030,5106", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,80,82,81,72,83,75,76,79,77,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "306,496,687,1505,1593,1668,1758,1844,1923,1988,2092,2196,2265,2335,2407,2476,2557,2640,2722,2795,2879,2955,3032,3112,3190,3256,3321,4069,4129,4177,4238,4310,4380,4445,4516,4581,4639,4705,4769,4835,4887,4949,5025,5101,5157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,270,342,419,486,583,674", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "124,191,265,337,414,481,578,669,745"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3326,3400,3467,3541,3613,3690,3757,3854,3945", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "3395,3462,3536,3608,3685,3752,3849,3940,4016"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,185,251,316,391,461,553,640", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "119,180,246,311,386,456,548,635,707"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3173,3242,3303,3369,3434,3509,3579,3671,3758", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "3237,3298,3364,3429,3504,3574,3666,3753,3825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,655,737,817,894,982,1064,1140,1204,1297,1389,1459,1523,1586,1656,1735,1811,1885,1953,2030,2100,2176,2259,2342,2404,2467,2520,2578,2626,2687,2746,2814,2875,2941,3005,3064,3128,3195,3262,3316,3376,3450,3524", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,78,75,73,67,76,69,75,82,82,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "281,472,650,732,812,889,977,1059,1135,1199,1292,1384,1454,1518,1581,1651,1730,1806,1880,1948,2025,2095,2171,2254,2337,2399,2462,2515,2573,2621,2682,2741,2809,2870,2936,3000,3059,3123,3190,3257,3311,3371,3445,3519,3575"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,1361,1443,1523,1600,1688,1770,1846,1910,2003,2095,2165,2229,2292,2362,2441,2517,2591,2659,2736,2806,2882,2965,3048,3110,3830,3883,3941,3989,4050,4109,4177,4238,4304,4368,4427,4491,4558,4625,4679,4739,4813,4887", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,78,75,73,67,76,69,75,82,82,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "281,472,650,1438,1518,1595,1683,1765,1841,1905,1998,2090,2160,2224,2287,2357,2436,2512,2586,2654,2731,2801,2877,2960,3043,3105,3168,3878,3936,3984,4045,4104,4172,4233,4299,4363,4422,4486,4553,4620,4674,4734,4808,4882,4938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "655,751,854,953,1051,1152,1250,4943", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "746,849,948,1046,1147,1245,1356,5039"}}]}]}
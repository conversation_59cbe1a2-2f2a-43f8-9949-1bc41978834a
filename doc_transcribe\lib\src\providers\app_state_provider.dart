import 'package:flutter/foundation.dart';
import '../models/consultation.dart';

enum AppScreen {
  home,
  patientInfo,
  recording,
  processing,
  review,
  history,
  settings,
}

class AppStateProvider extends ChangeNotifier {
  AppScreen _currentScreen = AppScreen.home;
  Consultation? _currentConsultation;
  String? _doctorName;
  String? _apiKey;
  String _selectedLanguage = 'en-US';
  bool _isOffline = false;
  String? _errorMessage;

  // Getters
  AppScreen get currentScreen => _currentScreen;
  Consultation? get currentConsultation => _currentConsultation;
  String? get doctorName => _doctorName;
  String? get apiKey => _apiKey;
  String get selectedLanguage => _selectedLanguage;
  bool get isOffline => _isOffline;
  String? get errorMessage => _errorMessage;

  // Navigation methods
  void navigateToScreen(AppScreen screen) {
    _currentScreen = screen;
    notifyListeners();
  }

  void navigateToHome() {
    _currentScreen = AppScreen.home;
    _currentConsultation = null;
    _errorMessage = null;
    notifyListeners();
  }

  void navigateToPatientInfo() {
    _currentScreen = AppScreen.patientInfo;
    notifyListeners();
  }

  void navigateToRecording(Consultation consultation) {
    _currentConsultation = consultation;
    _currentScreen = AppScreen.recording;
    notifyListeners();
  }

  void navigateToProcessing() {
    _currentScreen = AppScreen.processing;
    notifyListeners();
  }

  void navigateToReview() {
    _currentScreen = AppScreen.review;
    notifyListeners();
  }

  void navigateToHistory() {
    _currentScreen = AppScreen.history;
    notifyListeners();
  }

  void navigateToSettings() {
    _currentScreen = AppScreen.settings;
    notifyListeners();
  }

  // Consultation management
  void setCurrentConsultation(Consultation consultation) {
    _currentConsultation = consultation;
    notifyListeners();
  }

  void updateCurrentConsultation(Consultation consultation) {
    _currentConsultation = consultation;
    notifyListeners();
  }

  void clearCurrentConsultation() {
    _currentConsultation = null;
    notifyListeners();
  }

  // Settings management
  void setDoctorName(String? name) {
    _doctorName = name;
    notifyListeners();
  }

  void setApiKey(String? key) {
    _apiKey = key;
    notifyListeners();
  }

  void setSelectedLanguage(String language) {
    _selectedLanguage = language;
    notifyListeners();
  }

  // Network status
  void setOfflineStatus(bool offline) {
    _isOffline = offline;
    notifyListeners();
  }

  // Error handling
  void setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Utility methods
  bool get hasValidApiKey => _apiKey != null && _apiKey!.isNotEmpty;
  
  bool get canStartRecording => 
      hasValidApiKey && 
      _currentConsultation != null && 
      !_isOffline;

  Map<String, String> get supportedLanguages => {
    'en-US': 'English (US)',
    'es-ES': 'Spanish (Spain)',
    'hi-IN': 'Hindi (India)',
    'fr-FR': 'French (France)',
    'de-DE': 'German (Germany)',
    'it-IT': 'Italian (Italy)',
    'pt-BR': 'Portuguese (Brazil)',
    'ja-JP': 'Japanese (Japan)',
    'ko-KR': 'Korean (Korea)',
    'zh-CN': 'Chinese (Simplified)',
  };

  void reset() {
    _currentScreen = AppScreen.home;
    _currentConsultation = null;
    _errorMessage = null;
    notifyListeners();
  }
}

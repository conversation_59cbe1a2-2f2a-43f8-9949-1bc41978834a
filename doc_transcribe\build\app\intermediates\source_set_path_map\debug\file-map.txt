com.example.doc_transcribe.app-jetified-window-java-1.2.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\019f1354596c9157de919eeaf34898f6\transformed\jetified-window-java-1.2.0\res
com.example.doc_transcribe.app-appcompat-1.2.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\0d4fa9196a46da0fe30cf290869c0497\transformed\appcompat-1.2.0\res
com.example.doc_transcribe.app-jetified-tracing-1.2.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\10e3d3c57b390b9cf9961d7096409fa8\transformed\jetified-tracing-1.2.0\res
com.example.doc_transcribe.app-jetified-annotation-experimental-1.4.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\30f980438aad3643f9d80ee4b88a5829\transformed\jetified-annotation-experimental-1.4.0\res
com.example.doc_transcribe.app-core-runtime-2.2.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\34b683b690f10e6e11baa9a53be22c0b\transformed\core-runtime-2.2.0\res
com.example.doc_transcribe.app-jetified-startup-runtime-1.1.1-5 C:\Users\<USER>\.gradle\caches\transforms-3\43980c3f7213c92c8646e00d332d5bd5\transformed\jetified-startup-runtime-1.1.1\res
com.example.doc_transcribe.app-jetified-window-1.2.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\res
com.example.doc_transcribe.app-recyclerview-1.2.1-7 C:\Users\<USER>\.gradle\caches\transforms-3\6cc079d3a83268c2582a050621870aa2\transformed\recyclerview-1.2.1\res
com.example.doc_transcribe.app-lifecycle-livedata-2.7.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\71fb58f0bddf09641dfc956a06c8bca6\transformed\lifecycle-livedata-2.7.0\res
com.example.doc_transcribe.app-jetified-core-ktx-1.13.1-9 C:\Users\<USER>\.gradle\caches\transforms-3\8bd7f33683ffd85cac249325c937f42f\transformed\jetified-core-ktx-1.13.1\res
com.example.doc_transcribe.app-jetified-core-1.0.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\9961c5ce45b8c69bb8f4ca386e222dbf\transformed\jetified-core-1.0.0\res
com.example.doc_transcribe.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\a86d3ec1669f10da667b85e573f87715\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.doc_transcribe.app-lifecycle-runtime-2.7.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\aa11fe827c549893c9e5c2fc6854bbf6\transformed\lifecycle-runtime-2.7.0\res
com.example.doc_transcribe.app-media-1.4.3-13 C:\Users\<USER>\.gradle\caches\transforms-3\b30e4dc937276f023b4a6e07a5156550\transformed\media-1.4.3\res
com.example.doc_transcribe.app-core-1.13.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\res
com.example.doc_transcribe.app-jetified-activity-1.8.1-15 C:\Users\<USER>\.gradle\caches\transforms-3\bdf28e1efaec1f3184a5c597f2146f91\transformed\jetified-activity-1.8.1\res
com.example.doc_transcribe.app-jetified-profileinstaller-1.3.1-16 C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\res
com.example.doc_transcribe.app-jetified-lifecycle-livedata-core-ktx-2.7.0-17 C:\Users\<USER>\.gradle\caches\transforms-3\bed24815a27ea775e9a934e8154f0fb6\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.doc_transcribe.app-fragment-1.7.1-18 C:\Users\<USER>\.gradle\caches\transforms-3\bf6ae328e54b92e9215264b71b8866bc\transformed\fragment-1.7.1\res
com.example.doc_transcribe.app-jetified-exoplayer-core-2.17.1-19 C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\res
com.example.doc_transcribe.app-jetified-lifecycle-process-2.7.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\res
com.example.doc_transcribe.app-jetified-exoplayer-ui-2.17.1-21 C:\Users\<USER>\.gradle\caches\transforms-3\cc4c0aa845d72fda0dd7b9b816c58c3c\transformed\jetified-exoplayer-ui-2.17.1\res
com.example.doc_transcribe.app-jetified-appcompat-resources-1.2.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\d5d93a2c3f09faef6384fb2c8c6236ff\transformed\jetified-appcompat-resources-1.2.0\res
com.example.doc_transcribe.app-jetified-savedstate-1.2.1-23 C:\Users\<USER>\.gradle\caches\transforms-3\db865cfc583742df8a08e9a06de1f57a\transformed\jetified-savedstate-1.2.1\res
com.example.doc_transcribe.app-jetified-flutter_sound_core-9.28.0-24 C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\res
com.example.doc_transcribe.app-lifecycle-livedata-core-2.7.0-25 C:\Users\<USER>\.gradle\caches\transforms-3\f76e81b3835858943920f570981ec666\transformed\lifecycle-livedata-core-2.7.0\res
com.example.doc_transcribe.app-lifecycle-viewmodel-2.7.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\fd0a2dbf4f96c6dbec5c3dc6707cb9de\transformed\lifecycle-viewmodel-2.7.0\res
com.example.doc_transcribe.app-debug-27 C:\python_programs\DocScribe\doc_transcribe\android\app\src\debug\res
com.example.doc_transcribe.app-main-28 C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\res
com.example.doc_transcribe.app-pngs-29 C:\python_programs\DocScribe\doc_transcribe\build\app\generated\res\pngs\debug
com.example.doc_transcribe.app-resValues-30 C:\python_programs\DocScribe\doc_transcribe\build\app\generated\res\resValues\debug
com.example.doc_transcribe.app-rs-31 C:\python_programs\DocScribe\doc_transcribe\build\app\generated\res\rs\debug
com.example.doc_transcribe.app-mergeDebugResources-32 C:\python_programs\DocScribe\doc_transcribe\build\app\intermediates\incremental\debug\mergeDebugResources\merged.dir
com.example.doc_transcribe.app-mergeDebugResources-33 C:\python_programs\DocScribe\doc_transcribe\build\app\intermediates\incremental\debug\mergeDebugResources\stripped.dir
com.example.doc_transcribe.app-merged_res-34 C:\python_programs\DocScribe\doc_transcribe\build\app\intermediates\merged_res\debug
com.example.doc_transcribe.app-packaged_res-35 C:\python_programs\DocScribe\doc_transcribe\build\audio_waveforms\intermediates\packaged_res\debug
com.example.doc_transcribe.app-packaged_res-36 C:\python_programs\DocScribe\doc_transcribe\build\flutter_sound\intermediates\packaged_res\debug
com.example.doc_transcribe.app-packaged_res-37 C:\python_programs\DocScribe\doc_transcribe\build\path_provider_android\intermediates\packaged_res\debug
com.example.doc_transcribe.app-packaged_res-38 C:\python_programs\DocScribe\doc_transcribe\build\permission_handler_android\intermediates\packaged_res\debug
com.example.doc_transcribe.app-packaged_res-39 C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\packaged_res\debug
com.example.doc_transcribe.app-packaged_res-40 C:\python_programs\DocScribe\doc_transcribe\build\record_android\intermediates\packaged_res\debug
com.example.doc_transcribe.app-packaged_res-41 C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\packaged_res\debug

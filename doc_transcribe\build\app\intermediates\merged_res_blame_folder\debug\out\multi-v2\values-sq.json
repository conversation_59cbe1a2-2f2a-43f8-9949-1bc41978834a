{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,254,320,398,477,569,655", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "121,182,249,315,393,472,564,650,720"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3267,3338,3399,3466,3532,3610,3689,3781,3867", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "3333,3394,3461,3527,3605,3684,3776,3862,3932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "671,770,872,970,1067,1175,1286,5068", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "765,867,965,1062,1170,1281,1403,5164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,485,671,752,833,919,1023,1115,1188,1251,1341,1431,1496,1559,1626,1694,1777,1860,1937,2004,2086,2158,2231,2315,2396,2460,2530,2583,2641,2689,2750,2815,2881,2943,3011,3075,3134,3200,3265,3331,3383,3448,3526,3604", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,82,82,76,66,81,71,72,83,80,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "281,480,666,747,828,914,1018,1110,1183,1246,1336,1426,1491,1554,1621,1689,1772,1855,1932,1999,2081,2153,2226,2310,2391,2455,2525,2578,2636,2684,2745,2810,2876,2938,3006,3070,3129,3195,3260,3326,3378,3443,3521,3599,3656"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,485,1408,1489,1570,1656,1760,1852,1925,1988,2078,2168,2233,2296,2363,2431,2514,2597,2674,2741,2823,2895,2968,3052,3133,3197,3937,3990,4048,4096,4157,4222,4288,4350,4418,4482,4541,4607,4672,4738,4790,4855,4933,5011", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,82,82,76,66,81,71,72,83,80,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "281,480,666,1484,1565,1651,1755,1847,1920,1983,2073,2163,2228,2291,2358,2426,2509,2592,2669,2736,2818,2890,2963,3047,3128,3192,3262,3985,4043,4091,4152,4217,4283,4345,4413,4477,4536,4602,4667,4733,4785,4850,4928,5006,5063"}}]}]}
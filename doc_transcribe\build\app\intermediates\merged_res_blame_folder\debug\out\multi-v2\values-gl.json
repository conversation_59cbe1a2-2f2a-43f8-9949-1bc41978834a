{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,192,257,336,413,489,588,684", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "124,187,252,331,408,484,583,679,748"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3349,3423,3486,3551,3630,3707,3783,3882,3978", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "3418,3481,3546,3625,3702,3778,3877,3973,4042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "682,781,883,983,1081,1188,1294,5200", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "776,878,978,1076,1183,1289,1405,5296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,682,768,856,935,1033,1128,1205,1272,1372,1472,1538,1607,1674,1745,1833,1909,1992,2063,2149,2225,2302,2398,2493,2557,2621,2674,2732,2780,2841,2906,2976,3042,3114,3184,3252,3318,3383,3449,3502,3564,3640,3716", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,87,75,82,70,85,75,76,95,94,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "286,494,677,763,851,930,1028,1123,1200,1267,1367,1467,1533,1602,1669,1740,1828,1904,1987,2058,2144,2220,2297,2393,2488,2552,2616,2669,2727,2775,2836,2901,2971,3037,3109,3179,3247,3313,3378,3444,3497,3559,3635,3711,3769"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,1410,1496,1584,1663,1761,1856,1933,2000,2100,2200,2266,2335,2402,2473,2561,2637,2720,2791,2877,2953,3030,3126,3221,3285,4047,4100,4158,4206,4267,4332,4402,4468,4540,4610,4678,4744,4809,4875,4928,4990,5066,5142", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,87,75,82,70,85,75,76,95,94,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "286,494,677,1491,1579,1658,1756,1851,1928,1995,2095,2195,2261,2330,2397,2468,2556,2632,2715,2786,2872,2948,3025,3121,3216,3280,3344,4095,4153,4201,4262,4327,4397,4463,4535,4605,4673,4739,4804,4870,4923,4985,5061,5137,5195"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,192,257,336,413,489,588,684", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "124,187,252,331,408,484,583,679,748"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6147,6221,6284,6349,6428,6505,6581,6680,6776", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "6216,6279,6344,6423,6500,6576,6675,6771,6840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3480,3579,3681,3781,3879,3986,4092,8081", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "3574,3676,3776,3874,3981,4087,4203,8177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,820,900,992,1086,1183,1277,1377,1471,1567,1662,1754,1846,1927,2035,2142,2249,2358,2463,2577,2754,2853", "endColumns": "103,103,107,84,100,127,84,79,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,815,895,987,1081,1178,1272,1372,1466,1562,1657,1749,1841,1922,2030,2137,2244,2353,2458,2572,2749,2848,2931"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "732,836,940,1048,1133,1234,1362,1447,1527,1619,1713,1810,1904,2004,2098,2194,2289,2381,2473,2554,2662,2769,2876,2985,3090,3204,3381,7998", "endColumns": "103,103,107,84,100,127,84,79,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "831,935,1043,1128,1229,1357,1442,1522,1614,1708,1805,1899,1999,2093,2189,2284,2376,2468,2549,2657,2764,2871,2980,3085,3199,3376,3475,8076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,682,768,856,935,1033,1128,1205,1272,1372,1472,1538,1607,1674,1745,1833,1909,1992,2063,2149,2225,2302,2398,2493,2557,2621,2674,2732,2780,2841,2906,2976,3042,3114,3184,3252,3318,3383,3449,3502,3564,3640,3716", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,87,75,82,70,85,75,76,95,94,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "286,494,677,763,851,930,1028,1123,1200,1267,1367,1467,1533,1602,1669,1740,1828,1904,1987,2058,2144,2220,2297,2393,2488,2552,2616,2669,2727,2775,2836,2901,2971,3037,3109,3179,3247,3313,3378,3444,3497,3559,3635,3711,3769"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,341,549,4208,4294,4382,4461,4559,4654,4731,4798,4898,4998,5064,5133,5200,5271,5359,5435,5518,5589,5675,5751,5828,5924,6019,6083,6845,6898,6956,7004,7065,7130,7200,7266,7338,7408,7476,7542,7607,7673,7726,7788,7864,7940", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,87,75,82,70,85,75,76,95,94,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "336,544,727,4289,4377,4456,4554,4649,4726,4793,4893,4993,5059,5128,5195,5266,5354,5430,5513,5584,5670,5746,5823,5919,6014,6078,6142,6893,6951,6999,7060,7125,7195,7261,7333,7403,7471,7537,7602,7668,7721,7783,7859,7935,7993"}}]}]}
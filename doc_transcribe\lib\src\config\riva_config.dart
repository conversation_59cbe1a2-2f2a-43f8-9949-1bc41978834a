/// Configuration constants for NVIDIA Riva ASR integration
class RivaConfig {
  // NVIDIA Riva endpoint configuration
  static const String defaultEndpoint = 'grpc.nvcf.nvidia.com:443';
  static const int defaultPort = 443;
  static const bool useSSL = true;

  // Canary-1B ASR model configuration
  static const String canaryFunctionId = 'ee8dc628-76de-4acc-8595-1836e7e857bd';
  
  // Audio configuration for optimal Riva performance
  static const int sampleRate = 16000; // 16kHz
  static const int channels = 1; // Mono
  static const int bitsPerSample = 16; // 16-bit
  static const String audioEncoding = 'LINEAR16';
  
  // Streaming configuration
  static const int chunkSizeMs = 100; // 100ms chunks
  static const int maxAlternatives = 1;
  static const bool enableAutomaticPunctuation = true;
  static const bool enableWordTimeOffsets = true;
  
  // Language codes supported by Canary-1B ASR
  static const Map<String, String> supportedLanguages = {
    'en-US': 'English (United States)',
    'en-GB': 'English (United Kingdom)',
    'es-ES': 'Spanish (Spain)',
    'es-MX': 'Spanish (Mexico)',
    'fr-FR': 'French (France)',
    'de-DE': 'German (Germany)',
    'it-IT': 'Italian (Italy)',
    'pt-BR': 'Portuguese (Brazil)',
    'ru-RU': 'Russian (Russia)',
    'ja-JP': 'Japanese (Japan)',
    'ko-KR': 'Korean (South Korea)',
    'zh-CN': 'Chinese (Simplified)',
    'zh-TW': 'Chinese (Traditional)',
    'hi-IN': 'Hindi (India)',
    'ar-SA': 'Arabic (Saudi Arabia)',
  };

  // Default language
  static const String defaultLanguage = 'en-US';

  // Timeout configurations
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration streamingTimeout = Duration(minutes: 30);
  static const Duration responseTimeout = Duration(seconds: 10);

  // Retry configuration
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  // Confidence thresholds
  static const double minConfidenceThreshold = 0.5;
  static const double highConfidenceThreshold = 0.8;

  // Speaker diarization settings
  static const bool enableSpeakerDiarization = true;
  static const int maxSpeakers = 3; // Doctor, Patient, Other
  static const double speakerChangeThreshold = 0.7;

  /// Get the gRPC metadata for Riva API calls
  static Map<String, String> getGrpcMetadata(String apiKey) {
    return {
      'function-id': canaryFunctionId,
      'authorization': 'Bearer $apiKey',
    };
  }

  /// Validate if the provided API key format is correct
  static bool isValidApiKey(String apiKey) {
    return apiKey.isNotEmpty && 
           apiKey.startsWith('nvapi-') && 
           apiKey.length > 20;
  }

  /// Get audio configuration for the specified language
  static Map<String, dynamic> getAudioConfig(String languageCode) {
    return {
      'encoding': audioEncoding,
      'sampleRateHertz': sampleRate,
      'audioChannelCount': channels,
      'languageCode': languageCode,
      'maxAlternatives': maxAlternatives,
      'enableAutomaticPunctuation': enableAutomaticPunctuation,
      'enableWordTimeOffsets': enableWordTimeOffsets,
    };
  }

  /// Get streaming configuration
  static Map<String, dynamic> getStreamingConfig() {
    return {
      'interimResults': true,
      'singleUtterance': false,
      'enableVoiceActivityDetection': true,
    };
  }

  /// Check if a language is supported
  static bool isLanguageSupported(String languageCode) {
    return supportedLanguages.containsKey(languageCode);
  }

  /// Get the display name for a language code
  static String getLanguageDisplayName(String languageCode) {
    return supportedLanguages[languageCode] ?? languageCode;
  }

  /// Get the default endpoint URL
  static String getEndpointUrl() {
    return '$defaultEndpoint:$defaultPort';
  }
}

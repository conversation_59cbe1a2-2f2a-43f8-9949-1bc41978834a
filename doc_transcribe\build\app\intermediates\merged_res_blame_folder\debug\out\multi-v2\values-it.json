{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3331,3402,3463,3535,3605,3681,3747,3834,3919", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "3397,3458,3530,3600,3676,3742,3829,3914,3988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "663,761,863,962,1064,1173,1280,5142", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "756,858,957,1059,1168,1275,1405,5238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,663,749,837,916,1008,1100,1178,1243,1343,1441,1506,1574,1639,1710,1787,1870,1945,2015,2108,2183,2259,2355,2447,2516,2584,2637,2695,2743,2804,2878,2949,3012,3093,3151,3212,3278,3343,3409,3461,3523,3599,3675", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,76,82,74,69,92,74,75,95,91,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "281,470,658,744,832,911,1003,1095,1173,1238,1338,1436,1501,1569,1634,1705,1782,1865,1940,2010,2103,2178,2254,2350,2442,2511,2579,2632,2690,2738,2799,2873,2944,3007,3088,3146,3207,3273,3338,3404,3456,3518,3594,3670,3728"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,1410,1496,1584,1663,1755,1847,1925,1990,2090,2188,2253,2321,2386,2457,2534,2617,2692,2762,2855,2930,3006,3102,3194,3263,3993,4046,4104,4152,4213,4287,4358,4421,4502,4560,4621,4687,4752,4818,4870,4932,5008,5084", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,76,82,74,69,92,74,75,95,91,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "281,470,658,1491,1579,1658,1750,1842,1920,1985,2085,2183,2248,2316,2381,2452,2529,2612,2687,2757,2850,2925,3001,3097,3189,3258,3326,4041,4099,4147,4208,4282,4353,4416,4497,4555,4616,4682,4747,4813,4865,4927,5003,5079,5137"}}]}]}
1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="net.nfet.flutter.printing" >
4
5    <uses-sdk
6        android:minSdkVersion="21"
6-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml
7        android:targetSdkVersion="21" />
7-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml
8
9    <application>
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml:4:5-14:19
10        <provider
10-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml:5:9-13:20
11            android:name="net.nfet.flutter.printing.PrintFileProvider"
11-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml:6:13-46
12            android:authorities="${applicationId}.flutter.printing"
12-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml:7:13-68
13            android:exported="false"
13-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml:8:13-37
14            android:grantUriPermissions="true" >
14-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml:9:13-47
15            <meta-data
15-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml:10:13-12:70
16                android:name="android.support.FILE_PROVIDER_PATHS"
16-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml:11:17-67
17                android:resource="@xml/flutter_printing_file_paths" />
17-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\src\main\AndroidManifest.xml:12:17-68
18        </provider>
19    </application>
20
21</manifest>

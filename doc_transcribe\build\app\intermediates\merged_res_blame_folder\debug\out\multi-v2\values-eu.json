{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,253,325,402,476,587,685", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "119,182,248,320,397,471,582,680,748"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3351,3420,3483,3549,3621,3698,3772,3883,3981", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "3415,3478,3544,3616,3693,3767,3878,3976,4044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "657,755,858,958,1061,1166,1269,5203", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "750,853,953,1056,1161,1264,1383,5299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,657,741,827,902,1001,1092,1187,1255,1351,1447,1514,1586,1651,1722,1805,1883,1962,2031,2120,2192,2287,2389,2487,2553,2620,2673,2731,2780,2841,2903,2975,3039,3106,3171,3235,3302,3368,3435,3489,3556,3637,3718", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,82,77,78,68,88,71,94,101,97,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "281,470,652,736,822,897,996,1087,1182,1250,1346,1442,1509,1581,1646,1717,1800,1878,1957,2026,2115,2187,2282,2384,2482,2548,2615,2668,2726,2775,2836,2898,2970,3034,3101,3166,3230,3297,3363,3430,3484,3551,3632,3713,3769"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,1388,1472,1558,1633,1732,1823,1918,1986,2082,2178,2245,2317,2382,2453,2536,2614,2693,2762,2851,2923,3018,3120,3218,3284,4049,4102,4160,4209,4270,4332,4404,4468,4535,4600,4664,4731,4797,4864,4918,4985,5066,5147", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,82,77,78,68,88,71,94,101,97,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "281,470,652,1467,1553,1628,1727,1818,1913,1981,2077,2173,2240,2312,2377,2448,2531,2609,2688,2757,2846,2918,3013,3115,3213,3279,3346,4097,4155,4204,4265,4327,4399,4463,4530,4595,4659,4726,4792,4859,4913,4980,5061,5142,5198"}}]}]}
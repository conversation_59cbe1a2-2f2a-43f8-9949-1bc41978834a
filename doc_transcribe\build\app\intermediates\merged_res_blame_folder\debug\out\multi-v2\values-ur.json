{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "675,773,875,977,1081,1184,1282,5006", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "768,870,972,1076,1179,1277,1391,5102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,488,675,763,854,939,1034,1129,1197,1259,1348,1437,1507,1572,1634,1702,1782,1864,1943,2017,2098,2168,2236,2308,2379,2443,2506,2559,2617,2665,2726,2786,2855,2915,2978,3038,3101,3166,3229,3295,3348,3405,3476,3547", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,79,81,78,73,80,69,67,71,70,63,62,52,57,47,60,59,68,59,62,59,62,64,62,65,52,56,70,70,53", "endOffsets": "277,483,670,758,849,934,1029,1124,1192,1254,1343,1432,1502,1567,1629,1697,1777,1859,1938,2012,2093,2163,2231,2303,2374,2438,2501,2554,2612,2660,2721,2781,2850,2910,2973,3033,3096,3161,3224,3290,3343,3400,3471,3542,3596"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,488,1396,1484,1575,1660,1755,1850,1918,1980,2069,2158,2228,2293,2355,2423,2503,2585,2664,2738,2819,2889,2957,3029,3100,3164,3911,3964,4022,4070,4131,4191,4260,4320,4383,4443,4506,4571,4634,4700,4753,4810,4881,4952", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,79,81,78,73,80,69,67,71,70,63,62,52,57,47,60,59,68,59,62,59,62,64,62,65,52,56,70,70,53", "endOffsets": "277,483,670,1479,1570,1655,1750,1845,1913,1975,2064,2153,2223,2288,2350,2418,2498,2580,2659,2733,2814,2884,2952,3024,3095,3159,3222,3959,4017,4065,4126,4186,4255,4315,4378,4438,4501,4566,4629,4695,4748,4805,4876,4947,5001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,194,266,336,413,484,575,660", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "122,189,261,331,408,479,570,655,734"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3227,3299,3366,3438,3508,3585,3656,3747,3832", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "3294,3361,3433,3503,3580,3651,3742,3827,3906"}}]}]}
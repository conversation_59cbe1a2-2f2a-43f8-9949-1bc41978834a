{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "731,834,937,1039,1145,1243,1343,5262", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "829,932,1034,1140,1238,1338,1446,5358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3445,3516,3587,3657,3724,3802,3879,3979,4073", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "3511,3582,3652,3719,3797,3874,3974,4068,4137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,731,824,917,1007,1108,1211,1297,1361,1456,1551,1623,1696,1756,1826,1911,2002,2088,2167,2259,2327,2413,2505,2593,2662,2725,2778,2836,2884,2945,3007,3078,3140,3202,3261,3328,3394,3457,3524,3578,3640,3716,3792", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,84,90,85,78,91,67,85,91,87,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "305,524,726,819,912,1002,1103,1206,1292,1356,1451,1546,1618,1691,1751,1821,1906,1997,2083,2162,2254,2322,2408,2500,2588,2657,2720,2773,2831,2879,2940,3002,3073,3135,3197,3256,3323,3389,3452,3519,3573,3635,3711,3787,3840"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,1451,1544,1637,1727,1828,1931,2017,2081,2176,2271,2343,2416,2476,2546,2631,2722,2808,2887,2979,3047,3133,3225,3313,3382,4142,4195,4253,4301,4362,4424,4495,4557,4619,4678,4745,4811,4874,4941,4995,5057,5133,5209", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,84,90,85,78,91,67,85,91,87,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "305,524,726,1539,1632,1722,1823,1926,2012,2076,2171,2266,2338,2411,2471,2541,2626,2717,2803,2882,2974,3042,3128,3220,3308,3377,3440,4190,4248,4296,4357,4419,4490,4552,4614,4673,4740,4806,4869,4936,4990,5052,5128,5204,5257"}}]}]}
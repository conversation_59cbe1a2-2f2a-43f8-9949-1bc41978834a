'com.simform.audio_waveforms.AudioPlayer)com.simform.audio_waveforms.AudioRecorder0com.simform.audio_waveforms.AudioWaveformsPlugin,com.simform.audio_waveforms.RecorderSettings6com.simform.audio_waveforms.RecorderSettings.Companion(com.simform.audio_waveforms.DurationType%com.simform.audio_waveforms.Constants&com.simform.audio_waveforms.FinishMode=com.simform.audio_waveforms.RequestPermissionsSuccessCallback-com.simform.audio_waveforms.WaveformExtractor-com.simform.audio_waveforms.ExtractorCallBack                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
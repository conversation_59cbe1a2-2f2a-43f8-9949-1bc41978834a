{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,689,776,864,944,1031,1117,1188,1255,1353,1446,1516,1580,1642,1711,1789,1866,1942,2014,2096,2170,2236,2315,2394,2457,2522,2575,2633,2681,2742,2807,2879,2944,3012,3070,3128,3194,3258,3324,3376,3435,3508,3581", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,77,76,75,71,81,73,65,78,78,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "281,495,684,771,859,939,1026,1112,1183,1250,1348,1441,1511,1575,1637,1706,1784,1861,1937,2009,2091,2165,2231,2310,2389,2452,2517,2570,2628,2676,2737,2802,2874,2939,3007,3065,3123,3189,3253,3319,3371,3430,3503,3576,3631"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,1421,1508,1596,1676,1763,1849,1920,1987,2085,2178,2248,2312,2374,2443,2521,2598,2674,2746,2828,2902,2968,3047,3126,3189,3906,3959,4017,4065,4126,4191,4263,4328,4396,4454,4512,4578,4642,4708,4760,4819,4892,4965", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,77,76,75,71,81,73,65,78,78,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "281,495,684,1503,1591,1671,1758,1844,1915,1982,2080,2173,2243,2307,2369,2438,2516,2593,2669,2741,2823,2897,2963,3042,3121,3184,3249,3954,4012,4060,4121,4186,4258,4323,4391,4449,4507,4573,4637,4703,4755,4814,4887,4960,5015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3254,3323,3383,3449,3516,3591,3661,3750,3834", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "3318,3378,3444,3511,3586,3656,3745,3829,3901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "689,787,889,987,1085,1192,1301,5020", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "782,884,982,1080,1187,1296,1416,5116"}}]}]}
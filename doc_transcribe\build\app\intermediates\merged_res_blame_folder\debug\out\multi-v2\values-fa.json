{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,183,250,310,388,463,552,640", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "117,178,245,305,383,458,547,635,700"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3235,3302,3363,3430,3490,3568,3643,3732,3820", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "3297,3358,3425,3485,3563,3638,3727,3815,3880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "696,795,897,996,1096,1197,1303,5009", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "790,892,991,1091,1192,1298,1415,5105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,696,779,864,943,1036,1128,1205,1268,1360,1447,1510,1572,1633,1700,1774,1853,1929,1998,2074,2144,2216,2296,2374,2437,2511,2565,2634,2682,2743,2801,2878,2942,3006,3066,3128,3193,3259,3325,3377,3436,3509,3582", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,73,78,75,68,75,69,71,79,77,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "308,507,691,774,859,938,1031,1123,1200,1263,1355,1442,1505,1567,1628,1695,1769,1848,1924,1993,2069,2139,2211,2291,2369,2432,2506,2560,2629,2677,2738,2796,2873,2937,3001,3061,3123,3188,3254,3320,3372,3431,3504,3577,3630"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,1420,1503,1588,1667,1760,1852,1929,1992,2084,2171,2234,2296,2357,2424,2498,2577,2653,2722,2798,2868,2940,3020,3098,3161,3885,3939,4008,4056,4117,4175,4252,4316,4380,4440,4502,4567,4633,4699,4751,4810,4883,4956", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,73,78,75,68,75,69,71,79,77,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "308,507,691,1498,1583,1662,1755,1847,1924,1987,2079,2166,2229,2291,2352,2419,2493,2572,2648,2717,2793,2863,2935,3015,3093,3156,3230,3934,4003,4051,4112,4170,4247,4311,4375,4435,4497,4562,4628,4694,4746,4805,4878,4951,5004"}}]}]}
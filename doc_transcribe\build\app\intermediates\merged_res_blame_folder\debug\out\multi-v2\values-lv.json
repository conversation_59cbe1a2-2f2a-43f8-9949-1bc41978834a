{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,196,268,338,418,495,596,694", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "125,191,263,333,413,490,591,689,768"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3464,3539,3605,3677,3747,3827,3904,4005,4103", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "3534,3600,3672,3742,3822,3899,4000,4098,4177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,831,915,998,1081,1176,1271,1344,1411,1505,1599,1665,1732,1795,1871,1949,2032,2111,2185,2267,2341,2414,2508,2601,2667,2733,2786,2844,2892,2953,3011,3087,3151,3216,3281,3338,3404,3470,3536,3588,3652,3730,3808", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,77,82,78,73,81,73,72,93,92,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "281,562,826,910,993,1076,1171,1266,1339,1406,1500,1594,1660,1727,1790,1866,1944,2027,2106,2180,2262,2336,2409,2503,2596,2662,2728,2781,2839,2887,2948,3006,3082,3146,3211,3276,3333,3399,3465,3531,3583,3647,3725,3803,3858"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,1562,1646,1729,1812,1907,2002,2075,2142,2236,2330,2396,2463,2526,2602,2680,2763,2842,2916,2998,3072,3145,3239,3332,3398,4182,4235,4293,4341,4402,4460,4536,4600,4665,4730,4787,4853,4919,4985,5037,5101,5179,5257", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,77,82,78,73,81,73,72,93,92,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "281,562,826,1641,1724,1807,1902,1997,2070,2137,2231,2325,2391,2458,2521,2597,2675,2758,2837,2911,2993,3067,3140,3234,3327,3393,3459,4230,4288,4336,4397,4455,4531,4595,4660,4725,4782,4848,4914,4980,5032,5096,5174,5252,5307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "831,929,1031,1131,1232,1339,1447,5312", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "924,1026,1126,1227,1334,1442,1557,5408"}}]}]}
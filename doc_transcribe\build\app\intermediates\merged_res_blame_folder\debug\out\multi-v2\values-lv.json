{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,196,268,338,418,495,596,694", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "125,191,263,333,413,490,591,689,768"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6398,6473,6539,6611,6681,6761,6838,6939,7037", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "6468,6534,6606,6676,6756,6833,6934,7032,7111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,831,915,998,1081,1176,1271,1344,1411,1505,1599,1665,1732,1795,1871,1949,2032,2111,2185,2267,2341,2414,2508,2601,2667,2733,2786,2844,2892,2953,3011,3087,3151,3216,3281,3338,3404,3470,3536,3588,3652,3730,3808", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,77,82,78,73,81,73,72,93,92,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "281,562,826,910,993,1076,1171,1266,1339,1406,1500,1594,1660,1727,1790,1866,1944,2027,2106,2180,2262,2336,2409,2503,2596,2662,2728,2781,2839,2887,2948,3006,3082,3146,3211,3276,3333,3399,3465,3531,3583,3647,3725,3803,3858"}, "to": {"startLines": "2,11,16,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,617,4496,4580,4663,4746,4841,4936,5009,5076,5170,5264,5330,5397,5460,5536,5614,5697,5776,5850,5932,6006,6079,6173,6266,6332,7116,7169,7227,7275,7336,7394,7470,7534,7599,7664,7721,7787,7853,7919,7971,8035,8113,8191", "endLines": "10,15,20,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,77,82,78,73,81,73,72,93,92,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "331,612,876,4575,4658,4741,4836,4931,5004,5071,5165,5259,5325,5392,5455,5531,5609,5692,5771,5845,5927,6001,6074,6168,6261,6327,6393,7164,7222,7270,7331,7389,7465,7529,7594,7659,7716,7782,7848,7914,7966,8030,8108,8186,8241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "48,49,50,51,52,53,54,107", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3765,3863,3965,4065,4166,4273,4381,8329", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3858,3960,4060,4161,4268,4376,4491,8425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "881,1001,1111,1220,1306,1410,1532,1614,1694,1804,1912,2018,2127,2238,2341,2453,2560,2665,2765,2850,2959,3070,3169,3280,3387,3492,3666,8246", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "996,1106,1215,1301,1405,1527,1609,1689,1799,1907,2013,2122,2233,2336,2448,2555,2660,2760,2845,2954,3065,3164,3275,3382,3487,3661,3760,8324"}}]}]}
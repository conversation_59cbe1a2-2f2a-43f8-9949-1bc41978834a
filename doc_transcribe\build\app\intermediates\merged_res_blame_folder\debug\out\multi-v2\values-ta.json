{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,700,783,866,947,1052,1156,1235,1301,1397,1494,1565,1630,1692,1764,1856,1946,2038,2107,2191,2264,2344,2433,2520,2587,2655,2708,2771,2819,2880,2947,3012,3073,3142,3205,3268,3334,3397,3464,3518,3582,3660,3738", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,82,80,104,103,78,65,95,96,70,64,61,71,91,89,91,68,83,72,79,88,86,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,778,861,942,1047,1151,1230,1296,1392,1489,1560,1625,1687,1759,1851,1941,2033,2102,2186,2259,2339,2428,2515,2582,2650,2703,2766,2814,2875,2942,3007,3068,3137,3200,3263,3329,3392,3459,3513,3577,3655,3733,3789"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,1446,1529,1612,1693,1798,1902,1981,2047,2143,2240,2311,2376,2438,2510,2602,2692,2784,2853,2937,3010,3090,3179,3266,3333,4109,4162,4225,4273,4334,4401,4466,4527,4596,4659,4722,4788,4851,4918,4972,5036,5114,5192", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,82,80,104,103,78,65,95,96,70,64,61,71,91,89,91,68,83,72,79,88,86,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,1524,1607,1688,1793,1897,1976,2042,2138,2235,2306,2371,2433,2505,2597,2687,2779,2848,2932,3005,3085,3174,3261,3328,3396,4157,4220,4268,4329,4396,4461,4522,4591,4654,4717,4783,4846,4913,4967,5031,5109,5187,5243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,197,266,336,418,499,596,681", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "119,192,261,331,413,494,591,676,758"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3401,3470,3543,3612,3682,3764,3845,3942,4027", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "3465,3538,3607,3677,3759,3840,3937,4022,4104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "700,796,899,998,1096,1203,1318,5248", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "791,894,993,1091,1198,1313,1441,5344"}}]}]}
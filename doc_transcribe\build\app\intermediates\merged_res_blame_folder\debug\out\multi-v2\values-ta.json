{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "750,863,965,1080,1169,1280,1401,1480,1556,1654,1754,1849,1943,2050,2150,2252,2346,2444,2542,2623,2731,2834,2933,3049,3152,3257,3414,8064", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "858,960,1075,1164,1275,1396,1475,1551,1649,1749,1844,1938,2045,2145,2247,2341,2439,2537,2618,2726,2829,2928,3044,3147,3252,3409,3511,8141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,700,783,866,947,1052,1156,1235,1301,1397,1494,1565,1630,1692,1764,1856,1946,2038,2107,2191,2264,2344,2433,2520,2587,2655,2708,2771,2819,2880,2947,3012,3073,3142,3205,3268,3334,3397,3464,3518,3582,3660,3738", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,82,80,104,103,78,65,95,96,70,64,61,71,91,89,91,68,83,72,79,88,86,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,778,861,942,1047,1151,1230,1296,1392,1489,1560,1625,1687,1759,1851,1941,2033,2102,2186,2259,2339,2428,2515,2582,2650,2703,2766,2814,2875,2942,3007,3068,3137,3200,3263,3329,3392,3459,3513,3577,3655,3733,3789"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,555,4262,4345,4428,4509,4614,4718,4797,4863,4959,5056,5127,5192,5254,5326,5418,5508,5600,5669,5753,5826,5906,5995,6082,6149,6925,6978,7041,7089,7150,7217,7282,7343,7412,7475,7538,7604,7667,7734,7788,7852,7930,8008", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,82,82,80,104,103,78,65,95,96,70,64,61,71,91,89,91,68,83,72,79,88,86,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "330,550,745,4340,4423,4504,4609,4713,4792,4858,4954,5051,5122,5187,5249,5321,5413,5503,5595,5664,5748,5821,5901,5990,6077,6144,6212,6973,7036,7084,7145,7212,7277,7338,7407,7470,7533,7599,7662,7729,7783,7847,7925,8003,8059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,197,266,336,418,499,596,681", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "119,192,261,331,413,494,591,676,758"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6217,6286,6359,6428,6498,6580,6661,6758,6843", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "6281,6354,6423,6493,6575,6656,6753,6838,6920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3516,3612,3715,3814,3912,4019,4134,8146", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3607,3710,3809,3907,4014,4129,4257,8242"}}]}]}
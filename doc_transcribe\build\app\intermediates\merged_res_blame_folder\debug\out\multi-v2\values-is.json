{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "689,784,891,988,1088,1191,1295,5002", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "779,886,983,1083,1186,1290,1401,5098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,689,774,858,935,1024,1121,1190,1254,1345,1436,1499,1563,1625,1693,1772,1853,1932,2007,2088,2161,2230,2312,2393,2458,2538,2591,2652,2702,2763,2822,2892,2955,3017,3081,3141,3207,3272,3342,3394,3454,3528,3602", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,78,80,78,74,80,72,68,81,80,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "283,483,684,769,853,930,1019,1116,1185,1249,1340,1431,1494,1558,1620,1688,1767,1848,1927,2002,2083,2156,2225,2307,2388,2453,2533,2586,2647,2697,2758,2817,2887,2950,3012,3076,3136,3202,3267,3337,3389,3449,3523,3597,3650"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,1406,1491,1575,1652,1741,1838,1907,1971,2062,2153,2216,2280,2342,2410,2489,2570,2649,2724,2805,2878,2947,3029,3110,3175,3885,3938,3999,4049,4110,4169,4239,4302,4364,4428,4488,4554,4619,4689,4741,4801,4875,4949", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,78,80,78,74,80,72,68,81,80,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "283,483,684,1486,1570,1647,1736,1833,1902,1966,2057,2148,2211,2275,2337,2405,2484,2565,2644,2719,2800,2873,2942,3024,3105,3170,3250,3933,3994,4044,4105,4164,4234,4297,4359,4423,4483,4549,4614,4684,4736,4796,4870,4944,4997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3255,3322,3381,3440,3506,3582,3645,3734,3816", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "3317,3376,3435,3501,3577,3640,3729,3811,3880"}}]}]}
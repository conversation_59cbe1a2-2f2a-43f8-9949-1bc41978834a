{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3863,3928,3987,4054,4116,4198,4279,4380,4475", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "3923,3982,4049,4111,4193,4274,4375,4470,4554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "27,28,29,30,31,32,33,85", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1314,1407,1509,1604,1707,1810,1912,5683", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "1402,1504,1599,1702,1805,1907,2021,5779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2395,2471,2556,2622,2705,2777,2849,2937,3024,3088,3151,3204,3275,3330,3391,3449,3523,3587,3651,3711,3776,3840,3902,3968,4020,4077,4148,4219", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,74,75,84,65,82,71,71,87,86,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2390,2466,2551,2617,2700,2772,2844,2932,3019,3083,3146,3199,3270,3325,3386,3444,3518,3582,3646,3706,3771,3835,3897,3963,4015,4072,4143,4214,4270"}, "to": {"startLines": "2,11,19,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,2026,2105,2183,2259,2353,2445,2519,2584,2676,2766,2836,2900,2963,3032,3107,3183,3268,3334,3417,3489,3561,3649,3736,3800,4559,4612,4683,4738,4799,4857,4931,4995,5059,5119,5184,5248,5310,5376,5428,5485,5556,5627", "endLines": "10,18,26,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,74,75,84,65,82,71,71,87,86,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,2100,2178,2254,2348,2440,2514,2579,2671,2761,2831,2895,2958,3027,3102,3178,3263,3329,3412,3484,3556,3644,3731,3795,3858,4607,4678,4733,4794,4852,4926,4990,5054,5114,5179,5243,5305,5371,5423,5480,5551,5622,5678"}}]}]}
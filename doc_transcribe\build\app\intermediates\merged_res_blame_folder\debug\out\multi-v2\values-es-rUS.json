{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,662,748,835,908,1004,1100,1180,1248,1347,1446,1512,1581,1647,1718,1795,1870,1946,2017,2101,2177,2257,2352,2443,2509,2573,2626,2684,2732,2793,2858,2920,2986,3058,3122,3183,3249,3314,3380,3433,3498,3577,3656", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,76,74,75,70,83,75,79,94,90,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,470,657,743,830,903,999,1095,1175,1243,1342,1441,1507,1576,1642,1713,1790,1865,1941,2012,2096,2172,2252,2347,2438,2504,2568,2621,2679,2727,2788,2853,2915,2981,3053,3117,3178,3244,3309,3375,3428,3493,3572,3651,3709"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,1394,1480,1567,1640,1736,1832,1912,1980,2079,2178,2244,2313,2379,2450,2527,2602,2678,2749,2833,2909,2989,3084,3175,3241,3974,4027,4085,4133,4194,4259,4321,4387,4459,4523,4584,4650,4715,4781,4834,4899,4978,5057", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,76,74,75,70,83,75,79,94,90,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,470,657,1475,1562,1635,1731,1827,1907,1975,2074,2173,2239,2308,2374,2445,2522,2597,2673,2744,2828,2904,2984,3079,3170,3236,3300,4022,4080,4128,4189,4254,4316,4382,4454,4518,4579,4645,4710,4776,4829,4894,4973,5052,5110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3305,3380,3443,3508,3577,3654,3728,3817,3905", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "3375,3438,3503,3572,3649,3723,3812,3900,3969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "662,761,863,963,1061,1168,1274,5115", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "756,858,958,1056,1163,1269,1389,5211"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "949,1047,1149,1250,1351,1456,1559,5440", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "1042,1144,1245,1346,1451,1554,1671,5536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,949,1032,1115,1198,1288,1388,1459,1532,1631,1732,1805,1877,1942,2020,2098,2175,2254,2331,2426,2498,2571,2660,2747,2816,2881,2934,2996,3044,3105,3172,3240,3306,3388,3446,3503,3569,3634,3700,3752,3813,3898,3983", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,77,76,78,76,94,71,72,88,86,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "275,614,944,1027,1110,1193,1283,1383,1454,1527,1626,1727,1800,1872,1937,2015,2093,2170,2249,2326,2421,2493,2566,2655,2742,2811,2876,2929,2991,3039,3100,3167,3235,3301,3383,3441,3498,3564,3629,3695,3747,3808,3893,3978,4041"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,1676,1759,1842,1925,2015,2115,2186,2259,2358,2459,2532,2604,2669,2747,2825,2902,2981,3058,3153,3225,3298,3387,3474,3543,4275,4328,4390,4438,4499,4566,4634,4700,4782,4840,4897,4963,5028,5094,5146,5207,5292,5377", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,77,76,78,76,94,71,72,88,86,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "275,614,944,1754,1837,1920,2010,2110,2181,2254,2353,2454,2527,2599,2664,2742,2820,2897,2976,3053,3148,3220,3293,3382,3469,3538,3603,4323,4385,4433,4494,4561,4629,4695,4777,4835,4892,4958,5023,5089,5141,5202,5287,5372,5435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3608,3680,3741,3806,3872,3950,4024,4112,4198", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "3675,3736,3801,3867,3945,4019,4107,4193,4270"}}]}]}
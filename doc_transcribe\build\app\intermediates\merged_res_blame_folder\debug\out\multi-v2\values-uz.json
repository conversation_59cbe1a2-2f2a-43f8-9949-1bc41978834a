{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,186,256,320,399,467,569,663", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "115,181,251,315,394,462,564,658,738"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3311,3376,3442,3512,3576,3655,3723,3825,3919", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "3371,3437,3507,3571,3650,3718,3820,3914,3994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "644,746,848,949,1049,1157,1261,5121", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "741,843,944,1044,1152,1256,1375,5217"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,644,730,825,908,1006,1105,1180,1248,1349,1450,1515,1578,1641,1713,1798,1877,1962,2039,2113,2186,2261,2352,2440,2509,2575,2628,2688,2736,2797,2858,2929,2989,3057,3120,3185,3251,3315,3381,3433,3494,3569,3644", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,84,78,84,76,73,72,74,90,87,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "280,463,639,725,820,903,1001,1100,1175,1243,1344,1445,1510,1573,1636,1708,1793,1872,1957,2034,2108,2181,2256,2347,2435,2504,2570,2623,2683,2731,2792,2853,2924,2984,3052,3115,3180,3246,3310,3376,3428,3489,3564,3639,3692"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,1380,1466,1561,1644,1742,1841,1916,1984,2085,2186,2251,2314,2377,2449,2534,2613,2698,2775,2849,2922,2997,3088,3176,3245,3999,4052,4112,4160,4221,4282,4353,4413,4481,4544,4609,4675,4739,4805,4857,4918,4993,5068", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,84,78,84,76,73,72,74,90,87,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "280,463,639,1461,1556,1639,1737,1836,1911,1979,2080,2181,2246,2309,2372,2444,2529,2608,2693,2770,2844,2917,2992,3083,3171,3240,3306,4047,4107,4155,4216,4277,4348,4408,4476,4539,4604,4670,4734,4800,4852,4913,4988,5063,5116"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,175,237,297,369,432,521,602", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "112,170,232,292,364,427,516,597,663"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3080,3142,3200,3262,3322,3394,3457,3546,3627", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "3137,3195,3257,3317,3389,3452,3541,3622,3688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,664,744,825,901,986,1067,1133,1195,1279,1362,1429,1492,1553,1619,1692,1766,1838,1907,1983,2051,2117,2192,2266,2328,2393,2446,2503,2549,2610,2668,2743,2802,2864,2923,2980,3044,3107,3171,3221,3277,3347,3417", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,72,73,71,68,75,67,65,74,73,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "278,477,659,739,820,896,981,1062,1128,1190,1274,1357,1424,1487,1548,1614,1687,1761,1833,1902,1978,2046,2112,2187,2261,2323,2388,2441,2498,2544,2605,2663,2738,2797,2859,2918,2975,3039,3102,3166,3216,3272,3342,3412,3464"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,1351,1431,1512,1588,1673,1754,1820,1882,1966,2049,2116,2179,2240,2306,2379,2453,2525,2594,2670,2738,2804,2879,2953,3015,3693,3746,3803,3849,3910,3968,4043,4102,4164,4223,4280,4344,4407,4471,4521,4577,4647,4717", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,72,73,71,68,75,67,65,74,73,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "278,477,659,1426,1507,1583,1668,1749,1815,1877,1961,2044,2111,2174,2235,2301,2374,2448,2520,2589,2665,2733,2799,2874,2948,3010,3075,3741,3798,3844,3905,3963,4038,4097,4159,4218,4275,4339,4402,4466,4516,4572,4642,4712,4764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "664,757,857,954,1053,1149,1251,4769", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "752,852,949,1048,1144,1246,1346,4865"}}]}]}
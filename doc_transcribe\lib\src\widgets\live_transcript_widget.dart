import 'package:flutter/material.dart';
import '../models/transcript.dart';

class LiveTranscriptWidget extends StatefulWidget {
  final List<TranscriptSegment> segments;
  final bool isStreaming;

  const LiveTranscriptWidget({
    super.key,
    required this.segments,
    required this.isStreaming,
  });

  @override
  State<LiveTranscriptWidget> createState() => _LiveTranscriptWidgetState();
}

class _LiveTranscriptWidgetState extends State<LiveTranscriptWidget>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late AnimationController _blinkController;

  @override
  void initState() {
    super.initState();
    _blinkController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    if (widget.isStreaming) {
      _blinkController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(LiveTranscriptWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Auto-scroll to bottom when new segments are added
    if (widget.segments.length > oldWidget.segments.length) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }

    // Update blinking animation
    if (widget.isStreaming != oldWidget.isStreaming) {
      if (widget.isStreaming) {
        _blinkController.repeat(reverse: true);
      } else {
        _blinkController.stop();
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _blinkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Status indicator
          _buildStatusIndicator(),
          
          const SizedBox(height: 12),

          // Transcript content
          Expanded(
            child: _buildTranscriptContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator() {
    return Row(
      children: [
        AnimatedBuilder(
          animation: _blinkController,
          builder: (context, child) {
            return Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: widget.isStreaming
                    ? Colors.red.withOpacity(_blinkController.value)
                    : Colors.grey,
                shape: BoxShape.circle,
              ),
            );
          },
        ),
        const SizedBox(width: 8),
        Text(
          widget.isStreaming ? 'Live transcription active' : 'Transcription stopped',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: widget.isStreaming ? Colors.red : Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        Text(
          '${widget.segments.length} segments',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildTranscriptContent() {
    if (widget.segments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.mic_off,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              widget.isStreaming
                  ? 'Listening for speech...'
                  : 'No transcript available',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: widget.segments.length + (widget.isStreaming ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == widget.segments.length) {
          // Show typing indicator at the end
          return _buildTypingIndicator();
        }

        final segment = widget.segments[index];
        return _buildTranscriptSegment(segment);
      },
    );
  }

  Widget _buildTranscriptSegment(TranscriptSegment segment) {
    final isDoctor = segment.speaker == SpeakerType.doctor;
    final speakerColor = isDoctor ? Colors.blue : Colors.green;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Speaker indicator
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: speakerColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: speakerColor.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                isDoctor ? 'D' : 'P',
                style: TextStyle(
                  color: speakerColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 12),

          // Transcript content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Speaker label and confidence
                Row(
                  children: [
                    Text(
                      segment.speakerLabel,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: speakerColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (!segment.isFinal)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'partial',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.orange[700],
                            fontSize: 10,
                          ),
                        ),
                      ),
                    const Spacer(),
                    Text(
                      '${(segment.confidence * 100).toInt()}%',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[500],
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 4),

                // Transcript text
                Text(
                  segment.text,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: segment.isFinal ? Colors.black87 : Colors.grey[600],
                    fontStyle: segment.isFinal ? FontStyle.normal : FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(
              child: SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          AnimatedBuilder(
            animation: _blinkController,
            builder: (context, child) {
              return Text(
                'Listening...',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.withOpacity(0.5 + _blinkController.value * 0.5),
                  fontStyle: FontStyle.italic,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3569,3667,3770,3870,3973,4081,4187,8166", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3662,3765,3865,3968,4076,4182,4299,8262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "788,906,1017,1134,1219,1325,1448,1537,1622,1713,1806,1901,1995,2095,2188,2283,2380,2471,2562,2647,2758,2867,2969,3080,3190,3298,3469,8080", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "901,1012,1129,1214,1320,1443,1532,1617,1708,1801,1896,1990,2090,2183,2278,2375,2466,2557,2642,2753,2862,2964,3075,3185,3293,3464,3564,8161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,510,738,826,915,997,1080,1172,1269,1335,1431,1527,1592,1662,1727,1801,1880,1960,2049,2119,2202,2274,2371,2465,2556,2622,2697,2750,2808,2862,2923,2988,3057,3122,3194,3256,3316,3381,3448,3515,3573,3639,3719,3799", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,78,79,88,69,82,71,96,93,90,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "282,505,733,821,910,992,1075,1167,1264,1330,1426,1522,1587,1657,1722,1796,1875,1955,2044,2114,2197,2269,2366,2460,2551,2617,2692,2745,2803,2857,2918,2983,3052,3117,3189,3251,3311,3376,3443,3510,3568,3634,3714,3794,3848"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,560,4304,4392,4481,4563,4646,4738,4835,4901,4997,5093,5158,5228,5293,5367,5446,5526,5615,5685,5768,5840,5937,6031,6122,6188,6924,6977,7035,7089,7150,7215,7284,7349,7421,7483,7543,7608,7675,7742,7800,7866,7946,8026", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,78,79,88,69,82,71,96,93,90,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "332,555,783,4387,4476,4558,4641,4733,4830,4896,4992,5088,5153,5223,5288,5362,5441,5521,5610,5680,5763,5835,5932,6026,6117,6183,6258,6972,7030,7084,7145,7210,7279,7344,7416,7478,7538,7603,7670,7737,7795,7861,7941,8021,8075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,184,242,305,379,455,554,649", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "121,179,237,300,374,450,549,644,711"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6263,6334,6392,6450,6513,6587,6663,6762,6857", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "6329,6387,6445,6508,6582,6658,6757,6852,6919"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3543,3619,3681,3744,3813,3890,3960,4042,4122", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "3614,3676,3739,3808,3885,3955,4037,4117,4197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2036,2111,2190,2264,2344,2416,2497,2589,2680,2747,2812,2865,2923,2971,3032,3098,3165,3228,3295,3360,3419,3484,3548,3614,3666,3729,3806,3883", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,76,74,78,73,79,71,80,91,90,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2031,2106,2185,2259,2339,2411,2492,2584,2675,2742,2807,2860,2918,2966,3027,3093,3160,3223,3290,3355,3414,3479,3543,3609,3661,3724,3801,3878,3932"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,1655,1736,1816,1894,1996,2094,2172,2236,2325,2417,2487,2553,2618,2690,2767,2842,2921,2995,3075,3147,3228,3320,3411,3478,4202,4255,4313,4361,4422,4488,4555,4618,4685,4750,4809,4874,4938,5004,5056,5119,5196,5273", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,76,74,78,73,79,71,80,91,90,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1731,1811,1889,1991,2089,2167,2231,2320,2412,2482,2548,2613,2685,2762,2837,2916,2990,3070,3142,3223,3315,3406,3473,3538,4250,4308,4356,4417,4483,4550,4613,4680,4745,4804,4869,4933,4999,5051,5114,5191,5268,5322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "924,1022,1124,1225,1324,1429,1536,5327", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "1017,1119,1220,1319,1424,1531,1650,5423"}}]}]}
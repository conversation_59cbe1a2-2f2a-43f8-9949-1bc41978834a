{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,249,314,392,459,548,641", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "120,181,244,309,387,454,543,636,707"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3261,3331,3392,3455,3520,3598,3665,3754,3847", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "3326,3387,3450,3515,3593,3660,3749,3842,3913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,476,660,748,837,914,1006,1094,1170,1234,1325,1416,1481,1546,1608,1676,1760,1848,1930,2001,2082,2152,2228,2315,2399,2468,2534,2587,2645,2693,2754,2818,2890,2949,3012,3075,3135,3201,3265,3331,3383,3441,3513,3585", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,83,87,81,70,80,69,75,86,83,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "280,471,655,743,832,909,1001,1089,1165,1229,1320,1411,1476,1541,1603,1671,1755,1843,1925,1996,2077,2147,2223,2310,2394,2463,2529,2582,2640,2688,2749,2813,2885,2944,3007,3070,3130,3196,3260,3326,3378,3436,3508,3580,3634"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,476,1387,1475,1564,1641,1733,1821,1897,1961,2052,2143,2208,2273,2335,2403,2487,2575,2657,2728,2809,2879,2955,3042,3126,3195,3918,3971,4029,4077,4138,4202,4274,4333,4396,4459,4519,4585,4649,4715,4767,4825,4897,4969", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,83,87,81,70,80,69,75,86,83,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "280,471,655,1470,1559,1636,1728,1816,1892,1956,2047,2138,2203,2268,2330,2398,2482,2570,2652,2723,2804,2874,2950,3037,3121,3190,3256,3966,4024,4072,4133,4197,4269,4328,4391,4454,4514,4580,4644,4710,4762,4820,4892,4964,5018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "660,761,863,966,1070,1171,1276,5023", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "756,858,961,1065,1166,1271,1382,5119"}}]}]}
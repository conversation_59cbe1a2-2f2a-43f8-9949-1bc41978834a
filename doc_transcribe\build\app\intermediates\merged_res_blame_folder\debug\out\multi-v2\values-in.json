{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "646,741,843,940,1037,1143,1261,5001", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "736,838,935,1032,1138,1256,1371,5097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,185,250,313,389,453,553,647", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "118,180,245,308,384,448,548,642,711"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3216,3284,3346,3411,3474,3550,3614,3714,3808", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "3279,3341,3406,3469,3545,3609,3709,3803,3872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,646,729,814,890,978,1071,1148,1217,1313,1407,1471,1535,1601,1674,1751,1829,1902,1974,2054,2124,2198,2280,2355,2422,2486,2539,2597,2645,2706,2770,2832,2895,2961,3023,3086,3152,3216,3282,3334,3396,3472,3548", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,76,77,72,71,79,69,73,81,74,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "280,467,641,724,809,885,973,1066,1143,1212,1308,1402,1466,1530,1596,1669,1746,1824,1897,1969,2049,2119,2193,2275,2350,2417,2481,2534,2592,2640,2701,2765,2827,2890,2956,3018,3081,3147,3211,3277,3329,3391,3467,3543,3605"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,1376,1459,1544,1620,1708,1801,1878,1947,2043,2137,2201,2265,2331,2404,2481,2559,2632,2704,2784,2854,2928,3010,3085,3152,3877,3930,3988,4036,4097,4161,4223,4286,4352,4414,4477,4543,4607,4673,4725,4787,4863,4939", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,76,77,72,71,79,69,73,81,74,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "280,467,641,1454,1539,1615,1703,1796,1873,1942,2038,2132,2196,2260,2326,2399,2476,2554,2627,2699,2779,2849,2923,3005,3080,3147,3211,3925,3983,4031,4092,4156,4218,4281,4347,4409,4472,4538,4602,4668,4720,4782,4858,4934,4996"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3400,3495,3597,3694,3791,3897,4015,7840", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3490,3592,3689,3786,3892,4010,4125,7936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "696,811,915,1020,1107,1211,1327,1410,1488,1579,1672,1767,1861,1961,2054,2149,2243,2334,2425,2511,2614,2719,2820,2924,3033,3141,3301,7755", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "806,910,1015,1102,1206,1322,1405,1483,1574,1667,1762,1856,1956,2049,2144,2238,2329,2420,2506,2609,2714,2815,2919,3028,3136,3296,3395,7835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,185,250,313,389,453,553,647", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "118,180,245,308,384,448,548,642,711"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5970,6038,6100,6165,6228,6304,6368,6468,6562", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "6033,6095,6160,6223,6299,6363,6463,6557,6626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,646,729,814,890,978,1071,1148,1217,1313,1407,1471,1535,1601,1674,1751,1829,1902,1974,2054,2124,2198,2280,2355,2422,2486,2539,2597,2645,2706,2770,2832,2895,2961,3023,3086,3152,3216,3282,3334,3396,3472,3548", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,76,77,72,71,79,69,73,81,74,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "280,467,641,724,809,885,973,1066,1143,1212,1308,1402,1466,1530,1596,1669,1746,1824,1897,1969,2049,2119,2193,2275,2350,2417,2481,2534,2592,2640,2701,2765,2827,2890,2956,3018,3081,3147,3211,3277,3329,3391,3467,3543,3605"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,522,4130,4213,4298,4374,4462,4555,4632,4701,4797,4891,4955,5019,5085,5158,5235,5313,5386,5458,5538,5608,5682,5764,5839,5906,6631,6684,6742,6790,6851,6915,6977,7040,7106,7168,7231,7297,7361,7427,7479,7541,7617,7693", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,76,77,72,71,79,69,73,81,74,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "330,517,691,4208,4293,4369,4457,4550,4627,4696,4792,4886,4950,5014,5080,5153,5230,5308,5381,5453,5533,5603,5677,5759,5834,5901,5965,6679,6737,6785,6846,6910,6972,7035,7101,7163,7226,7292,7356,7422,7474,7536,7612,7688,7750"}}]}]}
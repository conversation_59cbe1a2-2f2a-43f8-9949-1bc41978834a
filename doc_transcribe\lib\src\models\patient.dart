class Patient {
  final String name;
  final int age;
  final String gender;
  final String? medicalRecordNumber;
  final String? reasonForVisit;
  final DateTime createdAt;

  Patient({
    required this.name,
    required this.age,
    required this.gender,
    this.medicalRecordNumber,
    this.reasonForVisit,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'age': age,
      'gender': gender,
      'medicalRecordNumber': medicalRecordNumber,
      'reasonForVisit': reasonForVisit,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory Patient.fromJson(Map<String, dynamic> json) {
    return Patient(
      name: json['name'] as String,
      age: json['age'] as int,
      gender: json['gender'] as String,
      medicalRecordNumber: json['medicalRecordNumber'] as String?,
      reasonForVisit: json['reasonForVisit'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  Patient copyWith({
    String? name,
    int? age,
    String? gender,
    String? medicalRecordNumber,
    String? reasonForVisit,
    DateTime? createdAt,
  }) {
    return Patient(
      name: name ?? this.name,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      medicalRecordNumber: medicalRecordNumber ?? this.medicalRecordNumber,
      reasonForVisit: reasonForVisit ?? this.reasonForVisit,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Patient(name: $name, age: $age, gender: $gender, mrn: $medicalRecordNumber)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Patient &&
        other.name == name &&
        other.age == age &&
        other.gender == gender &&
        other.medicalRecordNumber == medicalRecordNumber &&
        other.reasonForVisit == reasonForVisit;
  }

  @override
  int get hashCode {
    return Object.hash(
      name,
      age,
      gender,
      medicalRecordNumber,
      reasonForVisit,
    );
  }
}

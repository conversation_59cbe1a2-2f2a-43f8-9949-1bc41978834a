{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,686,775,866,945,1043,1140,1219,1285,1382,1479,1544,1607,1671,1743,1827,1914,2005,2080,2168,2241,2321,2415,2505,2571,2635,2688,2746,2794,2855,2922,2999,3066,3138,3196,3255,3321,3386,3452,3504,3569,3648,3727", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,83,86,90,74,87,72,79,93,89,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "280,494,681,770,861,940,1038,1135,1214,1280,1377,1474,1539,1602,1666,1738,1822,1909,2000,2075,2163,2236,2316,2410,2500,2566,2630,2683,2741,2789,2850,2917,2994,3061,3133,3191,3250,3316,3381,3447,3499,3564,3643,3722,3776"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,1409,1498,1589,1668,1766,1863,1942,2008,2105,2202,2267,2330,2394,2466,2550,2637,2728,2803,2891,2964,3044,3138,3228,3294,4088,4141,4199,4247,4308,4375,4452,4519,4591,4649,4708,4774,4839,4905,4957,5022,5101,5180", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,83,86,90,74,87,72,79,93,89,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "280,494,681,1493,1584,1663,1761,1858,1937,2003,2100,2197,2262,2325,2389,2461,2545,2632,2723,2798,2886,2959,3039,3133,3223,3289,3353,4136,4194,4242,4303,4370,4447,4514,4586,4644,4703,4769,4834,4900,4952,5017,5096,5175,5229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "686,784,886,985,1087,1191,1295,5234", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "779,881,980,1082,1186,1290,1404,5330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,272,344,427,503,600,693", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "124,189,267,339,422,498,595,688,780"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3358,3432,3497,3575,3647,3730,3806,3903,3996", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "3427,3492,3570,3642,3725,3801,3898,3991,4083"}}]}]}
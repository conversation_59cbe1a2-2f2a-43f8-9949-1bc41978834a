{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,691,776,860,941,1034,1130,1206,1272,1361,1450,1517,1581,1643,1716,1804,1895,1981,2052,2135,2204,2280,2370,2459,2523,2588,2641,2703,2751,2812,2872,2934,2998,3064,3121,3185,3250,3316,3382,3435,3498,3575,3652", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,87,90,85,70,82,68,75,89,88,63,64,52,61,47,60,59,61,63,65,56,63,64,65,65,52,62,76,76,51", "endOffsets": "283,492,686,771,855,936,1029,1125,1201,1267,1356,1445,1512,1576,1638,1711,1799,1890,1976,2047,2130,2199,2275,2365,2454,2518,2583,2636,2698,2746,2807,2867,2929,2993,3059,3116,3180,3245,3311,3377,3430,3493,3570,3647,3699"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,1420,1505,1589,1670,1763,1859,1935,2001,2090,2179,2246,2310,2372,2445,2533,2624,2710,2781,2864,2933,3009,3099,3188,3252,4076,4129,4191,4239,4300,4360,4422,4486,4552,4609,4673,4738,4804,4870,4923,4986,5063,5140", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,87,90,85,70,82,68,75,89,88,63,64,52,61,47,60,59,61,63,65,56,63,64,65,65,52,62,76,76,51", "endOffsets": "283,492,686,1500,1584,1665,1758,1854,1930,1996,2085,2174,2241,2305,2367,2440,2528,2619,2705,2776,2859,2928,3004,3094,3183,3247,3312,4124,4186,4234,4295,4355,4417,4481,4547,4604,4668,4733,4799,4865,4918,4981,5058,5135,5187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "691,790,892,994,1097,1198,1300,5192", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "785,887,989,1092,1193,1295,1415,5288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3317,3385,3451,3518,3584,3659,3726,3858,3987", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "3380,3446,3513,3579,3654,3721,3853,3982,4071"}}]}]}
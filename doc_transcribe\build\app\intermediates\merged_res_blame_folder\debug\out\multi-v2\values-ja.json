{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "604,696,796,890,986,1079,1172,4583", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "691,791,885,981,1074,1167,1268,4679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,604,676,747,816,895,973,1039,1100,1178,1255,1319,1380,1439,1504,1576,1647,1720,1785,1851,1916,1980,2055,2128,2189,2252,2304,2362,2410,2471,2527,2589,2646,2706,2762,2818,2881,2943,3006,3056,3114,3186,3258", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,71,70,72,64,65,64,63,74,72,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "276,439,599,671,742,811,890,968,1034,1095,1173,1250,1314,1375,1434,1499,1571,1642,1715,1780,1846,1911,1975,2050,2123,2184,2247,2299,2357,2405,2466,2522,2584,2641,2701,2757,2813,2876,2938,3001,3051,3109,3181,3253,3302"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,1273,1345,1416,1485,1564,1642,1708,1769,1847,1924,1988,2049,2108,2173,2245,2316,2389,2454,2520,2585,2649,2724,2797,2858,3528,3580,3638,3686,3747,3803,3865,3922,3982,4038,4094,4157,4219,4282,4332,4390,4462,4534", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,71,70,72,64,65,64,63,74,72,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "276,439,599,1340,1411,1480,1559,1637,1703,1764,1842,1919,1983,2044,2103,2168,2240,2311,2384,2449,2515,2580,2644,2719,2792,2853,2916,3575,3633,3681,3742,3798,3860,3917,3977,4033,4089,4152,4214,4277,4327,4385,4457,4529,4578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,180,245,307,381,440,520,597", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "115,175,240,302,376,435,515,592,657"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2921,2986,3046,3111,3173,3247,3306,3386,3463", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "2981,3041,3106,3168,3242,3301,3381,3458,3523"}}]}]}
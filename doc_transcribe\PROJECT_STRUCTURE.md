# DocTranscribe Project Structure

This document outlines the complete structure of the DocTranscribe Flutter application.

## Project Overview

DocTranscribe is a comprehensive Flutter mobile application that enables doctors to record patient consultations, transcribe them using NVIDIA Riva ASR technology, and generate professional PDF reports.

## Directory Structure

```
doc_transcribe/
├── README.md                           # Project documentation
├── PROJECT_STRUCTURE.md               # This file
├── pubspec.yaml                        # Flutter dependencies and configuration
├── 
├── lib/                               # Main application source code
│   ├── main.dart                      # Application entry point
│   └── src/
│       ├── config/
│       │   └── riva_config.dart       # NVIDIA Riva API configuration
│       ├── models/                    # Data models
│       │   ├── patient.dart           # Patient information model
│       │   ├── transcript.dart        # Transcript and segment models
│       │   └── consultation.dart      # Consultation session model
│       ├── providers/                 # State management (Provider pattern)
│       │   ├── app_state_provider.dart      # Global app state
│       │   ├── patient_provider.dart        # Patient form state
│       │   ├── recording_provider.dart      # Audio recording state
│       │   └── transcript_provider.dart     # Transcription state
│       ├── services/                  # Business logic and external integrations
│       │   ├── storage_service.dart         # Local data persistence
│       │   ├── audio_service.dart           # Audio recording and playback
│       │   ├── riva_service.dart            # NVIDIA Riva ASR integration
│       │   ├── speaker_filter_service.dart  # Speaker diarization and filtering
│       │   └── pdf_service.dart             # PDF report generation
│       ├── screens/                   # UI screens
│       │   ├── home_screen.dart             # Main dashboard
│       │   ├── patient_info_screen.dart     # Patient information entry
│       │   ├── recording_screen.dart        # Audio recording interface
│       │   ├── review_transcript_screen.dart # Transcript review and editing
│       │   ├── history_screen.dart          # Consultation history
│       │   └── settings_screen.dart         # App configuration
│       ├── widgets/                   # Reusable UI components
│       │   ├── app_scaffold.dart            # Common app layout
│       │   ├── recording_controls.dart      # Recording control buttons
│       │   ├── audio_visualizer.dart        # Audio level visualization
│       │   └── live_transcript_widget.dart  # Real-time transcript display
│       └── utils/                     # Utility functions
│           └── uuid_generator.dart          # UUID generation utility
│
├── android/                          # Android-specific configuration
│   └── app/
│       ├── build.gradle              # Android build configuration
│       └── src/main/
│           ├── AndroidManifest.xml   # Android permissions and configuration
│           ├── kotlin/com/example/doc_transcribe/
│           │   └── MainActivity.kt   # Android main activity
│           └── res/xml/
│               └── file_paths.xml    # File provider configuration
│
├── ios/                              # iOS-specific configuration
│   └── Runner/
│       └── Info.plist               # iOS permissions and configuration
│
├── assets/                           # Static assets
│   └── images/                       # Application images and icons
│
└── test/                            # Unit and widget tests
    └── widget_test.dart             # Basic widget tests
```

## Key Components

### 1. Models (`lib/src/models/`)
- **Patient**: Stores patient demographic information and visit details
- **Transcript**: Manages conversation segments with timestamps and speaker labels
- **Consultation**: Represents a complete consultation session

### 2. Providers (`lib/src/providers/`)
- **AppStateProvider**: Global application state and navigation
- **PatientProvider**: Patient information form management
- **RecordingProvider**: Audio recording state and controls
- **TranscriptProvider**: Live transcription and processing state

### 3. Services (`lib/src/services/`)
- **StorageService**: SQLite database and secure storage management
- **AudioService**: Microphone recording and audio file handling
- **RivaService**: NVIDIA Riva ASR API integration with gRPC
- **SpeakerFilterService**: Speaker diarization and voice filtering
- **PdfService**: Professional PDF report generation

### 4. Screens (`lib/src/screens/`)
- **HomeScreen**: Main dashboard with navigation options
- **PatientInfoScreen**: Patient information entry form
- **RecordingScreen**: Audio recording with live transcript preview
- **ReviewTranscriptScreen**: Transcript editing and PDF generation
- **HistoryScreen**: Consultation history management
- **SettingsScreen**: API configuration and app preferences

### 5. Widgets (`lib/src/widgets/`)
- **AppScaffold**: Consistent app layout and navigation
- **RecordingControls**: Audio recording control interface
- **AudioVisualizer**: Real-time audio level visualization
- **LiveTranscriptWidget**: Streaming transcript display

## Features Implemented

### Core Functionality
✅ Patient information entry and validation
✅ High-quality audio recording with real-time visualization
✅ NVIDIA Riva ASR integration for speech-to-text
✅ Live transcript preview during recording
✅ Speaker filtering and diarization
✅ Transcript review and editing capabilities
✅ Professional PDF report generation
✅ Consultation history management
✅ Secure local data storage

### Technical Features
✅ Cross-platform Flutter application (iOS/Android)
✅ Provider-based state management
✅ gRPC integration for NVIDIA Riva API
✅ SQLite database with encrypted storage
✅ File sharing capabilities
✅ Offline mode support
✅ Multi-language transcription support
✅ HIPAA-compliant security measures

### User Experience
✅ Intuitive Material Design interface
✅ Large, touch-friendly controls for medical environments
✅ Real-time feedback and progress indicators
✅ Error handling and user guidance
✅ Search and filter capabilities
✅ Professional PDF formatting

## API Integration

The application integrates with NVIDIA Riva Canary-1B ASR model:
- **Endpoint**: grpc.nvcf.nvidia.com:443
- **Function ID**: ee8dc628-76de-4acc-8595-1836e7e857bd
- **Authentication**: Bearer token with API key
- **Supported Languages**: 15+ languages including English, Spanish, Hindi, French, German, Italian, Portuguese, Japanese, Korean, Chinese, Russian, and Arabic

## Security & Privacy

- **Encrypted Storage**: All patient data encrypted at rest
- **Secure Communication**: HTTPS/TLS for API calls
- **Local Processing**: Minimal cloud dependency
- **Permission Management**: Proper microphone and storage permissions
- **HIPAA Compliance**: Healthcare privacy standards

## Getting Started

1. **Prerequisites**: Flutter 3.0+, NVIDIA Riva API key
2. **Installation**: `flutter pub get`
3. **Configuration**: Add API key in Settings
4. **Build**: `flutter build apk` or `flutter build ios`
5. **Deploy**: Install on target devices

## Development Notes

- **State Management**: Uses Provider pattern for reactive UI updates
- **Architecture**: Clean separation of concerns with services layer
- **Testing**: Basic widget tests included, expandable for integration tests
- **Performance**: Optimized for real-time audio processing and transcription
- **Scalability**: Modular design allows easy feature additions

This project provides a complete, production-ready solution for medical consultation transcription with professional PDF reporting capabilities.

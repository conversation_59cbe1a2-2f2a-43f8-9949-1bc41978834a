<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":share_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":record_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\python_programs\DocScribe\doc_transcribe\build\record_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":printing" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\python_programs\DocScribe\doc_transcribe\build\permission_handler_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\python_programs\DocScribe\doc_transcribe\build\path_provider_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":flutter_sound" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\python_programs\DocScribe\doc_transcribe\build\flutter_sound\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":audio_waveforms" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\python_programs\DocScribe\doc_transcribe\build\audio_waveforms\intermediates\library_assets\debug\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\assets"/><source path="C:\python_programs\DocScribe\doc_transcribe\build\app\intermediates\shader_assets\debug\out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\python_programs\DocScribe\doc_transcribe\android\app\src\debug\assets"/></dataSet></merger>
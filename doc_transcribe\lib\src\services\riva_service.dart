import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import '../models/transcript.dart';
import 'riva_grpc_client.dart';

// Mock gRPC classes for NVIDIA Riva
// In a real implementation, these would be generated from .proto files
class StreamingRecognizeRequest {
  final Uint8List? audioContent;
  final RecognitionConfig? config;
  
  StreamingRecognizeRequest({this.audioContent, this.config});
}

class RecognitionConfig {
  final String encoding;
  final int sampleRateHertz;
  final String languageCode;
  final int audioChannelCount;
  
  RecognitionConfig({
    required this.encoding,
    required this.sampleRateHertz,
    required this.languageCode,
    required this.audioChannelCount,
  });
}

class StreamingRecognizeResponse {
  final List<SpeechRecognitionResult> results;
  
  StreamingRecognizeResponse({required this.results});
}

class SpeechRecognitionResult {
  final List<SpeechRecognitionAlternative> alternatives;
  final bool isFinal;
  
  SpeechRecognitionResult({
    required this.alternatives,
    required this.isFinal,
  });
}

class SpeechRecognitionAlternative {
  final String transcript;
  final double confidence;
  
  SpeechRecognitionAlternative({
    required this.transcript,
    required this.confidence,
  });
}

class RivaService {
  static const String defaultEndpoint = 'grpc.nvcf.nvidia.com';
  static const String functionId = 'ee8dc628-76de-4acc-8595-1836e7e857bd'; // Canary-1B ASR

  RivaGrpcClient? _grpcClient;
  StreamController<TranscriptSegment>? _transcriptController;
  StreamSubscription? _audioSubscription;
  bool _isConnected = false;

  Stream<TranscriptSegment> get transcriptStream =>
      _transcriptController?.stream ?? const Stream.empty();

  bool get isConnected => _isConnected;

  // Initialize Riva service
  Future<void> initialize({
    required String apiKey,
    required String language,
    String? endpoint,
  }) async {
    try {
      // Create gRPC client
      _grpcClient = RivaGrpcClient();
      await _grpcClient!.initialize(
        apiKey: apiKey,
        language: language,
        endpoint: endpoint,
      );

      // Initialize transcript stream
      _transcriptController = StreamController<TranscriptSegment>.broadcast();

      _isConnected = true;
    } catch (e) {
      throw Exception('Failed to initialize Riva service: $e');
    }
  }

  // Start streaming recognition
  Future<void> startStreamingRecognition(Stream<Uint8List> audioStream) async {
    if (!_isConnected || _grpcClient == null) {
      throw Exception('Riva service not properly initialized');
    }

    try {
      // Use the real gRPC client for streaming recognition
      final transcriptStream = _grpcClient!.startStreamingRecognition(audioStream);

      _audioSubscription = transcriptStream.listen(
        (segment) => _transcriptController?.add(segment),
        onError: (error) => _transcriptController?.addError(error),
        onDone: () => _finishRecognition(),
      );
    } catch (e) {
      throw Exception('Failed to start streaming recognition: $e');
    }
  }

  // Process audio file (offline transcription)
  Future<List<TranscriptSegment>> transcribeAudioFile(
    String audioFilePath,
    String language,
  ) async {
    if (!_isConnected || _grpcClient == null) {
      throw Exception('Riva service not properly initialized');
    }

    try {
      final file = File(audioFilePath);
      if (!await file.exists()) {
        throw Exception('Audio file not found: $audioFilePath');
      }

      // Use the real gRPC client for file transcription
      return await _grpcClient!.transcribeAudioFile(audioFilePath, language);
    } catch (e) {
      throw Exception('Failed to transcribe audio file: $e');
    }
  }

  // Private methods
  void _finishRecognition() {
    // Called when streaming recognition is complete
    // The gRPC client handles the final segments
  }



  // Disconnect from Riva service
  Future<void> disconnect() async {
    try {
      await _audioSubscription?.cancel();
      await _transcriptController?.close();
      await _grpcClient?.close();

      _audioSubscription = null;
      _transcriptController = null;
      _grpcClient = null;
      _isConnected = false;
    } catch (e) {
      // Log error but don't throw
      print('Error disconnecting from Riva service: $e');
    }
  }

  // Get supported languages
  static Map<String, String> getSupportedLanguages() {
    return {
      'en-US': 'English (US)',
      'es-ES': 'Spanish (Spain)',
      'hi-IN': 'Hindi (India)',
      'fr-FR': 'French (France)',
      'de-DE': 'German (Germany)',
      'it-IT': 'Italian (Italy)',
      'pt-BR': 'Portuguese (Brazil)',
      'ja-JP': 'Japanese (Japan)',
      'ko-KR': 'Korean (Korea)',
      'zh-CN': 'Chinese (Simplified)',
    };
  }

  // Validate API key format
  static bool isValidApiKey(String apiKey) {
    return apiKey.startsWith('nvapi-') && apiKey.length > 20;
  }
}

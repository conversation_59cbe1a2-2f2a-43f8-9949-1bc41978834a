import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import '../models/transcript.dart';
import 'nvidia_riva_client.dart';

// Mock gRPC classes for NVIDIA Riva
// In a real implementation, these would be generated from .proto files
class StreamingRecognizeRequest {
  final Uint8List? audioContent;
  final RecognitionConfig? config;
  
  StreamingRecognizeRequest({this.audioContent, this.config});
}

class RecognitionConfig {
  final String encoding;
  final int sampleRateHertz;
  final String languageCode;
  final int audioChannelCount;
  
  RecognitionConfig({
    required this.encoding,
    required this.sampleRateHertz,
    required this.languageCode,
    required this.audioChannelCount,
  });
}

class StreamingRecognizeResponse {
  final List<SpeechRecognitionResult> results;
  
  StreamingRecognizeResponse({required this.results});
}

class SpeechRecognitionResult {
  final List<SpeechRecognitionAlternative> alternatives;
  final bool isFinal;
  
  SpeechRecognitionResult({
    required this.alternatives,
    required this.isFinal,
  });
}

class SpeechRecognitionAlternative {
  final String transcript;
  final double confidence;
  
  SpeechRecognitionAlternative({
    required this.transcript,
    required this.confidence,
  });
}

class RivaService {
  static const String defaultEndpoint = 'grpc.nvcf.nvidia.com';
  static const String functionId = 'ee8dc628-76de-4acc-8595-1836e7e857bd'; // Canary-1B ASR

  NvidiaRivaClient? _rivaClient;
  StreamController<TranscriptSegment>? _transcriptController;
  StreamSubscription? _audioSubscription;
  bool _isConnected = false;

  Stream<TranscriptSegment> get transcriptStream =>
      _rivaClient?.transcriptStream ?? const Stream.empty();

  bool get isConnected => _isConnected;

  // Initialize Riva service
  Future<void> initialize({
    required String apiKey,
    required String language,
    String? endpoint,
  }) async {
    try {
      // Create NVIDIA Riva client
      _rivaClient = NvidiaRivaClient();
      await _rivaClient!.initialize(
        apiKey: apiKey,
        language: language,
      );

      // Initialize transcript stream
      _transcriptController = StreamController<TranscriptSegment>.broadcast();

      _isConnected = true;
      print('Riva service initialized successfully');
    } catch (e) {
      throw Exception('Failed to initialize Riva service: $e');
    }
  }

  // Start streaming recognition
  Future<void> startStreamingRecognition(Stream<Uint8List> audioStream) async {
    if (!_isConnected || _rivaClient == null) {
      throw Exception('Riva service not properly initialized');
    }

    try {
      // Use the real NVIDIA Riva client for streaming recognition
      await _rivaClient!.startStreamingRecognition(audioStream: audioStream);
      print('Started streaming recognition with NVIDIA Riva');
    } catch (e) {
      throw Exception('Failed to start streaming recognition: $e');
    }
  }

  // Process audio file (offline transcription)
  Future<List<TranscriptSegment>> transcribeAudioFile(
    String audioFilePath,
    String language,
  ) async {
    if (!_isConnected || _rivaClient == null) {
      throw Exception('Riva service not properly initialized');
    }

    try {
      final file = File(audioFilePath);
      if (!await file.exists()) {
        throw Exception('Audio file not found: $audioFilePath');
      }

      // For file transcription, we'll read the file and process it
      // This is a simplified implementation
      final audioData = await file.readAsBytes();

      // Create a stream from the file data
      final audioStream = Stream.fromIterable([audioData]);
      await _rivaClient!.startStreamingRecognition(audioStream: audioStream);

      // Wait for transcription to complete and return segments
      // In a real implementation, this would be more sophisticated
      await Future.delayed(Duration(seconds: 2));
      return [];
    } catch (e) {
      throw Exception('Failed to transcribe audio file: $e');
    }
  }

  // Disconnect from Riva service
  Future<void> disconnect() async {
    try {
      await _audioSubscription?.cancel();
      await _transcriptController?.close();
      await _rivaClient?.disconnect();

      _audioSubscription = null;
      _transcriptController = null;
      _rivaClient = null;
      _isConnected = false;
    } catch (e) {
      // Log error but don't throw
      print('Error disconnecting from Riva service: $e');
    }
  }

  // Get supported languages
  static Map<String, String> getSupportedLanguages() {
    return {
      'en-US': 'English (US)',
      'es-ES': 'Spanish (Spain)',
      'hi-IN': 'Hindi (India)',
      'fr-FR': 'French (France)',
      'de-DE': 'German (Germany)',
      'it-IT': 'Italian (Italy)',
      'pt-BR': 'Portuguese (Brazil)',
      'ja-JP': 'Japanese (Japan)',
      'ko-KR': 'Korean (Korea)',
      'zh-CN': 'Chinese (Simplified)',
    };
  }

  // Validate API key format
  static bool isValidApiKey(String apiKey) {
    return apiKey.startsWith('nvapi-') && apiKey.length > 20;
  }
}

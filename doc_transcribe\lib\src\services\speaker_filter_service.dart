import 'dart:async';
import 'dart:io';
import 'dart:math';
import '../models/transcript.dart';

enum FilteringMethod {
  postProcessing,
  realTime,
}

class SpeakerFilterService {
  static final SpeakerFilterService _instance = SpeakerFilterService._internal();
  static SpeakerFilterService get instance => _instance;
  SpeakerFilterService._internal();

  FilteringMethod _method = FilteringMethod.postProcessing;
  Map<String, SpeakerType> _speakerMapping = {};
  List<double>? _doctorVoiceprint;
  List<double>? _patientVoiceprint;

  FilteringMethod get method => _method;

  // Set filtering method
  void setFilteringMethod(FilteringMethod method) {
    _method = method;
  }

  // Filter speakers from transcript segments
  Future<List<TranscriptSegment>> filterSpeakers(
    String audioFilePath,
    List<TranscriptSegment> segments,
  ) async {
    switch (_method) {
      case FilteringMethod.postProcessing:
        return await _postProcessingFilter(audioFilePath, segments);
      case FilteringMethod.realTime:
        return await _realTimeFilter(segments);
    }
  }

  // Post-processing diarization approach
  Future<List<TranscriptSegment>> _postProcessingFilter(
    String audioFilePath,
    List<TranscriptSegment> segments,
  ) async {
    try {
      // Step 1: Perform speaker diarization on the audio file
      final speakerSegments = await _performDiarization(audioFilePath);
      
      // Step 2: Map diarization results to transcript segments
      final mappedSegments = await _mapSpeakersToTranscript(speakerSegments, segments);
      
      // Step 3: Filter out non-doctor/patient speakers
      final filteredSegments = mappedSegments
          .where((segment) => 
              segment.speaker == SpeakerType.doctor || 
              segment.speaker == SpeakerType.patient)
          .toList();

      return filteredSegments;
    } catch (e) {
      throw Exception('Failed to filter speakers: $e');
    }
  }

  // Real-time voiceprint filtering approach
  Future<List<TranscriptSegment>> _realTimeFilter(
    List<TranscriptSegment> segments,
  ) async {
    try {
      // Filter segments based on pre-trained voiceprints
      final filteredSegments = <TranscriptSegment>[];

      for (final segment in segments) {
        final speakerType = await _classifySpeaker(segment);
        if (speakerType == SpeakerType.doctor || speakerType == SpeakerType.patient) {
          filteredSegments.add(segment.copyWith(speaker: speakerType));
        }
      }

      return filteredSegments;
    } catch (e) {
      throw Exception('Failed to filter speakers in real-time: $e');
    }
  }

  // Perform speaker diarization (mock implementation)
  Future<List<SpeakerSegment>> _performDiarization(String audioFilePath) async {
    // In a real implementation, this would use a diarization library
    // such as pyannote-audio or similar
    
    await Future.delayed(const Duration(seconds: 1)); // Simulate processing time

    // Mock diarization results
    final segments = <SpeakerSegment>[];
    final random = Random();
    final duration = await _getAudioDuration(audioFilePath);
    
    double currentTime = 0.0;
    int currentSpeaker = 0;
    
    while (currentTime < duration) {
      final segmentDuration = 5.0 + random.nextDouble() * 10.0; // 5-15 seconds
      final endTime = (currentTime + segmentDuration).clamp(0.0, duration);
      
      segments.add(SpeakerSegment(
        startTime: currentTime,
        endTime: endTime,
        speakerId: 'speaker_$currentSpeaker',
        confidence: 0.8 + random.nextDouble() * 0.2,
      ));
      
      currentTime = endTime;
      currentSpeaker = (currentSpeaker + 1) % 3; // Cycle through 3 speakers
    }

    return segments;
  }

  // Map speaker diarization results to transcript segments
  Future<List<TranscriptSegment>> _mapSpeakersToTranscript(
    List<SpeakerSegment> speakerSegments,
    List<TranscriptSegment> transcriptSegments,
  ) async {
    final mappedSegments = <TranscriptSegment>[];

    for (final transcriptSegment in transcriptSegments) {
      // Find the speaker segment that contains this transcript segment
      final speakerSegment = _findSpeakerSegment(speakerSegments, transcriptSegment);
      
      if (speakerSegment != null) {
        final speakerType = _mapSpeakerIdToType(speakerSegment.speakerId);
        mappedSegments.add(transcriptSegment.copyWith(speaker: speakerType));
      } else {
        // If no speaker segment found, mark as other
        mappedSegments.add(transcriptSegment.copyWith(speaker: SpeakerType.other));
      }
    }

    return mappedSegments;
  }

  // Find speaker segment that contains the transcript segment
  SpeakerSegment? _findSpeakerSegment(
    List<SpeakerSegment> speakerSegments,
    TranscriptSegment transcriptSegment,
  ) {
    // Convert timestamp to seconds since start of recording
    final segmentTime = _timestampToSeconds(transcriptSegment.timestamp);
    
    for (final speakerSegment in speakerSegments) {
      if (segmentTime >= speakerSegment.startTime && 
          segmentTime <= speakerSegment.endTime) {
        return speakerSegment;
      }
    }
    
    return null;
  }

  // Map speaker ID to speaker type
  SpeakerType _mapSpeakerIdToType(String speakerId) {
    // Use cached mapping if available
    if (_speakerMapping.containsKey(speakerId)) {
      return _speakerMapping[speakerId]!;
    }

    // Simple heuristic: assume first two speakers are doctor and patient
    // In a real implementation, this would use voiceprint matching
    switch (speakerId) {
      case 'speaker_0':
        _speakerMapping[speakerId] = SpeakerType.doctor;
        return SpeakerType.doctor;
      case 'speaker_1':
        _speakerMapping[speakerId] = SpeakerType.patient;
        return SpeakerType.patient;
      default:
        _speakerMapping[speakerId] = SpeakerType.other;
        return SpeakerType.other;
    }
  }

  // Classify speaker using voiceprint (mock implementation)
  Future<SpeakerType> _classifySpeaker(TranscriptSegment segment) async {
    // In a real implementation, this would extract audio features
    // and compare against stored voiceprints
    
    // Mock classification based on text patterns
    final text = segment.text.toLowerCase();
    
    if (text.contains('doctor') || text.contains('examine') || text.contains('prescribe')) {
      return SpeakerType.doctor;
    } else if (text.contains('pain') || text.contains('feel') || text.contains('hurt')) {
      return SpeakerType.patient;
    }
    
    // Default to alternating speakers
    return DateTime.now().millisecondsSinceEpoch % 2 == 0 
        ? SpeakerType.doctor 
        : SpeakerType.patient;
  }

  // Record doctor voiceprint
  Future<void> recordDoctorVoiceprint(String audioSamplePath) async {
    try {
      _doctorVoiceprint = await _extractVoiceprint(audioSamplePath);
    } catch (e) {
      throw Exception('Failed to record doctor voiceprint: $e');
    }
  }

  // Record patient voiceprint
  Future<void> recordPatientVoiceprint(String audioSamplePath) async {
    try {
      _patientVoiceprint = await _extractVoiceprint(audioSamplePath);
    } catch (e) {
      throw Exception('Failed to record patient voiceprint: $e');
    }
  }

  // Extract voiceprint from audio sample (mock implementation)
  Future<List<double>> _extractVoiceprint(String audioPath) async {
    // In a real implementation, this would extract MFCC features
    // or use a pre-trained speaker embedding model
    
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Generate mock voiceprint
    final random = Random();
    return List.generate(128, (index) => random.nextDouble());
  }

  // Helper methods
  double _timestampToSeconds(DateTime timestamp) {
    // Convert timestamp to seconds since start of recording
    // This is a simplified implementation
    return timestamp.millisecondsSinceEpoch / 1000.0;
  }

  Future<double> _getAudioDuration(String audioFilePath) async {
    // Get audio file duration in seconds
    // This is a mock implementation
    final file = File(audioFilePath);
    if (!await file.exists()) return 0.0;
    
    final stats = await file.stat();
    // Estimate duration based on file size (rough approximation)
    return stats.size / 32000.0; // Assuming 16kHz, 16-bit, mono
  }

  // Clear cached data
  void clearCache() {
    _speakerMapping.clear();
    _doctorVoiceprint = null;
    _patientVoiceprint = null;
  }
}

// Helper class for speaker diarization results
class SpeakerSegment {
  final double startTime;
  final double endTime;
  final String speakerId;
  final double confidence;

  SpeakerSegment({
    required this.startTime,
    required this.endTime,
    required this.speakerId,
    required this.confidence,
  });

  double get duration => endTime - startTime;

  @override
  String toString() {
    return 'SpeakerSegment(speaker: $speakerId, start: ${startTime.toStringAsFixed(2)}s, end: ${endTime.toStringAsFixed(2)}s)';
  }
}

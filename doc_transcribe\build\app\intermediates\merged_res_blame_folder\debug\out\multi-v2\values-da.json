{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,750,837,912,997,1084,1155,1219,1317,1413,1485,1550,1616,1686,1762,1839,1913,1986,2066,2142,2211,2295,2378,2441,2509,2562,2620,2668,2729,2799,2871,2939,3013,3077,3136,3200,3270,3336,3388,3449,3525,3600", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,75,76,73,72,79,75,68,83,82,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "280,469,659,745,832,907,992,1079,1150,1214,1312,1408,1480,1545,1611,1681,1757,1834,1908,1981,2061,2137,2206,2290,2373,2436,2504,2557,2615,2663,2724,2794,2866,2934,3008,3072,3131,3195,3265,3331,3383,3444,3520,3595,3648"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,1391,1477,1564,1639,1724,1811,1882,1946,2044,2140,2212,2277,2343,2413,2489,2566,2640,2713,2793,2869,2938,3022,3105,3168,3913,3966,4024,4072,4133,4203,4275,4343,4417,4481,4540,4604,4674,4740,4792,4853,4929,5004", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,75,76,73,72,79,75,68,83,82,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "280,469,659,1472,1559,1634,1719,1806,1877,1941,2039,2135,2207,2272,2338,2408,2484,2561,2635,2708,2788,2864,2933,3017,3100,3163,3231,3961,4019,4067,4128,4198,4270,4338,4412,4476,4535,4599,4669,4735,4787,4848,4924,4999,5052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,189,253,322,399,473,573,664", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "122,184,248,317,394,468,568,659,727"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3236,3308,3370,3434,3503,3580,3654,3754,3845", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "3303,3365,3429,3498,3575,3649,3749,3840,3908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "664,760,862,959,1057,1164,1273,5057", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "755,857,954,1052,1159,1268,1386,5153"}}]}]}
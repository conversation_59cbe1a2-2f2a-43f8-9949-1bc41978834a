{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3423,3497,3558,3623,3694,3772,3844,3931,4014", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "3492,3553,3618,3689,3767,3839,3926,4009,4082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,898,980,1062,1151,1242,1312,1378,1471,1565,1633,1697,1760,1832,1907,1991,2068,2144,2231,2304,2375,2471,2565,2632,2697,2750,2808,2856,2917,2983,3047,3110,3175,3239,3300,3366,3431,3497,3549,3611,3687,3763", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,74,83,76,75,86,72,70,95,93,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,893,975,1057,1146,1237,1307,1373,1466,1560,1628,1692,1755,1827,1902,1986,2063,2139,2226,2299,2370,2466,2560,2627,2692,2745,2803,2851,2912,2978,3042,3105,3170,3234,3295,3361,3426,3492,3544,3606,3682,3758,3814"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,1543,1624,1706,1788,1877,1968,2038,2104,2197,2291,2359,2423,2486,2558,2633,2717,2794,2870,2957,3030,3101,3197,3291,3358,4087,4140,4198,4246,4307,4373,4437,4500,4565,4629,4690,4756,4821,4887,4939,5001,5077,5153", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,74,83,76,75,86,72,70,95,93,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,1619,1701,1783,1872,1963,2033,2099,2192,2286,2354,2418,2481,2553,2628,2712,2789,2865,2952,3025,3096,3192,3286,3353,3418,4135,4193,4241,4302,4368,4432,4495,4560,4624,4685,4751,4816,4882,4934,4996,5072,5148,5204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "817,915,1017,1114,1218,1322,1427,5209", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "910,1012,1109,1213,1317,1422,1538,5305"}}]}]}
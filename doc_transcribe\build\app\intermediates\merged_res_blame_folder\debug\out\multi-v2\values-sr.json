{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6201,6275,6336,6401,6472,6550,6622,6709,6792", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "6270,6331,6396,6467,6545,6617,6704,6787,6860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,898,980,1062,1151,1242,1312,1378,1471,1565,1633,1697,1760,1832,1907,1991,2068,2144,2231,2304,2375,2471,2565,2632,2697,2750,2808,2856,2917,2983,3047,3110,3175,3239,3300,3366,3431,3497,3549,3611,3687,3763", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,74,83,76,75,86,72,70,95,93,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,893,975,1057,1146,1237,1307,1373,1466,1560,1628,1692,1755,1827,1902,1986,2063,2139,2226,2299,2370,2466,2560,2627,2692,2745,2803,2851,2912,2978,3042,3105,3170,3234,3295,3361,3426,3492,3544,3606,3682,3758,3814"}, "to": {"startLines": "2,11,16,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,606,4321,4402,4484,4566,4655,4746,4816,4882,4975,5069,5137,5201,5264,5336,5411,5495,5572,5648,5735,5808,5879,5975,6069,6136,6865,6918,6976,7024,7085,7151,7215,7278,7343,7407,7468,7534,7599,7665,7717,7779,7855,7931", "endLines": "10,15,20,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,74,83,76,75,86,72,70,95,93,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "332,601,862,4397,4479,4561,4650,4741,4811,4877,4970,5064,5132,5196,5259,5331,5406,5490,5567,5643,5730,5803,5874,5970,6064,6131,6196,6913,6971,7019,7080,7146,7210,7273,7338,7402,7463,7529,7594,7660,7712,7774,7850,7926,7982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "867,974,1075,1181,1267,1371,1493,1577,1658,1749,1842,1937,2031,2131,2224,2319,2424,2515,2606,2692,2797,2903,3006,3112,3221,3328,3498,7987", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "969,1070,1176,1262,1366,1488,1572,1653,1744,1837,1932,2026,2126,2219,2314,2419,2510,2601,2687,2792,2898,3001,3107,3216,3323,3493,3590,8069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "48,49,50,51,52,53,54,107", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3595,3693,3795,3892,3996,4100,4205,8074", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3688,3790,3887,3991,4095,4200,4316,8170"}}]}]}
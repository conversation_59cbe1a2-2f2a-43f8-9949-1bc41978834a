{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,255,330,411,485,579,665", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "123,186,250,325,406,480,574,660,733"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3247,3320,3383,3447,3522,3603,3677,3771,3857", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "3315,3378,3442,3517,3598,3672,3766,3852,3925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,674,760,847,930,1018,1102,1170,1234,1332,1430,1495,1563,1629,1702,1779,1856,1931,2006,2088,2164,2232,2314,2390,2455,2519,2572,2632,2680,2741,2805,2876,2940,3005,3070,3129,3194,3258,3324,3376,3436,3519,3602", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,76,76,74,74,81,75,67,81,75,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "280,475,669,755,842,925,1013,1097,1165,1229,1327,1425,1490,1558,1624,1697,1774,1851,1926,2001,2083,2159,2227,2309,2385,2450,2514,2567,2627,2675,2736,2800,2871,2935,3000,3065,3124,3189,3253,3319,3371,3431,3514,3597,3649"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,1402,1488,1575,1658,1746,1830,1898,1962,2060,2158,2223,2291,2357,2430,2507,2584,2659,2734,2816,2892,2960,3042,3118,3183,3930,3983,4043,4091,4152,4216,4287,4351,4416,4481,4540,4605,4669,4735,4787,4847,4930,5013", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,76,76,74,74,81,75,67,81,75,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "280,475,669,1483,1570,1653,1741,1825,1893,1957,2055,2153,2218,2286,2352,2425,2502,2579,2654,2729,2811,2887,2955,3037,3113,3178,3242,3978,4038,4086,4147,4211,4282,4346,4411,4476,4535,4600,4664,4730,4782,4842,4925,5008,5060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "674,769,871,969,1068,1176,1281,5065", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "764,866,964,1063,1171,1276,1397,5161"}}]}]}
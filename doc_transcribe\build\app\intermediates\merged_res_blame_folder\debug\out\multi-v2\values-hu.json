{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "692,789,891,993,1094,1197,1304,5163", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "784,886,988,1089,1192,1299,1409,5259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,692,780,867,945,1031,1134,1208,1276,1373,1474,1547,1615,1680,1748,1834,1913,1996,2070,2152,2226,2299,2384,2468,2536,2599,2652,2710,2758,2819,2883,2950,3014,3082,3147,3206,3271,3337,3403,3456,3521,3603,3685", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,85,78,82,73,81,73,72,84,83,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "280,485,687,775,862,940,1026,1129,1203,1271,1368,1469,1542,1610,1675,1743,1829,1908,1991,2065,2147,2221,2294,2379,2463,2531,2594,2647,2705,2753,2814,2878,2945,3009,3077,3142,3201,3266,3332,3398,3451,3516,3598,3680,3737"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,1414,1502,1589,1667,1753,1856,1930,1998,2095,2196,2269,2337,2402,2470,2556,2635,2718,2792,2874,2948,3021,3106,3190,3258,4020,4073,4131,4179,4240,4304,4371,4435,4503,4568,4627,4692,4758,4824,4877,4942,5024,5106", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,85,78,82,73,81,73,72,84,83,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "280,485,687,1497,1584,1662,1748,1851,1925,1993,2090,2191,2264,2332,2397,2465,2551,2630,2713,2787,2869,2943,3016,3101,3185,3253,3316,4068,4126,4174,4235,4299,4366,4430,4498,4563,4622,4687,4753,4819,4872,4937,5019,5101,5158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,192,266,338,416,489,583,673", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "125,187,261,333,411,484,578,668,749"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3321,3396,3458,3532,3604,3682,3755,3849,3939", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "3391,3453,3527,3599,3677,3750,3844,3934,4015"}}]}]}
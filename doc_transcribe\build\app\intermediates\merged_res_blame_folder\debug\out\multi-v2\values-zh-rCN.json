{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,678,747,815,892,968,1022,1084,1158,1232,1294,1355,1414,1480,1551,1621,1692,1755,1822,1887,1941,2016,2090,2151,2213,2265,2323,2370,2431,2488,2550,2607,2668,2724,2779,2842,2904,2967,3016,3067,3132,3197", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,70,69,70,62,66,64,53,74,73,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "282,445,603,673,742,810,887,963,1017,1079,1153,1227,1289,1350,1409,1475,1546,1616,1687,1750,1817,1882,1936,2011,2085,2146,2208,2260,2318,2365,2426,2483,2545,2602,2663,2719,2774,2837,2899,2962,3011,3062,3127,3192,3241"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,1272,1342,1411,1479,1556,1632,1686,1748,1822,1896,1958,2019,2078,2144,2215,2285,2356,2419,2486,2551,2605,2680,2754,2815,3438,3490,3548,3595,3656,3713,3775,3832,3893,3949,4004,4067,4129,4192,4241,4292,4357,4422", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,70,69,70,62,66,64,53,74,73,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "282,445,603,1337,1406,1474,1551,1627,1681,1743,1817,1891,1953,2014,2073,2139,2210,2280,2351,2414,2481,2546,2600,2675,2749,2810,2872,3485,3543,3590,3651,3708,3770,3827,3888,3944,3999,4062,4124,4187,4236,4287,4352,4417,4466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "608,700,801,895,989,1082,1176,4471", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "695,796,890,984,1077,1171,1267,4567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,479,557", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "106,162,220,273,345,399,474,552,611"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2877,2933,2989,3047,3100,3172,3226,3301,3379", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "2928,2984,3042,3095,3167,3221,3296,3374,3433"}}]}]}
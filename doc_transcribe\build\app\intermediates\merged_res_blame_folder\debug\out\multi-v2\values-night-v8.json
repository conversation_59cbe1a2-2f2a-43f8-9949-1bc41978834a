{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "8,9,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "396,466,550,634,730,832,934,1028", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "461,545,629,725,827,929,1023,1112"}}, {"source": "C:\\python_programs\\DocScribe\\doc_transcribe\\android\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "17,8", "startColumns": "4,4", "startOffsets": "827,387", "endLines": "19,10", "endColumns": "12,12", "endOffsets": "994,551"}, "to": {"startLines": "2,5", "startColumns": "4,4", "startOffsets": "55,227", "endLines": "4,7", "endColumns": "12,12", "endOffsets": "222,391"}}]}]}
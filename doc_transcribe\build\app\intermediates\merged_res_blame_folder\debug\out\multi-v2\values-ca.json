{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "666,762,864,963,1060,1166,1271,5200", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "757,859,958,1055,1161,1266,1392,5296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,487,666,752,840,923,1022,1120,1201,1267,1380,1490,1563,1632,1698,1769,1846,1931,2008,2077,2165,2240,2322,2419,2512,2576,2640,2693,2751,2799,2860,2925,2994,3059,3131,3195,3252,3318,3382,3448,3501,3561,3635,3709", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,76,84,76,68,87,74,81,96,92,63,63,52,57,47,60,64,68,64,71,63,56,65,63,65,52,59,73,73,56", "endOffsets": "280,482,661,747,835,918,1017,1115,1196,1262,1375,1485,1558,1627,1693,1764,1841,1926,2003,2072,2160,2235,2317,2414,2507,2571,2635,2688,2746,2794,2855,2920,2989,3054,3126,3190,3247,3313,3377,3443,3496,3556,3630,3704,3761"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,487,1397,1483,1571,1654,1753,1851,1932,1998,2111,2221,2294,2363,2429,2500,2577,2662,2739,2808,2896,2971,3053,3150,3243,3307,4074,4127,4185,4233,4294,4359,4428,4493,4565,4629,4686,4752,4816,4882,4935,4995,5069,5143", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,76,84,76,68,87,74,81,96,92,63,63,52,57,47,60,64,68,64,71,63,56,65,63,65,52,59,73,73,56", "endOffsets": "280,482,661,1478,1566,1649,1748,1846,1927,1993,2106,2216,2289,2358,2424,2495,2572,2657,2734,2803,2891,2966,3048,3145,3238,3302,3366,4122,4180,4228,4289,4354,4423,4488,4560,4624,4681,4747,4811,4877,4930,4990,5064,5138,5195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,261,331,407,483,581,676", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "128,187,256,326,402,478,576,671,753"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3371,3449,3508,3577,3647,3723,3799,3897,3992", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "3444,3503,3572,3642,3718,3794,3892,3987,4069"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,689,769,837,915,992,1048,1109,1183,1257,1319,1380,1439,1504,1577,1647,1720,1783,1850,1915,1971,2044,2116,2177,2240,2292,2350,2397,2458,2515,2577,2634,2695,2751,2806,2869,2931,2994,3043,3095,3161,3227", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,72,69,72,62,66,64,55,72,71,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "282,445,603,684,764,832,910,987,1043,1104,1178,1252,1314,1375,1434,1499,1572,1642,1715,1778,1845,1910,1966,2039,2111,2172,2235,2287,2345,2392,2453,2510,2572,2629,2690,2746,2801,2864,2926,2989,3038,3090,3156,3222,3271"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,1269,1350,1430,1498,1576,1653,1709,1770,1844,1918,1980,2041,2100,2165,2238,2308,2381,2444,2511,2576,2632,2705,2777,2838,3460,3512,3570,3617,3678,3735,3797,3854,3915,3971,4026,4089,4151,4214,4263,4315,4381,4447", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,72,69,72,62,66,64,55,72,71,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "282,445,603,1345,1425,1493,1571,1648,1704,1765,1839,1913,1975,2036,2095,2160,2233,2303,2376,2439,2506,2571,2627,2700,2772,2833,2896,3507,3565,3612,3673,3730,3792,3849,3910,3966,4021,4084,4146,4209,4258,4310,4376,4442,4491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,224,277,347,401,477,555", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "106,162,219,272,342,396,472,550,609"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2901,2957,3013,3070,3123,3193,3247,3323,3401", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "2952,3008,3065,3118,3188,3242,3318,3396,3455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "608,700,799,893,987,1080,1173,4496", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "695,794,888,982,1075,1168,1264,4592"}}]}]}
/ Header Record For PersistentHashMapValueStoragem 1io.flutter.embedding.engine.plugins.FlutterPlugin:io.flutter.embedding.engine.plugins.activity.ActivityAware9 8io.flutter.plugin.common.MethodChannel.MethodCallHandler; :com.llfbandit.record.record.bluetooth.BluetoothScoListenerI Hio.flutter.plugin.common.PluginRegistry.RequestPermissionsResultListener kotlin.Enum" !android.content.BroadcastReceiver7 6com.llfbandit.record.record.container.IContainerWriter7 6com.llfbandit.record.record.container.IContainerWriter7 6com.llfbandit.record.record.container.IContainerWriter7 6com.llfbandit.record.record.container.IContainerWriter7 6com.llfbandit.record.record.container.IContainerWriterb ,com.llfbandit.record.record.encoder.IEncoderandroid.os.HandlerThreadandroid.os.Handler.Callback" !android.media.MediaCodec.Callback- ,com.llfbandit.record.record.encoder.IEncoder* )com.llfbandit.record.record.format.Format* )com.llfbandit.record.record.format.Format* )com.llfbandit.record.record.format.Format* )com.llfbandit.record.record.format.Format* )com.llfbandit.record.record.format.Format* )com.llfbandit.record.record.format.Format* )com.llfbandit.record.record.format.Formatj .com.llfbandit.record.record.recorder.IRecorder:com.llfbandit.record.record.recorder.OnAudioRecordListener/ .com.llfbandit.record.record.recorder.IRecorder4 3com.llfbandit.record.record.encoder.EncoderListener4 3io.flutter.plugin.common.EventChannel.StreamHandler4 3io.flutter.plugin.common.EventChannel.StreamHandler
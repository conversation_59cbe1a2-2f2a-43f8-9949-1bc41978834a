name: doc_transcribe
description: A Flutter app for doctors to record, transcribe, and generate PDF reports of patient consultations.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6

  # State Management
  provider: ^6.1.1

  # UI Components
  intl: ^0.19.0
  uuid: ^4.3.3

  # Real Audio Recording
  permission_handler: ^11.3.0
  path_provider: ^2.1.2
  record: ^5.0.4
  audio_waveforms: ^1.0.5

  # gRPC for NVIDIA Riva
  grpc: ^3.2.4
  protobuf: ^3.1.0
  http: ^1.1.0

  # PDF Generation
  pdf: ^3.10.7
  printing: ^5.12.0

  # File Sharing
  share_plus: ^7.2.2

  # Local Storage
  path: ^1.8.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/

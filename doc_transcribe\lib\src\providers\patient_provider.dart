import 'package:flutter/foundation.dart';
import '../models/patient.dart';

class PatientProvider extends ChangeNotifier {
  String _name = '';
  int? _age;
  String _gender = 'Male';
  String _medicalRecordNumber = '';
  String _reasonForVisit = '';

  // Getters
  String get name => _name;
  int? get age => _age;
  String get gender => _gender;
  String get medicalRecordNumber => _medicalRecordNumber;
  String get reasonForVisit => _reasonForVisit;

  // Validation
  bool get isNameValid => _name.trim().isNotEmpty;
  bool get isAgeValid => _age != null && _age! > 0 && _age! <= 150;
  bool get isFormValid => isNameValid && isAgeValid;

  List<String> get genderOptions => ['Male', 'Female', 'Other'];

  // Setters
  void setName(String name) {
    _name = name;
    notifyListeners();
  }

  void setAge(int? age) {
    _age = age;
    notifyListeners();
  }

  void setGender(String gender) {
    _gender = gender;
    notifyListeners();
  }

  void setMedicalRecordNumber(String mrn) {
    _medicalRecordNumber = mrn;
    notifyListeners();
  }

  void setReasonForVisit(String reason) {
    _reasonForVisit = reason;
    notifyListeners();
  }

  // Create patient from current form data
  Patient createPatient() {
    if (!isFormValid) {
      throw Exception('Patient form is not valid');
    }

    return Patient(
      name: _name.trim(),
      age: _age!,
      gender: _gender,
      medicalRecordNumber: _medicalRecordNumber.trim().isEmpty 
          ? null 
          : _medicalRecordNumber.trim(),
      reasonForVisit: _reasonForVisit.trim().isEmpty 
          ? null 
          : _reasonForVisit.trim(),
    );
  }

  // Load patient data into form
  void loadPatient(Patient patient) {
    _name = patient.name;
    _age = patient.age;
    _gender = patient.gender;
    _medicalRecordNumber = patient.medicalRecordNumber ?? '';
    _reasonForVisit = patient.reasonForVisit ?? '';
    notifyListeners();
  }

  // Clear form
  void clearForm() {
    _name = '';
    _age = null;
    _gender = 'Male';
    _medicalRecordNumber = '';
    _reasonForVisit = '';
    notifyListeners();
  }

  // Validation messages
  String? getNameError() {
    if (_name.isEmpty) return null; // Don't show error for empty field initially
    if (!isNameValid) return 'Please enter a valid name';
    return null;
  }

  String? getAgeError() {
    if (_age == null) return null; // Don't show error for empty field initially
    if (!isAgeValid) return 'Please enter a valid age (1-150)';
    return null;
  }

  // Form summary for display
  Map<String, String> getFormSummary() {
    return {
      'Name': _name,
      'Age': _age?.toString() ?? '',
      'Gender': _gender,
      'Medical Record Number': _medicalRecordNumber.isEmpty ? 'Not provided' : _medicalRecordNumber,
      'Reason for Visit': _reasonForVisit.isEmpty ? 'Not provided' : _reasonForVisit,
    };
  }

  @override
  String toString() {
    return 'PatientProvider(name: $_name, age: $_age, gender: $_gender, valid: $isFormValid)';
  }
}

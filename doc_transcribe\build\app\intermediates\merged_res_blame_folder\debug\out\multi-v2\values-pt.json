{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3261,3331,3401,3473,3539,3616,3683,3784,3877", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "3326,3396,3468,3534,3611,3678,3779,3872,3942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1763,1839,1915,1982,2068,2144,2218,2310,2398,2462,2526,2579,2637,2685,2746,2811,2873,2939,3009,3073,3134,3200,3265,3331,3384,3448,3526,3604", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,76,75,75,66,85,75,73,91,87,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1758,1834,1910,1977,2063,2139,2213,2305,2393,2457,2521,2574,2632,2680,2741,2806,2868,2934,3004,3068,3129,3195,3260,3326,3379,3443,3521,3599,3658"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,1385,1466,1549,1622,1721,1817,1891,1957,2053,2148,2214,2283,2350,2421,2498,2574,2650,2717,2803,2879,2953,3045,3133,3197,3947,4000,4058,4106,4167,4232,4294,4360,4430,4494,4555,4621,4686,4752,4805,4869,4947,5025", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,76,75,75,66,85,75,73,91,87,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "280,466,645,1461,1544,1617,1716,1812,1886,1952,2048,2143,2209,2278,2345,2416,2493,2569,2645,2712,2798,2874,2948,3040,3128,3192,3256,3995,4053,4101,4162,4227,4289,4355,4425,4489,4550,4616,4681,4747,4800,4864,4942,5020,5079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "650,747,849,948,1048,1155,1265,5084", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "742,844,943,1043,1150,1260,1380,5180"}}]}]}
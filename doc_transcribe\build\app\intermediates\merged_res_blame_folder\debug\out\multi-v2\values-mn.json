{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,672,758,847,931,1023,1114,1190,1255,1344,1437,1508,1576,1637,1705,1796,1890,1999,2066,2148,2219,2299,2382,2462,2528,2593,2646,2704,2752,2813,2875,2951,3013,3077,3138,3199,3263,3328,3394,3446,3510,3588,3666", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,90,93,108,66,81,70,79,82,79,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "280,491,667,753,842,926,1018,1109,1185,1250,1339,1432,1503,1571,1632,1700,1791,1885,1994,2061,2143,2214,2294,2377,2457,2523,2588,2641,2699,2747,2808,2870,2946,3008,3072,3133,3194,3258,3323,3389,3441,3505,3583,3661,3719"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,1407,1493,1582,1666,1758,1849,1925,1990,2079,2172,2243,2311,2372,2440,2531,2625,2734,2801,2883,2954,3034,3117,3197,3263,3989,4042,4100,4148,4209,4271,4347,4409,4473,4534,4595,4659,4724,4790,4842,4906,4984,5062", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,90,93,108,66,81,70,79,82,79,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "280,491,667,1488,1577,1661,1753,1844,1920,1985,2074,2167,2238,2306,2367,2435,2526,2620,2729,2796,2878,2949,3029,3112,3192,3258,3323,4037,4095,4143,4204,4266,4342,4404,4468,4529,4590,4654,4719,4785,4837,4901,4979,5057,5115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,244,308,382,456,547,635", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "115,174,239,303,377,451,542,630,711"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3328,3393,3452,3517,3581,3655,3729,3820,3908", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "3388,3447,3512,3576,3650,3724,3815,3903,3984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "672,770,872,973,1071,1176,1288,5120", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "765,867,968,1066,1171,1283,1402,5216"}}]}]}
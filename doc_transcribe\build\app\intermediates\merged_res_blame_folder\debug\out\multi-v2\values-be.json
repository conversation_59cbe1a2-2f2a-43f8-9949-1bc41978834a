{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "23,24,25,26,27,28,29,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "957,1055,1157,1257,1358,1464,1567,5414", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "1050,1152,1252,1353,1459,1562,1683,5510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,627,957,1040,1123,1206,1304,1402,1491,1555,1648,1742,1807,1872,1937,2005,2083,2167,2255,2332,2411,2480,2570,2658,2744,2810,2875,2928,2988,3036,3097,3170,3235,3300,3373,3438,3496,3562,3627,3693,3745,3805,3879,3953", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,77,83,87,76,78,68,89,87,85,65,64,52,59,47,60,72,64,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "283,622,952,1035,1118,1201,1299,1397,1486,1550,1643,1737,1802,1867,1932,2000,2078,2162,2250,2327,2406,2475,2565,2653,2739,2805,2870,2923,2983,3031,3092,3165,3230,3295,3368,3433,3491,3557,3622,3688,3740,3800,3874,3948,4003"}, "to": {"startLines": "2,11,17,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,627,1688,1771,1854,1937,2035,2133,2222,2286,2379,2473,2538,2603,2668,2736,2814,2898,2986,3063,3142,3211,3301,3389,3475,3541,4281,4334,4394,4442,4503,4576,4641,4706,4779,4844,4902,4968,5033,5099,5151,5211,5285,5359", "endLines": "10,16,22,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,77,83,87,76,78,68,89,87,85,65,64,52,59,47,60,72,64,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "283,622,952,1766,1849,1932,2030,2128,2217,2281,2374,2468,2533,2598,2663,2731,2809,2893,2981,3058,3137,3206,3296,3384,3470,3536,3601,4329,4389,4437,4498,4571,4636,4701,4774,4839,4897,4963,5028,5094,5146,5206,5280,5354,5409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,257,320,397,465,564,660", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "121,185,252,315,392,460,559,655,725"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3606,3677,3741,3808,3871,3948,4016,4115,4211", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "3672,3736,3803,3866,3943,4011,4110,4206,4276"}}]}]}
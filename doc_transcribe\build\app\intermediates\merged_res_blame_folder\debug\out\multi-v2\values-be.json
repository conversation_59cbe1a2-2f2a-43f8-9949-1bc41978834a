{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "50,51,52,53,54,55,56,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3737,3835,3937,4037,4138,4244,4347,8276", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3830,3932,4032,4133,4239,4342,4463,8372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1007,1127,1230,1346,1432,1537,1656,1736,1813,1905,1999,2094,2188,2283,2377,2473,2568,2660,2752,2833,2939,3044,3142,3250,3356,3464,3637,8194", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "1122,1225,1341,1427,1532,1651,1731,1808,1900,1994,2089,2183,2278,2372,2468,2563,2655,2747,2828,2934,3039,3137,3245,3351,3459,3632,3732,8271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,627,957,1040,1123,1206,1304,1402,1491,1555,1648,1742,1807,1872,1937,2005,2083,2167,2255,2332,2411,2480,2570,2658,2744,2810,2875,2928,2988,3036,3097,3170,3235,3300,3373,3438,3496,3562,3627,3693,3745,3805,3879,3953", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,77,83,87,76,78,68,89,87,85,65,64,52,59,47,60,72,64,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "283,622,952,1035,1118,1201,1299,1397,1486,1550,1643,1737,1802,1867,1932,2000,2078,2162,2250,2327,2406,2475,2565,2653,2739,2805,2870,2923,2983,3031,3092,3165,3230,3295,3368,3433,3491,3557,3622,3688,3740,3800,3874,3948,4003"}, "to": {"startLines": "2,11,17,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,677,4468,4551,4634,4717,4815,4913,5002,5066,5159,5253,5318,5383,5448,5516,5594,5678,5766,5843,5922,5991,6081,6169,6255,6321,7061,7114,7174,7222,7283,7356,7421,7486,7559,7624,7682,7748,7813,7879,7931,7991,8065,8139", "endLines": "10,16,22,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,77,83,87,76,78,68,89,87,85,65,64,52,59,47,60,72,64,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "333,672,1002,4546,4629,4712,4810,4908,4997,5061,5154,5248,5313,5378,5443,5511,5589,5673,5761,5838,5917,5986,6076,6164,6250,6316,6381,7109,7169,7217,7278,7351,7416,7481,7554,7619,7677,7743,7808,7874,7926,7986,8060,8134,8189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,257,320,397,465,564,660", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "121,185,252,315,392,460,559,655,725"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6386,6457,6521,6588,6651,6728,6796,6895,6991", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "6452,6516,6583,6646,6723,6791,6890,6986,7056"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3278,3349,3413,3477,3544,3621,3690,3779,3862", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "3344,3408,3472,3539,3616,3685,3774,3857,3929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "677,779,881,981,1081,1188,1292,5048", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "774,876,976,1076,1183,1287,1406,5144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1808,1887,1964,2037,2116,2191,2260,2337,2413,2479,2544,2597,2655,2703,2764,2829,2891,2956,3024,3082,3140,3206,3271,3337,3389,3451,3527,3603", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,78,78,76,72,78,74,68,76,75,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1803,1882,1959,2032,2111,2186,2255,2332,2408,2474,2539,2592,2650,2698,2759,2824,2886,2951,3019,3077,3135,3201,3266,3332,3384,3446,3522,3598,3653"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,1411,1500,1588,1668,1761,1854,1927,1994,2096,2194,2262,2329,2394,2463,2542,2621,2698,2771,2850,2925,2994,3071,3147,3213,3934,3987,4045,4093,4154,4219,4281,4346,4414,4472,4530,4596,4661,4727,4779,4841,4917,4993", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,78,78,76,72,78,74,68,76,75,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "281,481,672,1495,1583,1663,1756,1849,1922,1989,2091,2189,2257,2324,2389,2458,2537,2616,2693,2766,2845,2920,2989,3066,3142,3208,3273,3982,4040,4088,4149,4214,4276,4341,4409,4467,4525,4591,4656,4722,4774,4836,4912,4988,5043"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "692,789,891,992,1089,1196,1304,5166", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "784,886,987,1084,1191,1299,1421,5262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,692,783,874,954,1039,1130,1208,1274,1375,1478,1545,1610,1672,1743,1820,1899,1979,2048,2135,2209,2289,2379,2467,2532,2596,2649,2707,2755,2816,2881,2943,3008,3076,3140,3198,3264,3328,3394,3446,3508,3587,3666", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,76,78,79,68,86,73,79,89,87,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "280,492,687,778,869,949,1034,1125,1203,1269,1370,1473,1540,1605,1667,1738,1815,1894,1974,2043,2130,2204,2284,2374,2462,2527,2591,2644,2702,2750,2811,2876,2938,3003,3071,3135,3193,3259,3323,3389,3441,3503,3582,3661,3718"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,1426,1517,1608,1688,1773,1864,1942,2008,2109,2212,2279,2344,2406,2477,2554,2633,2713,2782,2869,2943,3023,3113,3201,3266,4039,4092,4150,4198,4259,4324,4386,4451,4519,4583,4641,4707,4771,4837,4889,4951,5030,5109", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,76,78,79,68,86,73,79,89,87,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "280,492,687,1512,1603,1683,1768,1859,1937,2003,2104,2207,2274,2339,2401,2472,2549,2628,2708,2777,2864,2938,3018,3108,3196,3261,3325,4087,4145,4193,4254,4319,4381,4446,4514,4578,4636,4702,4766,4832,4884,4946,5025,5104,5161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,196,264,330,410,488,588,686", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "127,191,259,325,405,483,583,681,759"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3330,3407,3471,3539,3605,3685,3763,3863,3961", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "3402,3466,3534,3600,3680,3758,3858,3956,4034"}}]}]}
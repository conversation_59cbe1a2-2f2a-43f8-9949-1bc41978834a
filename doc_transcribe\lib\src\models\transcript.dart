enum SpeakerType {
  doctor,
  patient,
  other,
}

class TranscriptSegment {
  final String id;
  final DateTime timestamp;
  final SpeakerType speaker;
  final String text;
  final bool isFinal;
  final double confidence;

  TranscriptSegment({
    required this.id,
    required this.timestamp,
    required this.speaker,
    required this.text,
    this.isFinal = false,
    this.confidence = 1.0,
  });

  String get formattedTimestamp {
    final duration = timestamp.difference(DateTime.now().subtract(timestamp.difference(DateTime.now())));
    final hours = duration.inHours.toString().padLeft(2, '0');
    final minutes = (duration.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$hours:$minutes:$seconds';
  }

  String get speakerLabel {
    switch (speaker) {
      case SpeakerType.doctor:
        return 'Doctor';
      case SpeakerType.patient:
        return 'Patient';
      case SpeakerType.other:
        return 'Other';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'speaker': speaker.name,
      'text': text,
      'isFinal': isFinal,
      'confidence': confidence,
    };
  }

  factory TranscriptSegment.fromJson(Map<String, dynamic> json) {
    return TranscriptSegment(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      speaker: SpeakerType.values.firstWhere(
        (e) => e.name == json['speaker'],
        orElse: () => SpeakerType.other,
      ),
      text: json['text'] as String,
      isFinal: json['isFinal'] as bool? ?? false,
      confidence: (json['confidence'] as num?)?.toDouble() ?? 1.0,
    );
  }

  TranscriptSegment copyWith({
    String? id,
    DateTime? timestamp,
    SpeakerType? speaker,
    String? text,
    bool? isFinal,
    double? confidence,
  }) {
    return TranscriptSegment(
      id: id ?? this.id,
      timestamp: timestamp ?? this.timestamp,
      speaker: speaker ?? this.speaker,
      text: text ?? this.text,
      isFinal: isFinal ?? this.isFinal,
      confidence: confidence ?? this.confidence,
    );
  }

  @override
  String toString() {
    return 'TranscriptSegment(speaker: $speakerLabel, text: $text, isFinal: $isFinal)';
  }
}

class Transcript {
  final String id;
  final List<TranscriptSegment> segments;
  final DateTime startTime;
  final DateTime? endTime;
  final String language;
  final String? doctorNotes;

  Transcript({
    required this.id,
    required this.segments,
    required this.startTime,
    this.endTime,
    this.language = 'en-US',
    this.doctorNotes,
  });

  Duration get duration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    }
    return DateTime.now().difference(startTime);
  }

  List<TranscriptSegment> get finalSegments {
    return segments.where((segment) => segment.isFinal).toList();
  }

  List<TranscriptSegment> get doctorPatientSegments {
    return segments.where((segment) => 
      segment.speaker == SpeakerType.doctor || 
      segment.speaker == SpeakerType.patient
    ).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'segments': segments.map((s) => s.toJson()).toList(),
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'language': language,
      'doctorNotes': doctorNotes,
    };
  }

  factory Transcript.fromJson(Map<String, dynamic> json) {
    return Transcript(
      id: json['id'] as String,
      segments: (json['segments'] as List)
          .map((s) => TranscriptSegment.fromJson(s as Map<String, dynamic>))
          .toList(),
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] != null 
          ? DateTime.parse(json['endTime'] as String) 
          : null,
      language: json['language'] as String? ?? 'en-US',
      doctorNotes: json['doctorNotes'] as String?,
    );
  }

  Transcript copyWith({
    String? id,
    List<TranscriptSegment>? segments,
    DateTime? startTime,
    DateTime? endTime,
    String? language,
    String? doctorNotes,
  }) {
    return Transcript(
      id: id ?? this.id,
      segments: segments ?? this.segments,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      language: language ?? this.language,
      doctorNotes: doctorNotes ?? this.doctorNotes,
    );
  }
}

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.doc_transcribe"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:5-67
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:22-64
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:5-71
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:22-68
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:5-81
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:5-80
15-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:22-77
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:5-68
16-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:22-65
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:5-77
17-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:22-74
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->[com.google.android.exoplayer:exoplayer-core:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\AndroidManifest.xml:24:5-79
18-->[com.google.android.exoplayer:exoplayer-core:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\AndroidManifest.xml:24:22-76
19
20    <permission
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
21        android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
25
26    <application
27        android:name="android.app.Application"
27-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:13:9-42
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:icon="@mipmap/ic_launcher"
31-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:14:9-43
32        android:label="DocTranscribe"
32-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:12:9-38
33        android:requestLegacyExternalStorage="true" >
33-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:15:9-52
34        <activity
34-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:17:9-39:20
35            android:name="com.example.doc_transcribe.MainActivity"
35-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:18:13-41
36            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
36-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:22:13-163
37            android:exported="true"
37-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:19:13-36
38            android:hardwareAccelerated="true"
38-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:23:13-47
39            android:launchMode="singleTop"
39-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:20:13-43
40            android:theme="@style/LaunchTheme"
40-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:21:13-47
41            android:windowSoftInputMode="adjustResize" >
41-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:24:13-55
42
43            <!--
44                 Specifies an Android theme to apply to this Activity as soon as
45                 the Android process has started. This theme is visible to the user
46                 while the Flutter UI initializes. After that, this theme continues
47                 to determine the Window background behind the Flutter UI.
48            -->
49            <meta-data
49-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:30:13-33:17
50                android:name="io.flutter.embedding.android.NormalTheme"
50-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:31:15-70
51                android:resource="@style/NormalTheme" />
51-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:32:15-52
52
53            <intent-filter android:autoVerify="true" >
53-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:13-38:29
53-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:28-53
54                <action android:name="android.intent.action.MAIN" />
54-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:17-68
54-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:17-76
56-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:27-74
57            </intent-filter>
58        </activity>
59
60        <!--
61             Don't delete the meta-data below.
62             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
63        -->
64        <meta-data
64-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:43:9-45:33
65            android:name="flutterEmbedding"
65-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:44:13-44
66            android:value="2" />
66-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:45:13-30
67
68        <!-- File provider for sharing files -->
69        <provider
70            android:name="androidx.core.content.FileProvider"
70-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:49:13-62
71            android:authorities="com.example.doc_transcribe.fileprovider"
71-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:50:13-64
72            android:exported="false"
72-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:51:13-37
73            android:grantUriPermissions="true" >
73-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:52:13-47
74            <meta-data
74-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
75                android:name="android.support.FILE_PROVIDER_PATHS"
75-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
76                android:resource="@xml/file_paths" />
76-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
77        </provider>
78        <provider
78-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-18:20
79            android:name="net.nfet.flutter.printing.PrintFileProvider"
79-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
80            android:authorities="com.example.doc_transcribe.flutter.printing"
80-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-68
81            android:exported="false"
81-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-37
82            android:grantUriPermissions="true" >
82-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-47
83            <meta-data
83-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
84                android:name="android.support.FILE_PROVIDER_PATHS"
84-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
85                android:resource="@xml/flutter_printing_file_paths" />
85-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
86        </provider>
87        <!--
88           Declares a provider which allows us to store files to share in
89           '.../caches/share_plus' and grant the receiving action access
90        -->
91        <provider
91-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-23:20
92            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
92-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-77
93            android:authorities="com.example.doc_transcribe.flutter.share_provider"
93-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-74
94            android:exported="false"
94-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-37
95            android:grantUriPermissions="true" >
95-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-47
96            <meta-data
96-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
97                android:name="android.support.FILE_PROVIDER_PATHS"
97-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
98                android:resource="@xml/flutter_share_file_paths" />
98-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
99        </provider>
100        <!--
101           This manifest declared broadcast receiver allows us to use an explicit
102           Intent when creating a PendingItent to be informed of the user's choice
103        -->
104        <receiver
104-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-34:20
105            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
105-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-82
106            android:exported="false" >
106-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-37
107            <intent-filter>
107-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-33:29
108                <action android:name="EXTRA_CHOSEN_COMPONENT" />
108-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:32:17-65
108-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:32:25-62
109            </intent-filter>
110        </receiver>
111
112        <uses-library
112-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
113            android:name="androidx.window.extensions"
113-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
114            android:required="false" />
114-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
115        <uses-library
115-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
116            android:name="androidx.window.sidecar"
116-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
117            android:required="false" />
117-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
118
119        <provider
119-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
120            android:name="androidx.startup.InitializationProvider"
120-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
121            android:authorities="com.example.doc_transcribe.androidx-startup"
121-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
122            android:exported="false" >
122-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
123            <meta-data
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
125                android:value="androidx.startup" />
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
128                android:value="androidx.startup" />
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
129        </provider>
130
131        <receiver
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
132            android:name="androidx.profileinstaller.ProfileInstallReceiver"
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
133            android:directBootAware="false"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
134            android:enabled="true"
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
135            android:exported="true"
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
136            android:permission="android.permission.DUMP" >
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
138                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
139            </intent-filter>
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
141                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
142            </intent-filter>
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
144                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
145            </intent-filter>
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
147                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
148            </intent-filter>
149        </receiver>
150    </application>
151
152</manifest>

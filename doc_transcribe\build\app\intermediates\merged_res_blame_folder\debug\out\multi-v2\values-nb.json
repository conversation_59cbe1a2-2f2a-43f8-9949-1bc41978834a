{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,672,758,843,921,1007,1095,1170,1234,1327,1418,1491,1558,1624,1694,1770,1851,1925,1998,2081,2157,2230,2322,2413,2477,2542,2595,2653,2701,2762,2832,2900,2966,3036,3100,3159,3223,3288,3354,3406,3466,3540,3614", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,75,80,73,72,82,75,72,91,90,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "280,477,667,753,838,916,1002,1090,1165,1229,1322,1413,1486,1553,1619,1689,1765,1846,1920,1993,2076,2152,2225,2317,2408,2472,2537,2590,2648,2696,2757,2827,2895,2961,3031,3095,3154,3218,3283,3349,3401,3461,3535,3609,3662"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,1398,1484,1569,1647,1733,1821,1896,1960,2053,2144,2217,2284,2350,2420,2496,2577,2651,2724,2807,2883,2956,3048,3139,3203,3961,4014,4072,4120,4181,4251,4319,4385,4455,4519,4578,4642,4707,4773,4825,4885,4959,5033", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,75,80,73,72,82,75,72,91,90,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "280,477,667,1479,1564,1642,1728,1816,1891,1955,2048,2139,2212,2279,2345,2415,2491,2572,2646,2719,2802,2878,2951,3043,3134,3198,3263,4009,4067,4115,4176,4246,4314,4380,4450,4514,4573,4637,4702,4768,4820,4880,4954,5028,5081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "672,766,868,965,1064,1172,1278,5086", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "761,863,960,1059,1167,1273,1393,5182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3268,3344,3406,3470,3541,3621,3699,3793,3890", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "3339,3401,3465,3536,3616,3694,3788,3885,3956"}}]}]}
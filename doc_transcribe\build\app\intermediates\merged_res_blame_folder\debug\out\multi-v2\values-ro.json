{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,265,337,415,495,585,678", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "131,195,260,332,410,490,580,673,747"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3501,3582,3646,3711,3783,3861,3941,4031,4124", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "3577,3641,3706,3778,3856,3936,4026,4119,4193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,939,1028,1116,1212,1308,1383,1449,1548,1645,1716,1781,1844,1913,1998,2083,2161,2237,2317,2386,2462,2556,2646,2711,2774,2827,2885,2933,2994,3058,3128,3193,3262,3323,3381,3447,3511,3577,3629,3691,3767,3843", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,934,1023,1111,1207,1303,1378,1444,1543,1640,1711,1776,1839,1908,1993,2078,2156,2232,2312,2381,2457,2551,2641,2706,2769,2822,2880,2928,2989,3053,3123,3188,3257,3318,3376,3442,3506,3572,3624,3686,3762,3838,3895"}, "to": {"startLines": "2,11,16,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,1579,1666,1755,1843,1939,2035,2110,2176,2275,2372,2443,2508,2571,2640,2725,2810,2888,2964,3044,3113,3189,3283,3373,3438,4198,4251,4309,4357,4418,4482,4552,4617,4686,4747,4805,4871,4935,5001,5053,5115,5191,5267", "endLines": "10,15,20,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,1661,1750,1838,1934,2030,2105,2171,2270,2367,2438,2503,2566,2635,2720,2805,2883,2959,3039,3108,3184,3278,3368,3433,3496,4246,4304,4352,4413,4477,4547,4612,4681,4742,4800,4866,4930,4996,5048,5110,5186,5262,5319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "21,22,23,24,25,26,27,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "852,950,1052,1152,1251,1353,1462,5324", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "945,1047,1147,1246,1348,1457,1574,5420"}}]}]}
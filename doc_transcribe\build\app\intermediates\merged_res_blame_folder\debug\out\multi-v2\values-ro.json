{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "902,1023,1127,1240,1324,1428,1549,1634,1714,1805,1898,1993,2087,2187,2280,2375,2469,2560,2652,2735,2847,2955,3055,3169,3275,3381,3545,8120", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "1018,1122,1235,1319,1423,1544,1629,1709,1800,1893,1988,2082,2182,2275,2370,2464,2555,2647,2730,2842,2950,3050,3164,3270,3376,3540,3643,8199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,265,337,415,495,585,678", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "131,195,260,332,410,490,580,673,747"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6297,6378,6442,6507,6579,6657,6737,6827,6920", "endColumns": "80,63,64,71,77,79,89,92,73", "endOffsets": "6373,6437,6502,6574,6652,6732,6822,6915,6989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,939,1028,1116,1212,1308,1383,1449,1548,1645,1716,1781,1844,1913,1998,2083,2161,2237,2317,2386,2462,2556,2646,2711,2774,2827,2885,2933,2994,3058,3128,3193,3262,3323,3381,3447,3511,3577,3629,3691,3767,3843", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,934,1023,1111,1207,1303,1378,1444,1543,1640,1711,1776,1839,1908,1993,2078,2156,2232,2312,2381,2457,2551,2641,2706,2769,2822,2880,2928,2989,3053,3123,3188,3257,3318,3376,3442,3506,3572,3624,3686,3762,3838,3895"}, "to": {"startLines": "2,11,16,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,632,4375,4462,4551,4639,4735,4831,4906,4972,5071,5168,5239,5304,5367,5436,5521,5606,5684,5760,5840,5909,5985,6079,6169,6234,6994,7047,7105,7153,7214,7278,7348,7413,7482,7543,7601,7667,7731,7797,7849,7911,7987,8063", "endLines": "10,15,20,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "endColumns": "17,12,12,86,88,87,95,95,74,65,98,96,70,64,62,68,84,84,77,75,79,68,75,93,89,64,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "331,627,897,4457,4546,4634,4730,4826,4901,4967,5066,5163,5234,5299,5362,5431,5516,5601,5679,5755,5835,5904,5980,6074,6164,6229,6292,7042,7100,7148,7209,7273,7343,7408,7477,7538,7596,7662,7726,7792,7844,7906,7982,8058,8115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "48,49,50,51,52,53,54,107", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3648,3746,3848,3948,4047,4149,4258,8204", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3741,3843,3943,4042,4144,4253,4370,8300"}}]}]}
import 'patient.dart';
import 'transcript.dart';

enum ConsultationStatus {
  draft,
  recording,
  processing,
  completed,
  error,
}

class Consultation {
  final String id;
  final Patient patient;
  final Transcript? transcript;
  final DateTime createdAt;
  final DateTime? completedAt;
  final ConsultationStatus status;
  final String? audioFilePath;
  final String? pdfFilePath;
  final String? doctorName;
  final String? errorMessage;

  Consultation({
    required this.id,
    required this.patient,
    this.transcript,
    DateTime? createdAt,
    this.completedAt,
    this.status = ConsultationStatus.draft,
    this.audioFilePath,
    this.pdfFilePath,
    this.doctorName,
    this.errorMessage,
  }) : createdAt = createdAt ?? DateTime.now();

  Duration? get duration {
    if (transcript != null) {
      return transcript!.duration;
    }
    if (completedAt != null) {
      return completedAt!.difference(createdAt);
    }
    return null;
  }

  String get formattedDuration {
    final dur = duration;
    if (dur == null) return '00:00:00';
    
    final hours = dur.inHours.toString().padLeft(2, '0');
    final minutes = (dur.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (dur.inSeconds % 60).toString().padLeft(2, '0');
    return '$hours:$minutes:$seconds';
  }

  String get displayTitle {
    return '${patient.name} - ${_formatDate(createdAt)}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  bool get isCompleted => status == ConsultationStatus.completed;
  bool get isRecording => status == ConsultationStatus.recording;
  bool get isProcessing => status == ConsultationStatus.processing;
  bool get hasError => status == ConsultationStatus.error;
  bool get hasPdf => pdfFilePath != null && pdfFilePath!.isNotEmpty;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient': patient.toJson(),
      'transcript': transcript?.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'status': status.name,
      'audioFilePath': audioFilePath,
      'pdfFilePath': pdfFilePath,
      'doctorName': doctorName,
      'errorMessage': errorMessage,
    };
  }

  factory Consultation.fromJson(Map<String, dynamic> json) {
    return Consultation(
      id: json['id'] as String,
      patient: Patient.fromJson(json['patient'] as Map<String, dynamic>),
      transcript: json['transcript'] != null
          ? Transcript.fromJson(json['transcript'] as Map<String, dynamic>)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      status: ConsultationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ConsultationStatus.draft,
      ),
      audioFilePath: json['audioFilePath'] as String?,
      pdfFilePath: json['pdfFilePath'] as String?,
      doctorName: json['doctorName'] as String?,
      errorMessage: json['errorMessage'] as String?,
    );
  }

  Consultation copyWith({
    String? id,
    Patient? patient,
    Transcript? transcript,
    DateTime? createdAt,
    DateTime? completedAt,
    ConsultationStatus? status,
    String? audioFilePath,
    String? pdfFilePath,
    String? doctorName,
    String? errorMessage,
  }) {
    return Consultation(
      id: id ?? this.id,
      patient: patient ?? this.patient,
      transcript: transcript ?? this.transcript,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      status: status ?? this.status,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      pdfFilePath: pdfFilePath ?? this.pdfFilePath,
      doctorName: doctorName ?? this.doctorName,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  String toString() {
    return 'Consultation(id: $id, patient: ${patient.name}, status: $status)';
  }
}

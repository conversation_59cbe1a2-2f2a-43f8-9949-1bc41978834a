# DocTranscribe

A Flutter mobile application for doctors to record, transcribe, and generate PDF reports of patient consultations using NVIDIA Riva ASR technology.

## Features

- **Patient Information Entry**: Capture patient details including name, age, gender, medical record number, and reason for visit
- **High-Quality Audio Recording**: Record consultations with real-time audio visualization
- **AI-Powered Transcription**: Leverage NVIDIA Riva Canary-1B ASR model for accurate, multilingual speech-to-text
- **Speaker Filtering**: Automatically filter out third-party voices to focus on doctor-patient dialogue
- **Live Transcript Preview**: View transcription in real-time during recording
- **Transcript Review & Editing**: Review and edit transcripts before generating reports
- **Professional PDF Generation**: Create formatted consultation reports with patient info, transcript, and doctor's notes
- **Consultation History**: Browse, view, and manage past consultations
- **Secure Storage**: Encrypted local storage for patient data and audio files
- **Multi-language Support**: Support for 10+ languages including English, Spanish, Hindi, French, German, and more

## Prerequisites

- Flutter 3.0 or higher
- Dart 3.0 or higher
- Android Studio / Xcode for mobile development
- NVIDIA Riva API key (from NVIDIA NGC)

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd doc_transcribe
   ```

2. **Install dependencies**:
   ```bash
   flutter pub get
   ```

3. **Configure permissions**:
   - Android: Permissions are already configured in `android/app/src/main/AndroidManifest.xml`
   - iOS: Permissions are configured in `ios/Runner/Info.plist`

4. **Get NVIDIA Riva API Key**:
   - Visit [NVIDIA NGC](https://catalog.ngc.nvidia.com)
   - Sign up or log in to your account
   - Navigate to the Riva ASR service
   - Generate an API key for Canary-1B ASR model

## Configuration

1. **Launch the app** and navigate to Settings
2. **Enter your doctor name** for PDF reports
3. **Add your NVIDIA Riva API key**
4. **Select your preferred language** for transcription
5. **Save settings**

## Usage

### Starting a New Consultation

1. **Tap "New Consultation"** on the home screen
2. **Enter patient information**:
   - Patient name (required)
   - Age (required)
   - Gender (required)
   - Medical record number (optional)
   - Reason for visit (optional)
3. **Tap "Start Recording"** to proceed

### Recording a Consultation

1. **Tap the red record button** to start recording
2. **Optional**: Toggle "Show Live Transcript" to view real-time transcription
3. **Use pause/resume** controls as needed
4. **Tap stop** when consultation is complete
5. **Wait for processing** to complete

### Reviewing and Generating Reports

1. **Review the transcript** for accuracy
2. **Edit any segments** if needed using the Edit button
3. **Add doctor's notes** and clinical impressions
4. **Tap "Generate PDF"** to create the report
5. **Share the PDF** via email, messaging, or other apps

### Managing Consultation History

1. **Navigate to "Consultation History"**
2. **Search consultations** by patient name, MRN, or reason
3. **View, share, or delete** past consultations
4. **Tap any consultation** to view details

## Technical Architecture

### Core Components

- **Flutter Frontend**: Cross-platform mobile UI built with Material Design
- **Provider State Management**: Reactive state management for app data
- **NVIDIA Riva Integration**: gRPC client for real-time speech-to-text
- **Local Storage**: SQLite database with encrypted secure storage
- **PDF Generation**: Professional report formatting with patient data and transcripts
- **Audio Processing**: High-quality recording with speaker filtering

### Key Services

- **AudioService**: Handles microphone recording and audio file management
- **RivaService**: Manages connection to NVIDIA Riva ASR API
- **SpeakerFilterService**: Filters out non-doctor/patient voices
- **PdfService**: Generates formatted consultation reports
- **StorageService**: Manages local data persistence and security

### Data Models

- **Patient**: Patient demographic and visit information
- **Transcript**: Timestamped, speaker-labeled conversation segments
- **Consultation**: Complete consultation session with patient, transcript, and metadata

## API Configuration

The app uses NVIDIA Riva Canary-1B ASR model with the following configuration:

- **Endpoint**: `grpc.nvcf.nvidia.com:443`
- **Function ID**: `ee8dc628-76de-4acc-8595-1836e7e857bd`
- **Authentication**: Bearer token with your API key
- **Audio Format**: 16kHz, 16-bit, mono WAV
- **Supported Languages**: 25+ languages including English, Spanish, Hindi, French, German, Italian, Portuguese, Japanese, Korean, Chinese

## Security & Privacy

- **Encrypted Storage**: All patient data and audio files are encrypted at rest
- **Secure API Communication**: HTTPS/TLS for all network communications
- **Local Processing**: Audio and transcripts are processed locally when possible
- **HIPAA Compliance**: Designed with healthcare privacy regulations in mind
- **No Cloud Storage**: All data remains on the device unless explicitly shared

## Offline Mode

- **Audio Recording**: Works completely offline
- **Transcription Queue**: Audio files are queued for processing when network returns
- **PDF Generation**: Works offline using locally stored data
- **Data Sync**: Automatic upload/transcription when connectivity is restored

## Troubleshooting

### Common Issues

1. **Microphone Permission Denied**:
   - Go to device Settings > Apps > DocTranscribe > Permissions
   - Enable Microphone permission

2. **API Key Invalid**:
   - Verify your NVIDIA Riva API key in Settings
   - Ensure the key starts with "nvapi-"
   - Check your NGC account for key validity

3. **Transcription Not Working**:
   - Check internet connectivity
   - Verify API key configuration
   - Try switching to a different language

4. **PDF Generation Failed**:
   - Ensure storage permissions are granted
   - Check available device storage space
   - Try generating PDF again

### Performance Tips

- **Close other apps** during recording for best performance
- **Use in quiet environments** for better transcription accuracy
- **Keep device charged** during long consultations
- **Regular cleanup** of old consultation files to free storage

## Development

### Building for Release

**Android**:
```bash
flutter build apk --release
```

**iOS**:
```bash
flutter build ios --release
```

### Testing

```bash
flutter test
```

### Code Generation

If you modify the data models, regenerate JSON serialization:
```bash
flutter packages pub run build_runner build
```

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- NVIDIA Riva team for the excellent ASR technology
- Flutter team for the cross-platform framework
- Healthcare professionals who provided feedback and requirements

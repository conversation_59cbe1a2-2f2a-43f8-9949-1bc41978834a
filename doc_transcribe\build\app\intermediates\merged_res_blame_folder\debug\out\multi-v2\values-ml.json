{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3524,3626,3729,3831,3935,4038,4139,8159", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3621,3724,3826,3930,4033,4134,4256,8255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,882,988,1099,1190,1295,1417,1495,1570,1661,1754,1855,1949,2049,2143,2238,2337,2428,2519,2601,2710,2814,2913,3025,3137,3258,3423,8076", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "877,983,1094,1185,1290,1412,1490,1565,1656,1749,1850,1944,2044,2138,2233,2332,2423,2514,2596,2705,2809,2908,3020,3132,3253,3418,3519,8154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,294,521,725,826,926,1017,1110,1219,1296,1363,1455,1547,1624,1695,1756,1830,1914,2000,2085,2164,2245,2317,2394,2478,2561,2630,2695,2748,2806,2856,2917,2983,3045,3106,3176,3238,3302,3368,3439,3506,3562,3624,3700,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,83,85,84,78,80,71,76,83,82,68,64,52,57,49,60,65,61,60,69,61,63,65,70,66,55,61,75,75,53", "endOffsets": "289,516,720,821,921,1012,1105,1214,1291,1358,1450,1542,1619,1690,1751,1825,1909,1995,2080,2159,2240,2312,2389,2473,2556,2625,2690,2743,2801,2851,2912,2978,3040,3101,3171,3233,3297,3363,3434,3501,3557,3619,3695,3771,3825"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,344,571,4261,4362,4462,4553,4646,4755,4832,4899,4991,5083,5160,5231,5292,5366,5450,5536,5621,5700,5781,5853,5930,6014,6097,6166,6941,6994,7052,7102,7163,7229,7291,7352,7422,7484,7548,7614,7685,7752,7808,7870,7946,8022", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,83,85,84,78,80,71,76,83,82,68,64,52,57,49,60,65,61,60,69,61,63,65,70,66,55,61,75,75,53", "endOffsets": "339,566,770,4357,4457,4548,4641,4750,4827,4894,4986,5078,5155,5226,5287,5361,5445,5531,5616,5695,5776,5848,5925,6009,6092,6161,6226,6989,7047,7097,7158,7224,7286,7347,7417,7479,7543,7609,7680,7747,7803,7865,7941,8017,8071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,406,487,590,687", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "121,182,254,324,401,482,585,682,760"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6231,6302,6363,6435,6505,6582,6663,6766,6863", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "6297,6358,6430,6500,6577,6658,6761,6858,6936"}}]}]}
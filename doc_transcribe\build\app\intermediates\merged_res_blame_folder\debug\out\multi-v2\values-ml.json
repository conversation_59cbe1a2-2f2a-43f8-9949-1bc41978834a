{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "725,827,930,1032,1136,1239,1340,5277", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "822,925,1027,1131,1234,1335,1457,5373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,294,521,725,826,926,1017,1110,1219,1296,1363,1455,1547,1624,1695,1756,1830,1914,2000,2085,2164,2245,2317,2394,2478,2561,2630,2695,2748,2806,2856,2917,2983,3045,3106,3176,3238,3302,3368,3439,3506,3562,3624,3700,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,83,85,84,78,80,71,76,83,82,68,64,52,57,49,60,65,61,60,69,61,63,65,70,66,55,61,75,75,53", "endOffsets": "289,516,720,821,921,1012,1105,1214,1291,1358,1450,1542,1619,1690,1751,1825,1909,1995,2080,2159,2240,2312,2389,2473,2556,2625,2690,2743,2801,2851,2912,2978,3040,3101,3171,3233,3297,3363,3434,3501,3557,3619,3695,3771,3825"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,294,521,1462,1563,1663,1754,1847,1956,2033,2100,2192,2284,2361,2432,2493,2567,2651,2737,2822,2901,2982,3054,3131,3215,3298,3367,4142,4195,4253,4303,4364,4430,4492,4553,4623,4685,4749,4815,4886,4953,5009,5071,5147,5223", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,83,85,84,78,80,71,76,83,82,68,64,52,57,49,60,65,61,60,69,61,63,65,70,66,55,61,75,75,53", "endOffsets": "289,516,720,1558,1658,1749,1842,1951,2028,2095,2187,2279,2356,2427,2488,2562,2646,2732,2817,2896,2977,3049,3126,3210,3293,3362,3427,4190,4248,4298,4359,4425,4487,4548,4618,4680,4744,4810,4881,4948,5004,5066,5142,5218,5272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,406,487,590,687", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "121,182,254,324,401,482,585,682,760"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3432,3503,3564,3636,3706,3783,3864,3967,4064", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "3498,3559,3631,3701,3778,3859,3962,4059,4137"}}]}]}
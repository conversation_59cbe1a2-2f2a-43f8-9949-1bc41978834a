{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3293,3376,3439,3504,3578,3655,3722,3809,3895", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "3371,3434,3499,3573,3650,3717,3804,3890,3959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "658,757,859,959,1057,1164,1270,5103", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "752,854,954,1052,1159,1265,1385,5199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1790,1865,1941,2011,2098,2168,2248,2340,2431,2497,2561,2614,2672,2720,2779,2844,2906,2972,3044,3108,3169,3235,3300,3366,3419,3484,3563,3642", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,76,74,75,69,86,69,79,91,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1785,1860,1936,2006,2093,2163,2243,2335,2426,2492,2556,2609,2667,2715,2774,2839,2901,2967,3039,3103,3164,3230,3295,3361,3414,3479,3558,3637,3695"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,1390,1476,1563,1648,1744,1840,1915,1983,2078,2173,2239,2308,2374,2445,2522,2597,2673,2743,2830,2900,2980,3072,3163,3229,3964,4017,4075,4123,4182,4247,4309,4375,4447,4511,4572,4638,4703,4769,4822,4887,4966,5045", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,76,74,75,69,86,69,79,91,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,466,653,1471,1558,1643,1739,1835,1910,1978,2073,2168,2234,2303,2369,2440,2517,2592,2668,2738,2825,2895,2975,3067,3158,3224,3288,4012,4070,4118,4177,4242,4304,4370,4442,4506,4567,4633,4698,4764,4817,4882,4961,5040,5098"}}]}]}
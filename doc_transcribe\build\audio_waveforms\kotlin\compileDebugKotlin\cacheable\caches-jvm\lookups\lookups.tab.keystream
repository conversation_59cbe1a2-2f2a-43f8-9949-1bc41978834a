  Manifest android  
permission android.Manifest  RECORD_AUDIO android.Manifest.permission  Activity android.app  cacheDir android.app.Activity  getCACHEDir android.app.Activity  getCacheDir android.app.Activity  getLET android.app.Activity  getLet android.app.Activity  let android.app.Activity  setCacheDir android.app.Activity  Context android.content  let android.content.Context  let android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  AudioFormat 
android.media  
MediaCodec 
android.media  MediaExtractor 
android.media  MediaFormat 
android.media  MediaMetadataRetriever 
android.media  
MediaRecorder 
android.media  ENCODING_PCM_16BIT android.media.AudioFormat  ENCODING_PCM_8BIT android.media.AudioFormat  ENCODING_PCM_FLOAT android.media.AudioFormat  BUFFER_FLAG_END_OF_STREAM android.media.MediaCodec  
BufferInfo android.media.MediaCodec  Callback android.media.MediaCodec  CodecException android.media.MediaCodec  also android.media.MediaCodec  	configure android.media.MediaCodec  createDecoderByType android.media.MediaCodec  getALSO android.media.MediaCodec  getAlso android.media.MediaCodec  getInputBuffer android.media.MediaCodec  getOutputBuffer android.media.MediaCodec  queueInputBuffer android.media.MediaCodec  release android.media.MediaCodec  releaseOutputBuffer android.media.MediaCodec  setCallback android.media.MediaCodec  start android.media.MediaCodec  stop android.media.MediaCodec  
MediaCodec #android.media.MediaCodec.BufferInfo  flags #android.media.MediaCodec.BufferInfo  getISEof #android.media.MediaCodec.BufferInfo  getIsEof #android.media.MediaCodec.BufferInfo  isEof #android.media.MediaCodec.BufferInfo  offset #android.media.MediaCodec.BufferInfo  size #android.media.MediaCodec.BufferInfo  AudioFormat !android.media.MediaCodec.Callback  Build !android.media.MediaCodec.Callback  	Constants !android.media.MediaCodec.Callback  Int !android.media.MediaCodec.Callback  
MediaCodec !android.media.MediaCodec.Callback  MediaFormat !android.media.MediaCodec.Callback  channels !android.media.MediaCodec.Callback  durationMillis !android.media.MediaCodec.Callback  expectedPoints !android.media.MediaCodec.Callback  	extractor !android.media.MediaCodec.Callback  finishCount !android.media.MediaCodec.Callback  handle16bit !android.media.MediaCodec.Callback  handle32bit !android.media.MediaCodec.Callback  
handle8bit !android.media.MediaCodec.Callback  inputEof !android.media.MediaCodec.Callback  isEof !android.media.MediaCodec.Callback  let !android.media.MediaCodec.Callback  pcmEncodingBit !android.media.MediaCodec.Callback  perSamplePoints !android.media.MediaCodec.Callback  result !android.media.MediaCodec.Callback  
sampleRate !android.media.MediaCodec.Callback  stop !android.media.MediaCodec.Callback  totalSamples !android.media.MediaCodec.Callback  message 'android.media.MediaCodec.CodecException  advance android.media.MediaExtractor  
getSAMPLETime android.media.MediaExtractor  
getSampleTime android.media.MediaExtractor  
getTRACKCount android.media.MediaExtractor  
getTrackCount android.media.MediaExtractor  getTrackFormat android.media.MediaExtractor  readSampleData android.media.MediaExtractor  release android.media.MediaExtractor  
sampleTime android.media.MediaExtractor  selectTrack android.media.MediaExtractor  
setDataSource android.media.MediaExtractor  
setSampleTime android.media.MediaExtractor  
setTrackCount android.media.MediaExtractor  
trackCount android.media.MediaExtractor  KEY_CHANNEL_COUNT android.media.MediaFormat  KEY_DURATION android.media.MediaFormat  KEY_MIME android.media.MediaFormat  KEY_PCM_ENCODING android.media.MediaFormat  KEY_SAMPLE_RATE android.media.MediaFormat  containsKey android.media.MediaFormat  
getInteger android.media.MediaFormat  getLong android.media.MediaFormat  	getString android.media.MediaFormat  METADATA_KEY_DURATION $android.media.MediaMetadataRetriever  extractMetadata $android.media.MediaMetadataRetriever  release $android.media.MediaMetadataRetriever  
setDataSource $android.media.MediaMetadataRetriever  AudioEncoder android.media.MediaRecorder  AudioSource android.media.MediaRecorder  LOG_TAG android.media.MediaRecorder  Log android.media.MediaRecorder  
MediaRecorder android.media.MediaRecorder  OutputFormat android.media.MediaRecorder  apply android.media.MediaRecorder  getAPPLY android.media.MediaRecorder  getApply android.media.MediaRecorder  
getEncoder android.media.MediaRecorder  
getGETEncoder android.media.MediaRecorder  getGETOutputFormat android.media.MediaRecorder  
getGetEncoder android.media.MediaRecorder  getGetOutputFormat android.media.MediaRecorder  getMAXAmplitude android.media.MediaRecorder  getMaxAmplitude android.media.MediaRecorder  getOutputFormat android.media.MediaRecorder  maxAmplitude android.media.MediaRecorder  pause android.media.MediaRecorder  prepare android.media.MediaRecorder  release android.media.MediaRecorder  reset android.media.MediaRecorder  resume android.media.MediaRecorder  setAudioEncoder android.media.MediaRecorder  setAudioEncodingBitRate android.media.MediaRecorder  setAudioSamplingRate android.media.MediaRecorder  setAudioSource android.media.MediaRecorder  setMaxAmplitude android.media.MediaRecorder  
setOutputFile android.media.MediaRecorder  setOutputFormat android.media.MediaRecorder  start android.media.MediaRecorder  stop android.media.MediaRecorder  AAC (android.media.MediaRecorder.AudioEncoder  AAC_ELD (android.media.MediaRecorder.AudioEncoder  AMR_NB (android.media.MediaRecorder.AudioEncoder  AMR_WB (android.media.MediaRecorder.AudioEncoder  HE_AAC (android.media.MediaRecorder.AudioEncoder  OPUS (android.media.MediaRecorder.AudioEncoder  VORBIS (android.media.MediaRecorder.AudioEncoder  MIC 'android.media.MediaRecorder.AudioSource  AAC_ADTS (android.media.MediaRecorder.OutputFormat  AMR_NB (android.media.MediaRecorder.OutputFormat  AMR_WB (android.media.MediaRecorder.OutputFormat  	MPEG_2_TS (android.media.MediaRecorder.OutputFormat  MPEG_4 (android.media.MediaRecorder.OutputFormat  OGG (android.media.MediaRecorder.OutputFormat  	THREE_GPP (android.media.MediaRecorder.OutputFormat  WEBM (android.media.MediaRecorder.OutputFormat  Uri android.net  parse android.net.Uri  Build 
android.os  Handler 
android.os  Looper 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  post android.os.Handler  postDelayed android.os.Handler  removeCallbacks android.os.Handler  
getMainLooper android.os.Looper  Log android.util  d android.util.Log  e android.util.Log  let  android.view.ContextThemeWrapper  NonNull androidx.annotation  RequiresApi androidx.annotation  ActivityCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  	ExoPlayer com.google.android.exoplayer2  	MediaItem com.google.android.exoplayer2  PlaybackException com.google.android.exoplayer2  Player com.google.android.exoplayer2  Builder 'com.google.android.exoplayer2.ExoPlayer  addListener 'com.google.android.exoplayer2.ExoPlayer  addMediaItem 'com.google.android.exoplayer2.ExoPlayer  currentPosition 'com.google.android.exoplayer2.ExoPlayer  duration 'com.google.android.exoplayer2.ExoPlayer  getCURRENTPosition 'com.google.android.exoplayer2.ExoPlayer  getCurrentPosition 'com.google.android.exoplayer2.ExoPlayer  getDURATION 'com.google.android.exoplayer2.ExoPlayer  getDuration 'com.google.android.exoplayer2.ExoPlayer  getPLAYWhenReady 'com.google.android.exoplayer2.ExoPlayer  getPlayWhenReady 'com.google.android.exoplayer2.ExoPlayer  	getVOLUME 'com.google.android.exoplayer2.ExoPlayer  	getVolume 'com.google.android.exoplayer2.ExoPlayer  pause 'com.google.android.exoplayer2.ExoPlayer  play 'com.google.android.exoplayer2.ExoPlayer  
playWhenReady 'com.google.android.exoplayer2.ExoPlayer  prepare 'com.google.android.exoplayer2.ExoPlayer  release 'com.google.android.exoplayer2.ExoPlayer  removeListener 'com.google.android.exoplayer2.ExoPlayer  seekTo 'com.google.android.exoplayer2.ExoPlayer  setCurrentPosition 'com.google.android.exoplayer2.ExoPlayer  setDuration 'com.google.android.exoplayer2.ExoPlayer  setPlayWhenReady 'com.google.android.exoplayer2.ExoPlayer  setPlaybackSpeed 'com.google.android.exoplayer2.ExoPlayer  	setVolume 'com.google.android.exoplayer2.ExoPlayer  stop 'com.google.android.exoplayer2.ExoPlayer  volume 'com.google.android.exoplayer2.ExoPlayer  build /com.google.android.exoplayer2.ExoPlayer.Builder  fromUri 'com.google.android.exoplayer2.MediaItem  message /com.google.android.exoplayer2.PlaybackException  Listener $com.google.android.exoplayer2.Player  STATE_ENDED $com.google.android.exoplayer2.Player  STATE_READY $com.google.android.exoplayer2.Player  equals -com.google.android.exoplayer2.Player.Listener  
onPlayerError -com.google.android.exoplayer2.Player.Listener  ActivityCompat com.simform.audio_waveforms  Any com.simform.audio_waveforms  Array com.simform.audio_waveforms  	ArrayList com.simform.audio_waveforms  AudioFormat com.simform.audio_waveforms  AudioPlayer com.simform.audio_waveforms  
AudioRecorder com.simform.audio_waveforms  AudioWaveformsPlugin com.simform.audio_waveforms  Boolean com.simform.audio_waveforms  Build com.simform.audio_waveforms  	Constants com.simform.audio_waveforms  CountDownLatch com.simform.audio_waveforms  Date com.simform.audio_waveforms  Double com.simform.audio_waveforms  DurationType com.simform.audio_waveforms  	Exception com.simform.audio_waveforms  	ExoPlayer com.simform.audio_waveforms  ExtractorCallBack com.simform.audio_waveforms  File com.simform.audio_waveforms  
FinishMode com.simform.audio_waveforms  Float com.simform.audio_waveforms  Handler com.simform.audio_waveforms  HashMap com.simform.audio_waveforms  IllegalStateException com.simform.audio_waveforms  Int com.simform.audio_waveforms  IntArray com.simform.audio_waveforms  LOG_TAG com.simform.audio_waveforms  Locale com.simform.audio_waveforms  Log com.simform.audio_waveforms  Long com.simform.audio_waveforms  Looper com.simform.audio_waveforms  METADATA_KEY_DURATION com.simform.audio_waveforms  Manifest com.simform.audio_waveforms  Map com.simform.audio_waveforms  
MediaCodec com.simform.audio_waveforms  MediaExtractor com.simform.audio_waveforms  MediaFormat com.simform.audio_waveforms  	MediaItem com.simform.audio_waveforms  MediaMetadataRetriever com.simform.audio_waveforms  
MediaRecorder com.simform.audio_waveforms  
MethodChannel com.simform.audio_waveforms  
MutableMap com.simform.audio_waveforms  PackageManager com.simform.audio_waveforms  Player com.simform.audio_waveforms  RECORD_AUDIO_REQUEST_CODE com.simform.audio_waveforms  RecorderSettings com.simform.audio_waveforms  !RequestPermissionsSuccessCallback com.simform.audio_waveforms  Runnable com.simform.audio_waveforms  RuntimeException com.simform.audio_waveforms  SimpleDateFormat com.simform.audio_waveforms  String com.simform.audio_waveforms  Suppress com.simform.audio_waveforms  Uri com.simform.audio_waveforms  Volatile com.simform.audio_waveforms  WaveformExtractor com.simform.audio_waveforms  also com.simform.audio_waveforms  apply com.simform.audio_waveforms  arrayOf com.simform.audio_waveforms  channels com.simform.audio_waveforms  
component1 com.simform.audio_waveforms  
component2 com.simform.audio_waveforms  contains com.simform.audio_waveforms  durationMillis com.simform.audio_waveforms  error com.simform.audio_waveforms  expectedPoints com.simform.audio_waveforms  	extractor com.simform.audio_waveforms  
extractors com.simform.audio_waveforms  finishCount com.simform.audio_waveforms  
finishMode com.simform.audio_waveforms  get com.simform.audio_waveforms  
getEncoder com.simform.audio_waveforms  getOutputFormat com.simform.audio_waveforms  handle16bit com.simform.audio_waveforms  handle32bit com.simform.audio_waveforms  
handle8bit com.simform.audio_waveforms  handler com.simform.audio_waveforms  inputEof com.simform.audio_waveforms  invoke com.simform.audio_waveforms  isEof com.simform.audio_waveforms  
isNotEmpty com.simform.audio_waveforms  isPlayerPrepared com.simform.audio_waveforms  iterator com.simform.audio_waveforms  key com.simform.audio_waveforms  let com.simform.audio_waveforms  log10 com.simform.audio_waveforms  
methodChannel com.simform.audio_waveforms  mutableMapOf com.simform.audio_waveforms  pcmEncodingBit com.simform.audio_waveforms  perSamplePoints com.simform.audio_waveforms  player com.simform.audio_waveforms  
plusAssign com.simform.audio_waveforms  pow com.simform.audio_waveforms  repeat com.simform.audio_waveforms  result com.simform.audio_waveforms  
sampleRate com.simform.audio_waveforms  sendCurrentDuration com.simform.audio_waveforms  set com.simform.audio_waveforms  sqrt com.simform.audio_waveforms  stop com.simform.audio_waveforms  
stopListening com.simform.audio_waveforms  toInt com.simform.audio_waveforms  totalSamples com.simform.audio_waveforms  updateFrequency com.simform.audio_waveforms  Any 'com.simform.audio_waveforms.AudioPlayer  Boolean 'com.simform.audio_waveforms.AudioPlayer  	Constants 'com.simform.audio_waveforms.AudioPlayer  Context 'com.simform.audio_waveforms.AudioPlayer  DurationType 'com.simform.audio_waveforms.AudioPlayer  	Exception 'com.simform.audio_waveforms.AudioPlayer  	ExoPlayer 'com.simform.audio_waveforms.AudioPlayer  
FinishMode 'com.simform.audio_waveforms.AudioPlayer  Float 'com.simform.audio_waveforms.AudioPlayer  Handler 'com.simform.audio_waveforms.AudioPlayer  HashMap 'com.simform.audio_waveforms.AudioPlayer  Int 'com.simform.audio_waveforms.AudioPlayer  Long 'com.simform.audio_waveforms.AudioPlayer  Looper 'com.simform.audio_waveforms.AudioPlayer  	MediaItem 'com.simform.audio_waveforms.AudioPlayer  
MethodChannel 'com.simform.audio_waveforms.AudioPlayer  
MutableMap 'com.simform.audio_waveforms.AudioPlayer  PlaybackException 'com.simform.audio_waveforms.AudioPlayer  Player 'com.simform.audio_waveforms.AudioPlayer  Runnable 'com.simform.audio_waveforms.AudioPlayer  String 'com.simform.audio_waveforms.AudioPlayer  Uri 'com.simform.audio_waveforms.AudioPlayer  
appContext 'com.simform.audio_waveforms.AudioPlayer  equals 'com.simform.audio_waveforms.AudioPlayer  
finishMode 'com.simform.audio_waveforms.AudioPlayer  getDuration 'com.simform.audio_waveforms.AudioPlayer  getLET 'com.simform.audio_waveforms.AudioPlayer  getLet 'com.simform.audio_waveforms.AudioPlayer  getSET 'com.simform.audio_waveforms.AudioPlayer  getSet 'com.simform.audio_waveforms.AudioPlayer  handler 'com.simform.audio_waveforms.AudioPlayer  isPlayerPrepared 'com.simform.audio_waveforms.AudioPlayer  key 'com.simform.audio_waveforms.AudioPlayer  let 'com.simform.audio_waveforms.AudioPlayer  
methodChannel 'com.simform.audio_waveforms.AudioPlayer  pause 'com.simform.audio_waveforms.AudioPlayer  player 'com.simform.audio_waveforms.AudioPlayer  playerListener 'com.simform.audio_waveforms.AudioPlayer  
preparePlayer 'com.simform.audio_waveforms.AudioPlayer  release 'com.simform.audio_waveforms.AudioPlayer  runnable 'com.simform.audio_waveforms.AudioPlayer  seekToPosition 'com.simform.audio_waveforms.AudioPlayer  sendCurrentDuration 'com.simform.audio_waveforms.AudioPlayer  set 'com.simform.audio_waveforms.AudioPlayer  
setFinishMode 'com.simform.audio_waveforms.AudioPlayer  setRate 'com.simform.audio_waveforms.AudioPlayer  	setVolume 'com.simform.audio_waveforms.AudioPlayer  start 'com.simform.audio_waveforms.AudioPlayer  startListening 'com.simform.audio_waveforms.AudioPlayer  stop 'com.simform.audio_waveforms.AudioPlayer  
stopListening 'com.simform.audio_waveforms.AudioPlayer  updateFrequency 'com.simform.audio_waveforms.AudioPlayer  
getFINISHMode Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  
getFinishMode Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getISPlayerPrepared Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getIsPlayerPrepared Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getKEY Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getKey Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getMETHODChannel Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getMethodChannel Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  	getPLAYER Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  	getPlayer Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getSET Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getSTOPListening Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getSet Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  getStopListening Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  isPlayerPrepared Hcom.simform.audio_waveforms.AudioPlayer.preparePlayer.<no name provided>  
getHANDLER Icom.simform.audio_waveforms.AudioPlayer.startListening.<no name provided>  
getHandler Icom.simform.audio_waveforms.AudioPlayer.startListening.<no name provided>  getSENDCurrentDuration Icom.simform.audio_waveforms.AudioPlayer.startListening.<no name provided>  getSendCurrentDuration Icom.simform.audio_waveforms.AudioPlayer.startListening.<no name provided>  getUPDATEFrequency Icom.simform.audio_waveforms.AudioPlayer.startListening.<no name provided>  getUpdateFrequency Icom.simform.audio_waveforms.AudioPlayer.startListening.<no name provided>  Activity )com.simform.audio_waveforms.AudioRecorder  ActivityCompat )com.simform.audio_waveforms.AudioRecorder  Any )com.simform.audio_waveforms.AudioRecorder  Array )com.simform.audio_waveforms.AudioRecorder  Boolean )com.simform.audio_waveforms.AudioRecorder  Build )com.simform.audio_waveforms.AudioRecorder  	Constants )com.simform.audio_waveforms.AudioRecorder  Double )com.simform.audio_waveforms.AudioRecorder  	Exception )com.simform.audio_waveforms.AudioRecorder  HashMap )com.simform.audio_waveforms.AudioRecorder  IOException )com.simform.audio_waveforms.AudioRecorder  IllegalStateException )com.simform.audio_waveforms.AudioRecorder  Int )com.simform.audio_waveforms.AudioRecorder  IntArray )com.simform.audio_waveforms.AudioRecorder  LOG_TAG )com.simform.audio_waveforms.AudioRecorder  Log )com.simform.audio_waveforms.AudioRecorder  METADATA_KEY_DURATION )com.simform.audio_waveforms.AudioRecorder  Manifest )com.simform.audio_waveforms.AudioRecorder  MediaMetadataRetriever )com.simform.audio_waveforms.AudioRecorder  
MediaRecorder )com.simform.audio_waveforms.AudioRecorder  
MethodChannel )com.simform.audio_waveforms.AudioRecorder  PackageManager )com.simform.audio_waveforms.AudioRecorder  RECORD_AUDIO_REQUEST_CODE )com.simform.audio_waveforms.AudioRecorder  RecorderSettings )com.simform.audio_waveforms.AudioRecorder  !RequestPermissionsSuccessCallback )com.simform.audio_waveforms.AudioRecorder  RequiresApi )com.simform.audio_waveforms.AudioRecorder  RuntimeException )com.simform.audio_waveforms.AudioRecorder  String )com.simform.audio_waveforms.AudioRecorder  apply )com.simform.audio_waveforms.AudioRecorder  arrayOf )com.simform.audio_waveforms.AudioRecorder  checkPermission )com.simform.audio_waveforms.AudioRecorder  getAPPLY )com.simform.audio_waveforms.AudioRecorder  
getARRAYOf )com.simform.audio_waveforms.AudioRecorder  getApply )com.simform.audio_waveforms.AudioRecorder  
getArrayOf )com.simform.audio_waveforms.AudioRecorder  
getDecibel )com.simform.audio_waveforms.AudioRecorder  getDuration )com.simform.audio_waveforms.AudioRecorder  
getEncoder )com.simform.audio_waveforms.AudioRecorder  
getISNotEmpty )com.simform.audio_waveforms.AudioRecorder  
getIsNotEmpty )com.simform.audio_waveforms.AudioRecorder  getLET )com.simform.audio_waveforms.AudioRecorder  getLet )com.simform.audio_waveforms.AudioRecorder  getLog10 )com.simform.audio_waveforms.AudioRecorder  getOutputFormat )com.simform.audio_waveforms.AudioRecorder  getSET )com.simform.audio_waveforms.AudioRecorder  getSet )com.simform.audio_waveforms.AudioRecorder  getTOInt )com.simform.audio_waveforms.AudioRecorder  getToInt )com.simform.audio_waveforms.AudioRecorder  initRecorder )com.simform.audio_waveforms.AudioRecorder  
isNotEmpty )com.simform.audio_waveforms.AudioRecorder  isPermissionGranted )com.simform.audio_waveforms.AudioRecorder  let )com.simform.audio_waveforms.AudioRecorder  log10 )com.simform.audio_waveforms.AudioRecorder  pauseRecording )com.simform.audio_waveforms.AudioRecorder  permissions )com.simform.audio_waveforms.AudioRecorder  resumeRecording )com.simform.audio_waveforms.AudioRecorder  set )com.simform.audio_waveforms.AudioRecorder  
startRecorder )com.simform.audio_waveforms.AudioRecorder  
stopRecording )com.simform.audio_waveforms.AudioRecorder  successCallback )com.simform.audio_waveforms.AudioRecorder  toInt )com.simform.audio_waveforms.AudioRecorder  useLegacyNormalization )com.simform.audio_waveforms.AudioRecorder  Activity 0com.simform.audio_waveforms.AudioWaveformsPlugin  ActivityPluginBinding 0com.simform.audio_waveforms.AudioWaveformsPlugin  Any 0com.simform.audio_waveforms.AudioWaveformsPlugin  AudioPlayer 0com.simform.audio_waveforms.AudioWaveformsPlugin  
AudioRecorder 0com.simform.audio_waveforms.AudioWaveformsPlugin  Boolean 0com.simform.audio_waveforms.AudioWaveformsPlugin  Build 0com.simform.audio_waveforms.AudioWaveformsPlugin  	Constants 0com.simform.audio_waveforms.AudioWaveformsPlugin  Context 0com.simform.audio_waveforms.AudioWaveformsPlugin  Date 0com.simform.audio_waveforms.AudioWaveformsPlugin  Double 0com.simform.audio_waveforms.AudioWaveformsPlugin  DurationType 0com.simform.audio_waveforms.AudioWaveformsPlugin  	Exception 0com.simform.audio_waveforms.AudioWaveformsPlugin  ExtractorCallBack 0com.simform.audio_waveforms.AudioWaveformsPlugin  File 0com.simform.audio_waveforms.AudioWaveformsPlugin  Float 0com.simform.audio_waveforms.AudioWaveformsPlugin  
FlutterPlugin 0com.simform.audio_waveforms.AudioWaveformsPlugin  IOException 0com.simform.audio_waveforms.AudioWaveformsPlugin  Int 0com.simform.audio_waveforms.AudioWaveformsPlugin  Locale 0com.simform.audio_waveforms.AudioWaveformsPlugin  Log 0com.simform.audio_waveforms.AudioWaveformsPlugin  Map 0com.simform.audio_waveforms.AudioWaveformsPlugin  
MediaRecorder 0com.simform.audio_waveforms.AudioWaveformsPlugin  
MethodCall 0com.simform.audio_waveforms.AudioWaveformsPlugin  
MethodChannel 0com.simform.audio_waveforms.AudioWaveformsPlugin  NonNull 0com.simform.audio_waveforms.AudioWaveformsPlugin  RecorderSettings 0com.simform.audio_waveforms.AudioWaveformsPlugin  RequiresApi 0com.simform.audio_waveforms.AudioWaveformsPlugin  Result 0com.simform.audio_waveforms.AudioWaveformsPlugin  SimpleDateFormat 0com.simform.audio_waveforms.AudioWaveformsPlugin  String 0com.simform.audio_waveforms.AudioWaveformsPlugin  Suppress 0com.simform.audio_waveforms.AudioWaveformsPlugin  WaveformExtractor 0com.simform.audio_waveforms.AudioWaveformsPlugin  activity 0com.simform.audio_waveforms.AudioWaveformsPlugin  applicationContext 0com.simform.audio_waveforms.AudioWaveformsPlugin  audioPlayers 0com.simform.audio_waveforms.AudioWaveformsPlugin  
audioRecorder 0com.simform.audio_waveforms.AudioWaveformsPlugin  channel 0com.simform.audio_waveforms.AudioWaveformsPlugin  checkPathAndInitialiseRecorder 0com.simform.audio_waveforms.AudioWaveformsPlugin  
component1 0com.simform.audio_waveforms.AudioWaveformsPlugin  
component2 0com.simform.audio_waveforms.AudioWaveformsPlugin  createOrUpdateExtractor 0com.simform.audio_waveforms.AudioWaveformsPlugin  
extractors 0com.simform.audio_waveforms.AudioWaveformsPlugin  get 0com.simform.audio_waveforms.AudioWaveformsPlugin  
getComponent1 0com.simform.audio_waveforms.AudioWaveformsPlugin  
getComponent2 0com.simform.audio_waveforms.AudioWaveformsPlugin  getGET 0com.simform.audio_waveforms.AudioWaveformsPlugin  getGet 0com.simform.audio_waveforms.AudioWaveformsPlugin  getITERATOR 0com.simform.audio_waveforms.AudioWaveformsPlugin  getIterator 0com.simform.audio_waveforms.AudioWaveformsPlugin  getLET 0com.simform.audio_waveforms.AudioWaveformsPlugin  getLet 0com.simform.audio_waveforms.AudioWaveformsPlugin  getMUTABLEMapOf 0com.simform.audio_waveforms.AudioWaveformsPlugin  getMutableMapOf 0com.simform.audio_waveforms.AudioWaveformsPlugin  getSET 0com.simform.audio_waveforms.AudioWaveformsPlugin  getSet 0com.simform.audio_waveforms.AudioWaveformsPlugin  
initPlayer 0com.simform.audio_waveforms.AudioWaveformsPlugin  invoke 0com.simform.audio_waveforms.AudioWaveformsPlugin  iterator 0com.simform.audio_waveforms.AudioWaveformsPlugin  let 0com.simform.audio_waveforms.AudioWaveformsPlugin  mutableMapOf 0com.simform.audio_waveforms.AudioWaveformsPlugin  pauseAllPlayer 0com.simform.audio_waveforms.AudioWaveformsPlugin  
pluginBinding 0com.simform.audio_waveforms.AudioWaveformsPlugin  recorder 0com.simform.audio_waveforms.AudioWaveformsPlugin  recorderSettings 0com.simform.audio_waveforms.AudioWaveformsPlugin  set 0com.simform.audio_waveforms.AudioWaveformsPlugin  
stopAllPlayer 0com.simform.audio_waveforms.AudioWaveformsPlugin  
getEXTRACTORS [com.simform.audio_waveforms.AudioWaveformsPlugin.createOrUpdateExtractor.<no name provided>  
getExtractors [com.simform.audio_waveforms.AudioWaveformsPlugin.createOrUpdateExtractor.<no name provided>  LOG_TAG %com.simform.audio_waveforms.Constants  aac_adts %com.simform.audio_waveforms.Constants  aac_eld %com.simform.audio_waveforms.Constants  acc %com.simform.audio_waveforms.Constants  amr_nb %com.simform.audio_waveforms.Constants  amr_wb %com.simform.audio_waveforms.Constants  bitRate %com.simform.audio_waveforms.Constants  checkPermission %com.simform.audio_waveforms.Constants  current %com.simform.audio_waveforms.Constants  durationType %com.simform.audio_waveforms.Constants  encoder %com.simform.audio_waveforms.Constants  extractWaveformData %com.simform.audio_waveforms.Constants  fileNameFormat %com.simform.audio_waveforms.Constants  
finishMode %com.simform.audio_waveforms.Constants  
finishType %com.simform.audio_waveforms.Constants  
getDecibel %com.simform.audio_waveforms.Constants  getDuration %com.simform.audio_waveforms.Constants  he_aac %com.simform.audio_waveforms.Constants  initRecorder %com.simform.audio_waveforms.Constants  methodChannelName %com.simform.audio_waveforms.Constants  mpeg4 %com.simform.audio_waveforms.Constants  	mpeg_2_ts %com.simform.audio_waveforms.Constants  noOfSamples %com.simform.audio_waveforms.Constants  ogg %com.simform.audio_waveforms.Constants  onCurrentDuration %com.simform.audio_waveforms.Constants  onCurrentExtractedWaveformData %com.simform.audio_waveforms.Constants  onDidFinishPlayingAudio %com.simform.audio_waveforms.Constants  opus %com.simform.audio_waveforms.Constants  outputFormat %com.simform.audio_waveforms.Constants  path %com.simform.audio_waveforms.Constants  pauseAllPlayers %com.simform.audio_waveforms.Constants  pausePlayer %com.simform.audio_waveforms.Constants  pauseRecording %com.simform.audio_waveforms.Constants  	playerKey %com.simform.audio_waveforms.Constants  
preparePlayer %com.simform.audio_waveforms.Constants  progress %com.simform.audio_waveforms.Constants  rate %com.simform.audio_waveforms.Constants  
releasePlayer %com.simform.audio_waveforms.Constants  resultDuration %com.simform.audio_waveforms.Constants  resultFilePath %com.simform.audio_waveforms.Constants  resumeRecording %com.simform.audio_waveforms.Constants  
sampleRate %com.simform.audio_waveforms.Constants  seekTo %com.simform.audio_waveforms.Constants  setRate %com.simform.audio_waveforms.Constants  	setVolume %com.simform.audio_waveforms.Constants  startPlayer %com.simform.audio_waveforms.Constants  startRecording %com.simform.audio_waveforms.Constants  stopAllPlayers %com.simform.audio_waveforms.Constants  
stopPlayer %com.simform.audio_waveforms.Constants  
stopRecording %com.simform.audio_waveforms.Constants  	three_gpp %com.simform.audio_waveforms.Constants  updateFrequency %com.simform.audio_waveforms.Constants  useLegacyNormalization %com.simform.audio_waveforms.Constants  volume %com.simform.audio_waveforms.Constants  vorbis %com.simform.audio_waveforms.Constants  waveformData %com.simform.audio_waveforms.Constants  webm %com.simform.audio_waveforms.Constants  Current (com.simform.audio_waveforms.DurationType  Max (com.simform.audio_waveforms.DurationType  equals (com.simform.audio_waveforms.DurationType  Float -com.simform.audio_waveforms.ExtractorCallBack  
onProgress -com.simform.audio_waveforms.ExtractorCallBack  
FinishMode &com.simform.audio_waveforms.FinishMode  Int &com.simform.audio_waveforms.FinishMode  Loop &com.simform.audio_waveforms.FinishMode  Pause &com.simform.audio_waveforms.FinishMode  Stop &com.simform.audio_waveforms.FinishMode  Any ,com.simform.audio_waveforms.RecorderSettings  	Constants ,com.simform.audio_waveforms.RecorderSettings  Int ,com.simform.audio_waveforms.RecorderSettings  Map ,com.simform.audio_waveforms.RecorderSettings  RecorderSettings ,com.simform.audio_waveforms.RecorderSettings  String ,com.simform.audio_waveforms.RecorderSettings  bitRate ,com.simform.audio_waveforms.RecorderSettings  encoder ,com.simform.audio_waveforms.RecorderSettings  fromJson ,com.simform.audio_waveforms.RecorderSettings  invoke ,com.simform.audio_waveforms.RecorderSettings  outputFormat ,com.simform.audio_waveforms.RecorderSettings  path ,com.simform.audio_waveforms.RecorderSettings  
sampleRate ,com.simform.audio_waveforms.RecorderSettings  Any 6com.simform.audio_waveforms.RecorderSettings.Companion  	Constants 6com.simform.audio_waveforms.RecorderSettings.Companion  Int 6com.simform.audio_waveforms.RecorderSettings.Companion  Map 6com.simform.audio_waveforms.RecorderSettings.Companion  RecorderSettings 6com.simform.audio_waveforms.RecorderSettings.Companion  String 6com.simform.audio_waveforms.RecorderSettings.Companion  fromJson 6com.simform.audio_waveforms.RecorderSettings.Companion  invoke 6com.simform.audio_waveforms.RecorderSettings.Companion  <SAM-CONSTRUCTOR> =com.simform.audio_waveforms.RequestPermissionsSuccessCallback  Boolean =com.simform.audio_waveforms.RequestPermissionsSuccessCallback  	onSuccess =com.simform.audio_waveforms.RequestPermissionsSuccessCallback  Any -com.simform.audio_waveforms.WaveformExtractor  	ArrayList -com.simform.audio_waveforms.WaveformExtractor  AudioFormat -com.simform.audio_waveforms.WaveformExtractor  Build -com.simform.audio_waveforms.WaveformExtractor  
ByteBuffer -com.simform.audio_waveforms.WaveformExtractor  	Constants -com.simform.audio_waveforms.WaveformExtractor  Context -com.simform.audio_waveforms.WaveformExtractor  CountDownLatch -com.simform.audio_waveforms.WaveformExtractor  	Exception -com.simform.audio_waveforms.WaveformExtractor  ExtractorCallBack -com.simform.audio_waveforms.WaveformExtractor  Float -com.simform.audio_waveforms.WaveformExtractor  HashMap -com.simform.audio_waveforms.WaveformExtractor  Int -com.simform.audio_waveforms.WaveformExtractor  
MediaCodec -com.simform.audio_waveforms.WaveformExtractor  MediaExtractor -com.simform.audio_waveforms.WaveformExtractor  MediaFormat -com.simform.audio_waveforms.WaveformExtractor  
MethodChannel -com.simform.audio_waveforms.WaveformExtractor  
MutableMap -com.simform.audio_waveforms.WaveformExtractor  String -com.simform.audio_waveforms.WaveformExtractor  Uri -com.simform.audio_waveforms.WaveformExtractor  Volatile -com.simform.audio_waveforms.WaveformExtractor  also -com.simform.audio_waveforms.WaveformExtractor  channels -com.simform.audio_waveforms.WaveformExtractor  contains -com.simform.audio_waveforms.WaveformExtractor  context -com.simform.audio_waveforms.WaveformExtractor  currentProgress -com.simform.audio_waveforms.WaveformExtractor  decoder -com.simform.audio_waveforms.WaveformExtractor  durationMillis -com.simform.audio_waveforms.WaveformExtractor  error -com.simform.audio_waveforms.WaveformExtractor  expectedPoints -com.simform.audio_waveforms.WaveformExtractor  	extractor -com.simform.audio_waveforms.WaveformExtractor  extractorCallBack -com.simform.audio_waveforms.WaveformExtractor  finishCount -com.simform.audio_waveforms.WaveformExtractor  getALSO -com.simform.audio_waveforms.WaveformExtractor  getAlso -com.simform.audio_waveforms.WaveformExtractor  getCONTAINS -com.simform.audio_waveforms.WaveformExtractor  getContains -com.simform.audio_waveforms.WaveformExtractor  getERROR -com.simform.audio_waveforms.WaveformExtractor  getError -com.simform.audio_waveforms.WaveformExtractor  	getFormat -com.simform.audio_waveforms.WaveformExtractor  getISEof -com.simform.audio_waveforms.WaveformExtractor  getIsEof -com.simform.audio_waveforms.WaveformExtractor  getLET -com.simform.audio_waveforms.WaveformExtractor  getLet -com.simform.audio_waveforms.WaveformExtractor  
getPLUSAssign -com.simform.audio_waveforms.WaveformExtractor  getPOW -com.simform.audio_waveforms.WaveformExtractor  
getPlusAssign -com.simform.audio_waveforms.WaveformExtractor  getPow -com.simform.audio_waveforms.WaveformExtractor  	getREPEAT -com.simform.audio_waveforms.WaveformExtractor  	getRepeat -com.simform.audio_waveforms.WaveformExtractor  getSET -com.simform.audio_waveforms.WaveformExtractor  getSQRT -com.simform.audio_waveforms.WaveformExtractor  getSet -com.simform.audio_waveforms.WaveformExtractor  getSqrt -com.simform.audio_waveforms.WaveformExtractor  handle16bit -com.simform.audio_waveforms.WaveformExtractor  handle32bit -com.simform.audio_waveforms.WaveformExtractor  
handle8bit -com.simform.audio_waveforms.WaveformExtractor  inputEof -com.simform.audio_waveforms.WaveformExtractor  isEof -com.simform.audio_waveforms.WaveformExtractor  key -com.simform.audio_waveforms.WaveformExtractor  let -com.simform.audio_waveforms.WaveformExtractor  
methodChannel -com.simform.audio_waveforms.WaveformExtractor  path -com.simform.audio_waveforms.WaveformExtractor  pcmEncodingBit -com.simform.audio_waveforms.WaveformExtractor  perSamplePoints -com.simform.audio_waveforms.WaveformExtractor  
plusAssign -com.simform.audio_waveforms.WaveformExtractor  pow -com.simform.audio_waveforms.WaveformExtractor  progress -com.simform.audio_waveforms.WaveformExtractor  repeat -com.simform.audio_waveforms.WaveformExtractor  result -com.simform.audio_waveforms.WaveformExtractor  rms -com.simform.audio_waveforms.WaveformExtractor  sampleCount -com.simform.audio_waveforms.WaveformExtractor  
sampleData -com.simform.audio_waveforms.WaveformExtractor  
sampleRate -com.simform.audio_waveforms.WaveformExtractor  	sampleSum -com.simform.audio_waveforms.WaveformExtractor  set -com.simform.audio_waveforms.WaveformExtractor  sqrt -com.simform.audio_waveforms.WaveformExtractor  startDecode -com.simform.audio_waveforms.WaveformExtractor  started -com.simform.audio_waveforms.WaveformExtractor  stop -com.simform.audio_waveforms.WaveformExtractor  totalSamples -com.simform.audio_waveforms.WaveformExtractor  getCHANNELS Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getChannels Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getDURATIONMillis Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getDurationMillis Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getEXPECTEDPoints Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getEXTRACTOR Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getExpectedPoints Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getExtractor Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getFINISHCount Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getFinishCount Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getHandle16bit Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getHandle32bit Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  
getHandle8bit Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getINPUTEof Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getISEof Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getInputEof Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getIsEof Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getLET Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getLet Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getPCMEncodingBit Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getPERSamplePoints Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getPcmEncodingBit Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getPerSamplePoints Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  	getRESULT Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  	getResult Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  
getSAMPLERate Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getSTOP Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  
getSampleRate Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getStop Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getTOTALSamples Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  getTotalSamples Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  isEof Xcom.simform.audio_waveforms.WaveformExtractor.startDecode.<anonymous>.<no name provided>  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  equals Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getACTIVITY Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  &removeRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  setActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  invokeMethod &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  File java.io  IOException java.io  createTempFile java.io.File  getPATH java.io.File  getPath java.io.File  path java.io.File  setPath java.io.File  ActivityCompat 	java.lang  	ArrayList 	java.lang  AudioFormat 	java.lang  AudioPlayer 	java.lang  
AudioRecorder 	java.lang  Build 	java.lang  	Constants 	java.lang  CountDownLatch 	java.lang  Date 	java.lang  Double 	java.lang  DurationType 	java.lang  	Exception 	java.lang  	ExoPlayer 	java.lang  File 	java.lang  
FinishMode 	java.lang  Handler 	java.lang  HashMap 	java.lang  LOG_TAG 	java.lang  Locale 	java.lang  Log 	java.lang  Looper 	java.lang  METADATA_KEY_DURATION 	java.lang  Manifest 	java.lang  
MediaCodec 	java.lang  MediaExtractor 	java.lang  MediaFormat 	java.lang  	MediaItem 	java.lang  MediaMetadataRetriever 	java.lang  
MediaRecorder 	java.lang  
MethodChannel 	java.lang  PackageManager 	java.lang  Player 	java.lang  RECORD_AUDIO_REQUEST_CODE 	java.lang  RecorderSettings 	java.lang  Runnable 	java.lang  SimpleDateFormat 	java.lang  Uri 	java.lang  WaveformExtractor 	java.lang  also 	java.lang  apply 	java.lang  arrayOf 	java.lang  channels 	java.lang  
component1 	java.lang  
component2 	java.lang  contains 	java.lang  durationMillis 	java.lang  error 	java.lang  expectedPoints 	java.lang  	extractor 	java.lang  
extractors 	java.lang  finishCount 	java.lang  
finishMode 	java.lang  get 	java.lang  
getEncoder 	java.lang  getOutputFormat 	java.lang  handle16bit 	java.lang  handle32bit 	java.lang  
handle8bit 	java.lang  handler 	java.lang  inputEof 	java.lang  isEof 	java.lang  
isNotEmpty 	java.lang  isPlayerPrepared 	java.lang  iterator 	java.lang  key 	java.lang  let 	java.lang  log10 	java.lang  
methodChannel 	java.lang  mutableMapOf 	java.lang  pcmEncodingBit 	java.lang  perSamplePoints 	java.lang  player 	java.lang  
plusAssign 	java.lang  pow 	java.lang  repeat 	java.lang  result 	java.lang  
sampleRate 	java.lang  sendCurrentDuration 	java.lang  set 	java.lang  sqrt 	java.lang  stop 	java.lang  
stopListening 	java.lang  toInt 	java.lang  totalSamples 	java.lang  updateFrequency 	java.lang  message java.lang.Exception  toString java.lang.Exception  getLET java.lang.Runnable  getLet java.lang.Runnable  let java.lang.Runnable  Buffer java.nio  
ByteBuffer java.nio  get java.nio.Buffer  let java.nio.Buffer  position java.nio.Buffer  get java.nio.ByteBuffer  getLET java.nio.ByteBuffer  getLet java.nio.ByteBuffer  let java.nio.ByteBuffer  position java.nio.ByteBuffer  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  	ArrayList 	java.util  Date 	java.util  HashMap 	java.util  Locale 	java.util  add java.util.AbstractCollection  add java.util.AbstractList  set java.util.AbstractMap  add java.util.ArrayList  getSET java.util.HashMap  getSet java.util.HashMap  set java.util.HashMap  US java.util.Locale  CountDownLatch java.util.concurrent  	countDown #java.util.concurrent.CountDownLatch  ActivityCompat kotlin  Any kotlin  Array kotlin  	ArrayList kotlin  AudioFormat kotlin  AudioPlayer kotlin  
AudioRecorder kotlin  Boolean kotlin  Build kotlin  Byte kotlin  	Constants kotlin  CountDownLatch kotlin  Date kotlin  Double kotlin  DurationType kotlin  	Exception kotlin  	ExoPlayer kotlin  File kotlin  
FinishMode kotlin  Float kotlin  	Function1 kotlin  Handler kotlin  HashMap kotlin  IllegalStateException kotlin  Int kotlin  IntArray kotlin  LOG_TAG kotlin  Locale kotlin  Log kotlin  Long kotlin  Looper kotlin  METADATA_KEY_DURATION kotlin  Manifest kotlin  
MediaCodec kotlin  MediaExtractor kotlin  MediaFormat kotlin  	MediaItem kotlin  MediaMetadataRetriever kotlin  
MediaRecorder kotlin  
MethodChannel kotlin  Nothing kotlin  PackageManager kotlin  Player kotlin  RECORD_AUDIO_REQUEST_CODE kotlin  RecorderSettings kotlin  Runnable kotlin  RuntimeException kotlin  SimpleDateFormat kotlin  String kotlin  Suppress kotlin  Unit kotlin  Uri kotlin  Volatile kotlin  WaveformExtractor kotlin  also kotlin  apply kotlin  arrayOf kotlin  channels kotlin  
component1 kotlin  
component2 kotlin  contains kotlin  durationMillis kotlin  error kotlin  expectedPoints kotlin  	extractor kotlin  
extractors kotlin  finishCount kotlin  
finishMode kotlin  get kotlin  
getEncoder kotlin  getOutputFormat kotlin  handle16bit kotlin  handle32bit kotlin  
handle8bit kotlin  handler kotlin  inputEof kotlin  isEof kotlin  
isNotEmpty kotlin  isPlayerPrepared kotlin  iterator kotlin  key kotlin  let kotlin  log10 kotlin  
methodChannel kotlin  mutableMapOf kotlin  pcmEncodingBit kotlin  perSamplePoints kotlin  player kotlin  
plusAssign kotlin  pow kotlin  repeat kotlin  result kotlin  
sampleRate kotlin  sendCurrentDuration kotlin  set kotlin  sqrt kotlin  stop kotlin  
stopListening kotlin  toInt kotlin  totalSamples kotlin  updateFrequency kotlin  
getPLUSAssign 
kotlin.Double  getPOW 
kotlin.Double  
getPlusAssign 
kotlin.Double  getPow 
kotlin.Double  getLET 
kotlin.Int  getLet 
kotlin.Int  
getISNotEmpty kotlin.IntArray  
getIsNotEmpty kotlin.IntArray  
isNotEmpty kotlin.IntArray  getLET kotlin.Long  getLet kotlin.Long  getCONTAINS 
kotlin.String  getContains 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  getTOInt 
kotlin.String  getToInt 
kotlin.String  ActivityCompat kotlin.annotation  	ArrayList kotlin.annotation  AudioFormat kotlin.annotation  AudioPlayer kotlin.annotation  
AudioRecorder kotlin.annotation  Build kotlin.annotation  	Constants kotlin.annotation  CountDownLatch kotlin.annotation  Date kotlin.annotation  Double kotlin.annotation  DurationType kotlin.annotation  	Exception kotlin.annotation  	ExoPlayer kotlin.annotation  File kotlin.annotation  
FinishMode kotlin.annotation  Handler kotlin.annotation  HashMap kotlin.annotation  IllegalStateException kotlin.annotation  LOG_TAG kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  METADATA_KEY_DURATION kotlin.annotation  Manifest kotlin.annotation  
MediaCodec kotlin.annotation  MediaExtractor kotlin.annotation  MediaFormat kotlin.annotation  	MediaItem kotlin.annotation  MediaMetadataRetriever kotlin.annotation  
MediaRecorder kotlin.annotation  
MethodChannel kotlin.annotation  PackageManager kotlin.annotation  Player kotlin.annotation  RECORD_AUDIO_REQUEST_CODE kotlin.annotation  RecorderSettings kotlin.annotation  Runnable kotlin.annotation  RuntimeException kotlin.annotation  SimpleDateFormat kotlin.annotation  Uri kotlin.annotation  Volatile kotlin.annotation  WaveformExtractor kotlin.annotation  also kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  channels kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  contains kotlin.annotation  durationMillis kotlin.annotation  error kotlin.annotation  expectedPoints kotlin.annotation  	extractor kotlin.annotation  
extractors kotlin.annotation  finishCount kotlin.annotation  
finishMode kotlin.annotation  get kotlin.annotation  
getEncoder kotlin.annotation  getOutputFormat kotlin.annotation  handle16bit kotlin.annotation  handle32bit kotlin.annotation  
handle8bit kotlin.annotation  handler kotlin.annotation  inputEof kotlin.annotation  isEof kotlin.annotation  
isNotEmpty kotlin.annotation  isPlayerPrepared kotlin.annotation  iterator kotlin.annotation  key kotlin.annotation  let kotlin.annotation  log10 kotlin.annotation  
methodChannel kotlin.annotation  mutableMapOf kotlin.annotation  pcmEncodingBit kotlin.annotation  perSamplePoints kotlin.annotation  player kotlin.annotation  
plusAssign kotlin.annotation  pow kotlin.annotation  repeat kotlin.annotation  result kotlin.annotation  
sampleRate kotlin.annotation  sendCurrentDuration kotlin.annotation  set kotlin.annotation  sqrt kotlin.annotation  stop kotlin.annotation  
stopListening kotlin.annotation  toInt kotlin.annotation  totalSamples kotlin.annotation  updateFrequency kotlin.annotation  ActivityCompat kotlin.collections  	ArrayList kotlin.collections  AudioFormat kotlin.collections  AudioPlayer kotlin.collections  
AudioRecorder kotlin.collections  Build kotlin.collections  	Constants kotlin.collections  CountDownLatch kotlin.collections  Date kotlin.collections  Double kotlin.collections  DurationType kotlin.collections  	Exception kotlin.collections  	ExoPlayer kotlin.collections  File kotlin.collections  
FinishMode kotlin.collections  Handler kotlin.collections  HashMap kotlin.collections  IllegalStateException kotlin.collections  LOG_TAG kotlin.collections  Locale kotlin.collections  Log kotlin.collections  Looper kotlin.collections  METADATA_KEY_DURATION kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  
MediaCodec kotlin.collections  MediaExtractor kotlin.collections  MediaFormat kotlin.collections  	MediaItem kotlin.collections  MediaMetadataRetriever kotlin.collections  
MediaRecorder kotlin.collections  
MethodChannel kotlin.collections  
MutableMap kotlin.collections  PackageManager kotlin.collections  Player kotlin.collections  RECORD_AUDIO_REQUEST_CODE kotlin.collections  RecorderSettings kotlin.collections  Runnable kotlin.collections  RuntimeException kotlin.collections  SimpleDateFormat kotlin.collections  Uri kotlin.collections  Volatile kotlin.collections  WaveformExtractor kotlin.collections  also kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  channels kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  durationMillis kotlin.collections  error kotlin.collections  expectedPoints kotlin.collections  	extractor kotlin.collections  
extractors kotlin.collections  finishCount kotlin.collections  
finishMode kotlin.collections  get kotlin.collections  
getEncoder kotlin.collections  getOutputFormat kotlin.collections  handle16bit kotlin.collections  handle32bit kotlin.collections  
handle8bit kotlin.collections  handler kotlin.collections  inputEof kotlin.collections  isEof kotlin.collections  
isNotEmpty kotlin.collections  isPlayerPrepared kotlin.collections  iterator kotlin.collections  key kotlin.collections  let kotlin.collections  log10 kotlin.collections  
methodChannel kotlin.collections  mutableIterator kotlin.collections  mutableMapOf kotlin.collections  pcmEncodingBit kotlin.collections  perSamplePoints kotlin.collections  player kotlin.collections  
plusAssign kotlin.collections  pow kotlin.collections  repeat kotlin.collections  result kotlin.collections  
sampleRate kotlin.collections  sendCurrentDuration kotlin.collections  set kotlin.collections  sqrt kotlin.collections  stop kotlin.collections  
stopListening kotlin.collections  toInt kotlin.collections  totalSamples kotlin.collections  updateFrequency kotlin.collections  getGET kotlin.collections.MutableMap  getGet kotlin.collections.MutableMap  getITERATOR kotlin.collections.MutableMap  getIterator kotlin.collections.MutableMap  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  
getComponent1 *kotlin.collections.MutableMap.MutableEntry  
getComponent2 *kotlin.collections.MutableMap.MutableEntry  ActivityCompat kotlin.comparisons  	ArrayList kotlin.comparisons  AudioFormat kotlin.comparisons  AudioPlayer kotlin.comparisons  
AudioRecorder kotlin.comparisons  Build kotlin.comparisons  	Constants kotlin.comparisons  CountDownLatch kotlin.comparisons  Date kotlin.comparisons  Double kotlin.comparisons  DurationType kotlin.comparisons  	Exception kotlin.comparisons  	ExoPlayer kotlin.comparisons  File kotlin.comparisons  
FinishMode kotlin.comparisons  Handler kotlin.comparisons  HashMap kotlin.comparisons  IllegalStateException kotlin.comparisons  LOG_TAG kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  METADATA_KEY_DURATION kotlin.comparisons  Manifest kotlin.comparisons  
MediaCodec kotlin.comparisons  MediaExtractor kotlin.comparisons  MediaFormat kotlin.comparisons  	MediaItem kotlin.comparisons  MediaMetadataRetriever kotlin.comparisons  
MediaRecorder kotlin.comparisons  
MethodChannel kotlin.comparisons  PackageManager kotlin.comparisons  Player kotlin.comparisons  RECORD_AUDIO_REQUEST_CODE kotlin.comparisons  RecorderSettings kotlin.comparisons  Runnable kotlin.comparisons  RuntimeException kotlin.comparisons  SimpleDateFormat kotlin.comparisons  Uri kotlin.comparisons  Volatile kotlin.comparisons  WaveformExtractor kotlin.comparisons  also kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  channels kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  contains kotlin.comparisons  durationMillis kotlin.comparisons  error kotlin.comparisons  expectedPoints kotlin.comparisons  	extractor kotlin.comparisons  
extractors kotlin.comparisons  finishCount kotlin.comparisons  
finishMode kotlin.comparisons  get kotlin.comparisons  
getEncoder kotlin.comparisons  getOutputFormat kotlin.comparisons  handle16bit kotlin.comparisons  handle32bit kotlin.comparisons  
handle8bit kotlin.comparisons  handler kotlin.comparisons  inputEof kotlin.comparisons  isEof kotlin.comparisons  
isNotEmpty kotlin.comparisons  isPlayerPrepared kotlin.comparisons  iterator kotlin.comparisons  key kotlin.comparisons  let kotlin.comparisons  log10 kotlin.comparisons  
methodChannel kotlin.comparisons  mutableMapOf kotlin.comparisons  pcmEncodingBit kotlin.comparisons  perSamplePoints kotlin.comparisons  player kotlin.comparisons  
plusAssign kotlin.comparisons  pow kotlin.comparisons  repeat kotlin.comparisons  result kotlin.comparisons  
sampleRate kotlin.comparisons  sendCurrentDuration kotlin.comparisons  set kotlin.comparisons  sqrt kotlin.comparisons  stop kotlin.comparisons  
stopListening kotlin.comparisons  toInt kotlin.comparisons  totalSamples kotlin.comparisons  updateFrequency kotlin.comparisons  ActivityCompat 	kotlin.io  	ArrayList 	kotlin.io  AudioFormat 	kotlin.io  AudioPlayer 	kotlin.io  
AudioRecorder 	kotlin.io  Build 	kotlin.io  	Constants 	kotlin.io  CountDownLatch 	kotlin.io  Date 	kotlin.io  Double 	kotlin.io  DurationType 	kotlin.io  	Exception 	kotlin.io  	ExoPlayer 	kotlin.io  File 	kotlin.io  
FinishMode 	kotlin.io  Handler 	kotlin.io  HashMap 	kotlin.io  IllegalStateException 	kotlin.io  LOG_TAG 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  METADATA_KEY_DURATION 	kotlin.io  Manifest 	kotlin.io  
MediaCodec 	kotlin.io  MediaExtractor 	kotlin.io  MediaFormat 	kotlin.io  	MediaItem 	kotlin.io  MediaMetadataRetriever 	kotlin.io  
MediaRecorder 	kotlin.io  
MethodChannel 	kotlin.io  PackageManager 	kotlin.io  Player 	kotlin.io  RECORD_AUDIO_REQUEST_CODE 	kotlin.io  RecorderSettings 	kotlin.io  Runnable 	kotlin.io  RuntimeException 	kotlin.io  SimpleDateFormat 	kotlin.io  Uri 	kotlin.io  Volatile 	kotlin.io  WaveformExtractor 	kotlin.io  also 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  channels 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  contains 	kotlin.io  durationMillis 	kotlin.io  error 	kotlin.io  expectedPoints 	kotlin.io  	extractor 	kotlin.io  
extractors 	kotlin.io  finishCount 	kotlin.io  
finishMode 	kotlin.io  get 	kotlin.io  
getEncoder 	kotlin.io  getOutputFormat 	kotlin.io  handle16bit 	kotlin.io  handle32bit 	kotlin.io  
handle8bit 	kotlin.io  handler 	kotlin.io  inputEof 	kotlin.io  isEof 	kotlin.io  
isNotEmpty 	kotlin.io  isPlayerPrepared 	kotlin.io  iterator 	kotlin.io  key 	kotlin.io  let 	kotlin.io  log10 	kotlin.io  
methodChannel 	kotlin.io  mutableMapOf 	kotlin.io  pcmEncodingBit 	kotlin.io  perSamplePoints 	kotlin.io  player 	kotlin.io  
plusAssign 	kotlin.io  pow 	kotlin.io  repeat 	kotlin.io  result 	kotlin.io  
sampleRate 	kotlin.io  sendCurrentDuration 	kotlin.io  set 	kotlin.io  sqrt 	kotlin.io  stop 	kotlin.io  
stopListening 	kotlin.io  toInt 	kotlin.io  totalSamples 	kotlin.io  updateFrequency 	kotlin.io  ActivityCompat 
kotlin.jvm  	ArrayList 
kotlin.jvm  AudioFormat 
kotlin.jvm  AudioPlayer 
kotlin.jvm  
AudioRecorder 
kotlin.jvm  Build 
kotlin.jvm  	Constants 
kotlin.jvm  CountDownLatch 
kotlin.jvm  Date 
kotlin.jvm  Double 
kotlin.jvm  DurationType 
kotlin.jvm  	Exception 
kotlin.jvm  	ExoPlayer 
kotlin.jvm  File 
kotlin.jvm  
FinishMode 
kotlin.jvm  Handler 
kotlin.jvm  HashMap 
kotlin.jvm  IllegalStateException 
kotlin.jvm  LOG_TAG 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  METADATA_KEY_DURATION 
kotlin.jvm  Manifest 
kotlin.jvm  
MediaCodec 
kotlin.jvm  MediaExtractor 
kotlin.jvm  MediaFormat 
kotlin.jvm  	MediaItem 
kotlin.jvm  MediaMetadataRetriever 
kotlin.jvm  
MediaRecorder 
kotlin.jvm  
MethodChannel 
kotlin.jvm  PackageManager 
kotlin.jvm  Player 
kotlin.jvm  RECORD_AUDIO_REQUEST_CODE 
kotlin.jvm  RecorderSettings 
kotlin.jvm  Runnable 
kotlin.jvm  RuntimeException 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  Uri 
kotlin.jvm  Volatile 
kotlin.jvm  WaveformExtractor 
kotlin.jvm  also 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  channels 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  contains 
kotlin.jvm  durationMillis 
kotlin.jvm  error 
kotlin.jvm  expectedPoints 
kotlin.jvm  	extractor 
kotlin.jvm  
extractors 
kotlin.jvm  finishCount 
kotlin.jvm  
finishMode 
kotlin.jvm  get 
kotlin.jvm  
getEncoder 
kotlin.jvm  getOutputFormat 
kotlin.jvm  handle16bit 
kotlin.jvm  handle32bit 
kotlin.jvm  
handle8bit 
kotlin.jvm  handler 
kotlin.jvm  inputEof 
kotlin.jvm  isEof 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  isPlayerPrepared 
kotlin.jvm  iterator 
kotlin.jvm  key 
kotlin.jvm  let 
kotlin.jvm  log10 
kotlin.jvm  
methodChannel 
kotlin.jvm  mutableMapOf 
kotlin.jvm  pcmEncodingBit 
kotlin.jvm  perSamplePoints 
kotlin.jvm  player 
kotlin.jvm  
plusAssign 
kotlin.jvm  pow 
kotlin.jvm  repeat 
kotlin.jvm  result 
kotlin.jvm  
sampleRate 
kotlin.jvm  sendCurrentDuration 
kotlin.jvm  set 
kotlin.jvm  sqrt 
kotlin.jvm  stop 
kotlin.jvm  
stopListening 
kotlin.jvm  toInt 
kotlin.jvm  totalSamples 
kotlin.jvm  updateFrequency 
kotlin.jvm  log10 kotlin.math  pow kotlin.math  sqrt kotlin.math  ActivityCompat 
kotlin.ranges  	ArrayList 
kotlin.ranges  AudioFormat 
kotlin.ranges  AudioPlayer 
kotlin.ranges  
AudioRecorder 
kotlin.ranges  Build 
kotlin.ranges  	Constants 
kotlin.ranges  CountDownLatch 
kotlin.ranges  Date 
kotlin.ranges  Double 
kotlin.ranges  DurationType 
kotlin.ranges  	Exception 
kotlin.ranges  	ExoPlayer 
kotlin.ranges  File 
kotlin.ranges  
FinishMode 
kotlin.ranges  Handler 
kotlin.ranges  HashMap 
kotlin.ranges  IllegalStateException 
kotlin.ranges  LOG_TAG 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  METADATA_KEY_DURATION 
kotlin.ranges  Manifest 
kotlin.ranges  
MediaCodec 
kotlin.ranges  MediaExtractor 
kotlin.ranges  MediaFormat 
kotlin.ranges  	MediaItem 
kotlin.ranges  MediaMetadataRetriever 
kotlin.ranges  
MediaRecorder 
kotlin.ranges  
MethodChannel 
kotlin.ranges  PackageManager 
kotlin.ranges  Player 
kotlin.ranges  RECORD_AUDIO_REQUEST_CODE 
kotlin.ranges  RecorderSettings 
kotlin.ranges  Runnable 
kotlin.ranges  RuntimeException 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  Uri 
kotlin.ranges  Volatile 
kotlin.ranges  WaveformExtractor 
kotlin.ranges  also 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  channels 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  contains 
kotlin.ranges  durationMillis 
kotlin.ranges  error 
kotlin.ranges  expectedPoints 
kotlin.ranges  	extractor 
kotlin.ranges  
extractors 
kotlin.ranges  finishCount 
kotlin.ranges  
finishMode 
kotlin.ranges  get 
kotlin.ranges  
getEncoder 
kotlin.ranges  getOutputFormat 
kotlin.ranges  handle16bit 
kotlin.ranges  handle32bit 
kotlin.ranges  
handle8bit 
kotlin.ranges  handler 
kotlin.ranges  inputEof 
kotlin.ranges  isEof 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  isPlayerPrepared 
kotlin.ranges  iterator 
kotlin.ranges  key 
kotlin.ranges  let 
kotlin.ranges  log10 
kotlin.ranges  
methodChannel 
kotlin.ranges  mutableMapOf 
kotlin.ranges  pcmEncodingBit 
kotlin.ranges  perSamplePoints 
kotlin.ranges  player 
kotlin.ranges  
plusAssign 
kotlin.ranges  pow 
kotlin.ranges  repeat 
kotlin.ranges  result 
kotlin.ranges  
sampleRate 
kotlin.ranges  sendCurrentDuration 
kotlin.ranges  set 
kotlin.ranges  sqrt 
kotlin.ranges  stop 
kotlin.ranges  
stopListening 
kotlin.ranges  toInt 
kotlin.ranges  totalSamples 
kotlin.ranges  updateFrequency 
kotlin.ranges  ActivityCompat kotlin.sequences  	ArrayList kotlin.sequences  AudioFormat kotlin.sequences  AudioPlayer kotlin.sequences  
AudioRecorder kotlin.sequences  Build kotlin.sequences  	Constants kotlin.sequences  CountDownLatch kotlin.sequences  Date kotlin.sequences  Double kotlin.sequences  DurationType kotlin.sequences  	Exception kotlin.sequences  	ExoPlayer kotlin.sequences  File kotlin.sequences  
FinishMode kotlin.sequences  Handler kotlin.sequences  HashMap kotlin.sequences  IllegalStateException kotlin.sequences  LOG_TAG kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  METADATA_KEY_DURATION kotlin.sequences  Manifest kotlin.sequences  
MediaCodec kotlin.sequences  MediaExtractor kotlin.sequences  MediaFormat kotlin.sequences  	MediaItem kotlin.sequences  MediaMetadataRetriever kotlin.sequences  
MediaRecorder kotlin.sequences  
MethodChannel kotlin.sequences  PackageManager kotlin.sequences  Player kotlin.sequences  RECORD_AUDIO_REQUEST_CODE kotlin.sequences  RecorderSettings kotlin.sequences  Runnable kotlin.sequences  RuntimeException kotlin.sequences  SimpleDateFormat kotlin.sequences  Uri kotlin.sequences  Volatile kotlin.sequences  WaveformExtractor kotlin.sequences  also kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  channels kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  contains kotlin.sequences  durationMillis kotlin.sequences  error kotlin.sequences  expectedPoints kotlin.sequences  	extractor kotlin.sequences  
extractors kotlin.sequences  finishCount kotlin.sequences  
finishMode kotlin.sequences  get kotlin.sequences  
getEncoder kotlin.sequences  getOutputFormat kotlin.sequences  handle16bit kotlin.sequences  handle32bit kotlin.sequences  
handle8bit kotlin.sequences  handler kotlin.sequences  inputEof kotlin.sequences  isEof kotlin.sequences  
isNotEmpty kotlin.sequences  isPlayerPrepared kotlin.sequences  iterator kotlin.sequences  key kotlin.sequences  let kotlin.sequences  log10 kotlin.sequences  
methodChannel kotlin.sequences  mutableMapOf kotlin.sequences  pcmEncodingBit kotlin.sequences  perSamplePoints kotlin.sequences  player kotlin.sequences  
plusAssign kotlin.sequences  pow kotlin.sequences  repeat kotlin.sequences  result kotlin.sequences  
sampleRate kotlin.sequences  sendCurrentDuration kotlin.sequences  set kotlin.sequences  sqrt kotlin.sequences  stop kotlin.sequences  
stopListening kotlin.sequences  toInt kotlin.sequences  totalSamples kotlin.sequences  updateFrequency kotlin.sequences  ActivityCompat kotlin.text  	ArrayList kotlin.text  AudioFormat kotlin.text  AudioPlayer kotlin.text  
AudioRecorder kotlin.text  Build kotlin.text  	Constants kotlin.text  CountDownLatch kotlin.text  Date kotlin.text  Double kotlin.text  DurationType kotlin.text  	Exception kotlin.text  	ExoPlayer kotlin.text  File kotlin.text  
FinishMode kotlin.text  Handler kotlin.text  HashMap kotlin.text  IllegalStateException kotlin.text  LOG_TAG kotlin.text  Locale kotlin.text  Log kotlin.text  Looper kotlin.text  METADATA_KEY_DURATION kotlin.text  Manifest kotlin.text  
MediaCodec kotlin.text  MediaExtractor kotlin.text  MediaFormat kotlin.text  	MediaItem kotlin.text  MediaMetadataRetriever kotlin.text  
MediaRecorder kotlin.text  
MethodChannel kotlin.text  PackageManager kotlin.text  Player kotlin.text  RECORD_AUDIO_REQUEST_CODE kotlin.text  RecorderSettings kotlin.text  Runnable kotlin.text  RuntimeException kotlin.text  SimpleDateFormat kotlin.text  Uri kotlin.text  Volatile kotlin.text  WaveformExtractor kotlin.text  also kotlin.text  apply kotlin.text  arrayOf kotlin.text  channels kotlin.text  
component1 kotlin.text  
component2 kotlin.text  contains kotlin.text  durationMillis kotlin.text  error kotlin.text  expectedPoints kotlin.text  	extractor kotlin.text  
extractors kotlin.text  finishCount kotlin.text  
finishMode kotlin.text  get kotlin.text  
getEncoder kotlin.text  getOutputFormat kotlin.text  handle16bit kotlin.text  handle32bit kotlin.text  
handle8bit kotlin.text  handler kotlin.text  inputEof kotlin.text  isEof kotlin.text  
isNotEmpty kotlin.text  isPlayerPrepared kotlin.text  iterator kotlin.text  key kotlin.text  let kotlin.text  log10 kotlin.text  
methodChannel kotlin.text  mutableMapOf kotlin.text  pcmEncodingBit kotlin.text  perSamplePoints kotlin.text  player kotlin.text  
plusAssign kotlin.text  pow kotlin.text  repeat kotlin.text  result kotlin.text  
sampleRate kotlin.text  sendCurrentDuration kotlin.text  set kotlin.text  sqrt kotlin.text  stop kotlin.text  
stopListening kotlin.text  toInt kotlin.text  totalSamples kotlin.text  updateFrequency kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
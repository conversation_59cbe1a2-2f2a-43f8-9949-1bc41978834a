{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,172,232,288,360,419,501,581", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "109,167,227,283,355,414,496,576,639"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5558,5617,5675,5735,5791,5863,5922,6004,6084", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "5612,5670,5730,5786,5858,5917,5999,6079,6142"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3242,3334,3434,3528,3625,3721,3819,7278", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3329,3429,3523,3620,3716,3814,3914,7374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,612,686,759,830,911,989,1048,1109,1186,1262,1326,1387,1446,1511,1581,1651,1725,1789,1858,1923,1981,2055,2125,2186,2251,2304,2361,2407,2466,2522,2584,2641,2701,2757,2813,2877,2940,3004,3054,3110,3180,3250", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,69,69,73,63,68,64,57,73,69,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "282,449,607,681,754,825,906,984,1043,1104,1181,1257,1321,1382,1441,1506,1576,1646,1720,1784,1853,1918,1976,2050,2120,2181,2246,2299,2356,2402,2461,2517,2579,2636,2696,2752,2808,2872,2935,2999,3049,3105,3175,3245,3298"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,504,3919,3993,4066,4137,4218,4296,4355,4416,4493,4569,4633,4694,4753,4818,4888,4958,5032,5096,5165,5230,5288,5362,5432,5493,6147,6200,6257,6303,6362,6418,6480,6537,6597,6653,6709,6773,6836,6900,6950,7006,7076,7146", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,69,69,73,63,68,64,57,73,69,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "332,499,657,3988,4061,4132,4213,4291,4350,4411,4488,4564,4628,4689,4748,4813,4883,4953,5027,5091,5160,5225,5283,5357,5427,5488,5553,6195,6252,6298,6357,6413,6475,6532,6592,6648,6704,6768,6831,6895,6945,7001,7071,7141,7194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "662,759,853,954,1036,1134,1240,1320,1395,1486,1579,1674,1768,1868,1961,2056,2150,2241,2332,2412,2510,2604,2699,2799,2896,2996,3148,7199", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "754,848,949,1031,1129,1235,1315,1390,1481,1574,1669,1763,1863,1956,2051,2145,2236,2327,2407,2505,2599,2694,2794,2891,2991,3143,3237,7273"}}]}]}
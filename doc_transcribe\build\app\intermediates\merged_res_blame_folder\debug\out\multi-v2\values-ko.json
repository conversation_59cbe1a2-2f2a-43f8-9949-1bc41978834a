{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,172,232,288,360,419,501,581", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "109,167,227,283,355,414,496,576,639"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2928,2987,3045,3105,3161,3233,3292,3374,3454", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "2982,3040,3100,3156,3228,3287,3369,3449,3512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "612,704,804,898,995,1091,1189,4569", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "699,799,893,990,1086,1184,1284,4665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,612,686,759,830,911,989,1048,1109,1186,1262,1326,1387,1446,1511,1581,1651,1725,1789,1858,1923,1981,2055,2125,2186,2251,2304,2361,2407,2466,2522,2584,2641,2701,2757,2813,2877,2940,3004,3054,3110,3180,3250", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,69,69,73,63,68,64,57,73,69,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "282,449,607,681,754,825,906,984,1043,1104,1181,1257,1321,1382,1441,1506,1576,1646,1720,1784,1853,1918,1976,2050,2120,2181,2246,2299,2356,2402,2461,2517,2579,2636,2696,2752,2808,2872,2935,2999,3049,3105,3175,3245,3298"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,1289,1363,1436,1507,1588,1666,1725,1786,1863,1939,2003,2064,2123,2188,2258,2328,2402,2466,2535,2600,2658,2732,2802,2863,3517,3570,3627,3673,3732,3788,3850,3907,3967,4023,4079,4143,4206,4270,4320,4376,4446,4516", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,69,69,73,63,68,64,57,73,69,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "282,449,607,1358,1431,1502,1583,1661,1720,1781,1858,1934,1998,2059,2118,2183,2253,2323,2397,2461,2530,2595,2653,2727,2797,2858,2923,3565,3622,3668,3727,3783,3845,3902,3962,4018,4074,4138,4201,4265,4315,4371,4441,4511,4564"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "48,49,50,51,52,53,54,107", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3598,3696,3798,3895,3999,4103,4208,8097", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3691,3793,3890,3994,4098,4203,4319,8193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,899,982,1064,1153,1244,1314,1381,1475,1570,1638,1702,1765,1837,1912,1997,2074,2150,2238,2312,2383,2481,2577,2644,2709,2762,2820,2868,2929,2995,3059,3122,3187,3251,3312,3378,3443,3509,3561,3623,3699,3775", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,74,84,76,75,87,73,70,97,95,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,894,977,1059,1148,1239,1309,1376,1470,1565,1633,1697,1760,1832,1907,1992,2069,2145,2233,2307,2378,2476,2572,2639,2704,2757,2815,2863,2924,2990,3054,3117,3182,3246,3307,3373,3438,3504,3556,3618,3694,3770,3826"}, "to": {"startLines": "2,11,16,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,606,4324,4406,4489,4571,4660,4751,4821,4888,4982,5077,5145,5209,5272,5344,5419,5504,5581,5657,5745,5819,5890,5988,6084,6151,6888,6941,6999,7047,7108,7174,7238,7301,7366,7430,7491,7557,7622,7688,7740,7802,7878,7954", "endLines": "10,15,20,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,74,84,76,75,87,73,70,97,95,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "332,601,862,4401,4484,4566,4655,4746,4816,4883,4977,5072,5140,5204,5267,5339,5414,5499,5576,5652,5740,5814,5885,5983,6079,6146,6211,6936,6994,7042,7103,7169,7233,7296,7361,7425,7486,7552,7617,7683,7735,7797,7873,7949,8005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6216,6291,6352,6417,6489,6568,6641,6729,6813", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "6286,6347,6412,6484,6563,6636,6724,6808,6883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "867,974,1075,1181,1267,1371,1493,1578,1660,1751,1844,1939,2033,2133,2226,2321,2426,2517,2608,2694,2799,2905,3008,3115,3224,3331,3501,8010", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "969,1070,1176,1262,1366,1488,1573,1655,1746,1839,1934,2028,2128,2221,2316,2421,2512,2603,2689,2794,2900,3003,3110,3219,3326,3496,3593,8092"}}]}]}
{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-32:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,652,741,834,909,994,1080,1155,1221,1306,1392,1460,1522,1582,1651,1731,1816,1896,1965,2049,2119,2195,2278,2358,2423,2487,2540,2598,2646,2707,2771,2838,2900,2966,3028,3085,3149,3214,3280,3332,3392,3466,3540", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,79,84,79,68,83,69,75,82,79,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "280,467,647,736,829,904,989,1075,1150,1216,1301,1387,1455,1517,1577,1646,1726,1811,1891,1960,2044,2114,2190,2273,2353,2418,2482,2535,2593,2641,2702,2766,2833,2895,2961,3023,3080,3144,3209,3275,3327,3387,3461,3535,3592"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,522,4107,4196,4289,4364,4449,4535,4610,4676,4761,4847,4915,4977,5037,5106,5186,5271,5351,5420,5504,5574,5650,5733,5813,5878,6618,6671,6729,6777,6838,6902,6969,7031,7097,7159,7216,7280,7345,7411,7463,7523,7597,7671", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,79,84,79,68,83,69,75,82,79,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "330,517,697,4191,4284,4359,4444,4530,4605,4671,4756,4842,4910,4972,5032,5101,5181,5266,5346,5415,5499,5569,5645,5728,5808,5873,5937,6666,6724,6772,6833,6897,6964,7026,7092,7154,7211,7275,7340,7406,7458,7518,7592,7666,7723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5942,6019,6078,6143,6204,6284,6356,6446,6542", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "6014,6073,6138,6199,6279,6351,6441,6537,6613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d4fa9196a46da0fe30cf290869c0497\\transformed\\appcompat-1.2.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "702,816,915,1027,1112,1218,1338,1418,1493,1584,1677,1769,1863,1963,2056,2158,2253,2344,2435,2514,2621,2725,2821,2928,3031,3140,3296,7728", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "811,910,1022,1107,1213,1333,1413,1488,1579,1672,1764,1858,1958,2051,2153,2248,2339,2430,2509,2616,2720,2816,2923,3026,3135,3291,3389,7803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3394,3491,3593,3691,3788,3890,3996,7808", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3486,3588,3686,3783,3885,3991,4102,7904"}}]}]}
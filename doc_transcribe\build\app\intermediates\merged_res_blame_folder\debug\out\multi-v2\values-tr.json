{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,652,741,834,909,994,1080,1155,1221,1306,1392,1460,1522,1582,1651,1731,1816,1896,1965,2049,2119,2195,2278,2358,2423,2487,2540,2598,2646,2707,2771,2838,2900,2966,3028,3085,3149,3214,3280,3332,3392,3466,3540", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,79,84,79,68,83,69,75,82,79,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "280,467,647,736,829,904,989,1075,1150,1216,1301,1387,1455,1517,1577,1646,1726,1811,1891,1960,2044,2114,2190,2273,2353,2418,2482,2535,2593,2641,2702,2766,2833,2895,2961,3023,3080,3144,3209,3275,3327,3387,3461,3535,3592"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,1365,1454,1547,1622,1707,1793,1868,1934,2019,2105,2173,2235,2295,2364,2444,2529,2609,2678,2762,2832,2908,2991,3071,3136,3876,3929,3987,4035,4096,4160,4227,4289,4355,4417,4474,4538,4603,4669,4721,4781,4855,4929", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,79,84,79,68,83,69,75,82,79,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "280,467,647,1449,1542,1617,1702,1788,1863,1929,2014,2100,2168,2230,2290,2359,2439,2524,2604,2673,2757,2827,2903,2986,3066,3131,3195,3924,3982,4030,4091,4155,4222,4284,4350,4412,4469,4533,4598,4664,4716,4776,4850,4924,4981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3200,3277,3336,3401,3462,3542,3614,3704,3800", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "3272,3331,3396,3457,3537,3609,3699,3795,3871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "652,749,851,949,1046,1148,1254,4986", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "744,846,944,1041,1143,1249,1360,5082"}}]}]}
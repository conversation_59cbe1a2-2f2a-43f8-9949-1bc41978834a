{"logs": [{"outputFile": "com.example.doc_transcribe.app-mergeDebugResources-29:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84429f70a7457aa890ea440bb20b385\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "19,20,21,22,23,24,25,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "602,694,793,887,981,1074,1167,4466", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "689,788,882,976,1069,1162,1258,4562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c14396579847727929a6847b59e764b2\\transformed\\jetified-exoplayer-core-2.17.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2871,2927,2983,3041,3094,3166,3220,3294,3370", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "2922,2978,3036,3089,3161,3215,3289,3365,3424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc4c0aa845d72fda0dd7b9b816c58c3c\\transformed\\jetified-exoplayer-ui-2.17.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,602,672,741,811,887,962,1017,1078,1152,1226,1288,1349,1408,1473,1544,1614,1687,1750,1817,1882,1937,2012,2086,2147,2210,2262,2320,2367,2428,2484,2546,2603,2663,2719,2774,2837,2899,2962,3011,3064,3131,3198", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,70,69,72,62,66,64,54,74,73,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "276,439,597,667,736,806,882,957,1012,1073,1147,1221,1283,1344,1403,1468,1539,1609,1682,1745,1812,1877,1932,2007,2081,2142,2205,2257,2315,2362,2423,2479,2541,2598,2658,2714,2769,2832,2894,2957,3006,3059,3126,3193,3242"}, "to": {"startLines": "2,11,15,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,1263,1333,1402,1472,1548,1623,1678,1739,1813,1887,1949,2010,2069,2134,2205,2275,2348,2411,2478,2543,2598,2673,2747,2808,3429,3481,3539,3586,3647,3703,3765,3822,3882,3938,3993,4056,4118,4181,4230,4283,4350,4417", "endLines": "10,14,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,70,69,72,62,66,64,54,74,73,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "276,439,597,1328,1397,1467,1543,1618,1673,1734,1808,1882,1944,2005,2064,2129,2200,2270,2343,2406,2473,2538,2593,2668,2742,2803,2866,3476,3534,3581,3642,3698,3760,3817,3877,3933,3988,4051,4113,4176,4225,4278,4345,4412,4461"}}]}]}
["C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/record_web/assets/js/record.worklet.js", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/record_web/assets/js/record.fixwebmduration.js", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z"]